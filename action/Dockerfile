FROM ubuntu:22.04

# Prevent interactive prompts
ENV DEBIAN_FRONTEND=noninteractive

# Install basic utilities, Node.js 20, Java 17, and system dependencies for Playwright
RUN apt-get update && \
    apt-get install -y \
        curl \
        wget \
        git \
        lsof \
        xvfb \
        ca-certificates \
        gnupg \
        software-properties-common \
        fonts-freefont-ttf \
        fonts-liberation \
        fonts-ipafont-gothic \
        fonts-noto-color-emoji \
        fonts-tlwg-loma-otf \
        fonts-unifont \
        fonts-wqy-zenhei \
        xfonts-scalable \
        chromium-browser \
        openjdk-17-jre-headless && \
    # Install Node.js 20.x from NodeSource
    curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get install -y nodejs && \
    # Clean up
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install Firebase CLI globally
RUN npm install -g firebase-tools

# Set up working directory
WORKDIR /github/workspace

# Environment variable: skip browser download if you want to use system Chromium
ENV PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1

# Default command (override in act)
CMD ["bash"]
