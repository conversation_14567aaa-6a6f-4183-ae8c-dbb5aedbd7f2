import {useState} from 'react';

interface ShowMoreListProps<T> {
    items: T[];
    renderItem: (item: T, index: number) => React.ReactNode;
    initialCount?: number;
    className?: string;
    showMoreLabel?: string;
    showLessLabel?: string;
}

export function ShowMoreList<T>({items, renderItem, initialCount = 8, className = '', showMoreLabel = 'Show More', showLessLabel = 'Show Less'}: ShowMoreListProps<T>) {
    const [showAll, setShowAll] = useState(false);
    const displayed = showAll ? items : items.slice(0, initialCount);
    
    return (
        <div className={className}>
            <div className="space-y-0.5">{displayed.map(renderItem)}</div>
            {items.length > initialCount && (
<div className="mt-6 text-center">
                <button
                        onClick={() => setShowAll((v) => !v)}
                        className="text-elegant-text/60 hover:text-elegant-accent font-montserrat text-sm transition-all duration-200 border border-elegant/20 hover:border-elegant-accent px-4 py-2 rounded-sm"
                    >
                    {showAll ? showLessLabel : `${showMoreLabel} (${items.length - initialCount})`}
                    <span className="ml-2 transition-transform duration-200 inline-block">
                      {showAll ? '↑' : '↓'}
                    </span>
                </button>
            </div>
        )}
        </div>
    );
}
