"use client";

import {useEffect, useState, useRef} from 'react';
import {useAdminContent} from '@/hooks/use-admin-content';
import {useImageResolver} from '@/hooks/use-image-resolver';
import {useToast} from '@/hooks/use-toast';
import {Event, EventsContent} from '@/types/events';
import AdminFormLayout from '@/components/admin/AdminFormLayout';
import {FormField} from '@/components/ui/form-field';
import {EnhancedImageField} from '@/components/admin';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {Textarea} from '@/components/ui/textarea';
import PreviewPanel from '@/components/admin/PreviewPanel';
import PreviewToggle from '@/components/admin/PreviewToggle';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import {Trash2, Plus, Eye} from 'lucide-react';
import tryToCatch from 'try-to-catch';

const defaultEventsContent: EventsContent = {
    events: [],
};

export default function AdminEventsPage() {
    const {
        content,
        setContent,
        loading,
        saving,
        isDirty,
        saveContent,
        resetContent,
    } = useAdminContent<EventsContent>({
        previewCollection: 'preview',
        previewDocId: 'events',
        liveCollection: 'live',
        liveDocId: 'events',
        defaultValue: defaultEventsContent,
    });
    
    const {toast, showToast} = useToast();
    const {resolveImages} = useImageResolver();
    
    // Track validation errors for each event
    const [eventErrors, setEventErrors] = useState<Record<number, Record<string, string>>>({});
    
    // Refs for scrolling
    const eventRefs = useRef<Record<number, HTMLDivElement | null>>({});
    const [lastAddedEventId, setLastAddedEventId] = useState<number | null>(null);
    const [errorEventId, setErrorEventId] = useState<number | null>(null);
    
    // Preview panel visibility state
    const [showPreview, setShowPreview] = useState(false);
    
    // Resolve all image paths when content changes
    useEffect(() => {
        if (content?.events) {
            const imagePaths = content
                .events
                .flatMap((event) => [
                event.images.main,
                event.images.details,
            ])
                .filter(Boolean);
            
            resolveImages(imagePaths);
        }
    }, [content, resolveImages]);
    
    // Scroll to newly added event
    useEffect(() => {
        if (lastAddedEventId !== null && eventRefs.current[lastAddedEventId]) {
            eventRefs.current[lastAddedEventId]?.scrollIntoView({ behavior: 'smooth', block: 'center' });
            setLastAddedEventId(null);
        }
    }, [lastAddedEventId]);
    
    // Scroll to event with error
    useEffect(() => {
        if (errorEventId !== null && eventRefs.current[errorEventId]) {
            eventRefs.current[errorEventId]?.scrollIntoView({ behavior: 'smooth', block: 'center' });
            setErrorEventId(null);
        }
    }, [errorEventId]);

    // Update a specific event field
    const updateEventField = (index: number, field: keyof Event, value: string | number | boolean) => {
        const updatedEvents = content.events.slice();
        
        updatedEvents[index] = {
            ...updatedEvents[index],
            [field]: value,
        };
        
        setContent({
            ...content,
            events: updatedEvents,
        });

        // Validate required fields
        if (field === 'title' || field === 'date' || field === 'location' || field === 'description')
            validateEventField(index, field as string, value as string);
    };
    
    // Validate an event field
    const validateEventField = (index: number, field: string, value: string) => {
        const isValid = value.trim().length > 0;
        
        setEventErrors((prev) => ({
            ...prev,
            [index]: {
                ...prev[index],
                [field]: isValid ? '' : `${field
                    .charAt(0)
                    .toUpperCase() + field.slice(1)} is required`,
            },
        }));
        
        return isValid;
    };
    
    // Update an event image
    const updateEventImage = (index: number, key: keyof Event['images'], value: string) => {
        const updatedEvents = content.events.slice();
        
        updatedEvents[index] = {
            ...updatedEvents[index],
            images: {
                ...updatedEvents[index].images,
                [key]: value,
            },
        };
        
        setContent({
            ...content,
            events: updatedEvents,
        });

        // Validate main image
        if (key === 'main')
            validateEventField(index, 'mainImage', value);
    };
    
    // Add a new event
    const addEvent = () => {
        const newEventId = Date.now();
        setContent({
            ...content,
            events: [
                ...content.events, {
                    id: newEventId,
                    title: '',
                    date: '',
                    location: '',
                    description: '',
                    images: {
                        main: '',
                    },
                    status: 'upcoming',
                },
            ],
        });
        
        // Set the ID of the newly added event for scrolling
        setLastAddedEventId(newEventId);
    };
    
    // Remove an event
    const removeEvent = (index: number) => {
        setContent({
            ...content,
            events: content.events.filter((_, i) => i !== index),
        });
        
        // Remove validation errors for this event
        setEventErrors((prev) => {
            const newErrors = {...prev};
            delete newErrors[index];
            return newErrors;
        });
    };
    
    const handleSave = async () => {
        // Validate all events before saving
        let isValid = true;
        const newEventErrors: Record<number, Record<string, string>> = {};
        let firstErrorEventId = null;
        
        for (const [index, event] of content.events.entries()) {
            const eventFieldErrors: Record<string, string> = {};
            
            // Validate required fields
            if (!event.title || !event.title.trim().length) {
                eventFieldErrors.title = 'Title is required';
                isValid = false;
            }
            
            if (!event.date || !event.date.trim().length) {
                eventFieldErrors.date = 'Date is required';
                isValid = false;
            }
            
            if (!event.location || !event.location.trim().length) {
                eventFieldErrors.location = 'Location is required';
                isValid = false;
            }
            
            if (!event.description || !event.description.trim().length) {
                eventFieldErrors.description = 'Description is required';
                isValid = false;
            }
            
            if (!event.images.main || !event.images.main.trim().length) {
                eventFieldErrors.mainImage = 'Main image is required';
                isValid = false;
            }
            
            if (Object.keys(eventFieldErrors).length > 0) {
                newEventErrors[index] = eventFieldErrors;
                if (firstErrorEventId === null) {
                    firstErrorEventId = event.id;
                }
            }
        }
        
        setEventErrors(newEventErrors);
        
        if (!isValid) {
            showToast('Please fix the validation errors before saving', {
                variant: 'error',
            });
            
            // Scroll to the first event with an error
            if (firstErrorEventId !== null) {
                setErrorEventId(firstErrorEventId);
            }
            
            return;
        }
        
        await tryToCatch(saveContent);
    };
    
    return (
        <AdminFormLayout
            title="Edit Events Page"
            description="Manage events for your gallery. Events can be upcoming, ongoing, or past."
            isLoading={loading}
            isSaving={saving}
            isDirty={isDirty}
            onSave={handleSave}
            onReset={resetContent}
            previewUrl="/events"
            previewDocId="events"
        >
            <div className="flex justify-end mb-4">
                <PreviewToggle isVisible={showPreview} onToggle={() => setShowPreview(!showPreview)} />
            </div>
            
            <div className={`grid grid-cols-1 ${showPreview ? 'md:grid-cols-3' : 'md:grid-cols-1'} gap-6`}>
                <div className={showPreview ? 'md:col-span-2' : 'md:col-span-1'}>
                    <div className="space-y-8">
                        <div className="flex justify-between items-center">
                            <p className="text-gray-600">
                                Add, edit, or remove events. All fields are required except for the details image.
                            </p>
                            <Button
                                    type="button"
                                    onClick={addEvent}
                                >
                                <Plus className="h-4 w-4 mr-1"/> Add Event
                            </Button>
                        </div>
                        {content.events.length === 0 ? (
                        <div className="text-center py-12 border rounded-md bg-gray-50">
                            <p className="text-gray-500 mb-4">No events added yet</p>
                            <Button
                                    type="button"
                                    onClick={addEvent}
                                >
                                <Plus className="h-4 w-4 mr-1"/> Add Your First Event
                                </Button>
                        </div>
                        ) : (
                        <div className="space-y-8">
                            {content.events.map((event, index) => {
                                const hasErrors = eventErrors[index] && Object.values(eventErrors[index]).some(error => error);
                                
                                return (
                                    <div 
                                        key={event.id} 
                                        ref={el => eventRefs.current[event.id] = el}
                                        className={`border rounded-md p-6 space-y-6 ${hasErrors ? 'border-red-500 bg-red-50' : ''}`}
                                    >
                                        {hasErrors && (
                                            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
                                                <strong className="font-bold">Please fix the errors in this event</strong>
                                            </div>
                                        )}
                                        <div className="flex justify-between items-center">
                                            <h3 className="text-lg font-medium">
                                                {event.title || `Event ${index + 1}`}
                                            </h3>
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="icon"
                                                onClick={() => removeEvent(index)}
                                            >
                                            <Trash2 className="h-4 w-4"/>
                                        </Button>
                                        </div>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <FormField
                                                label="Title"
                                                required
                                                error={eventErrors[index]?.title}
                                            >
                                            <Input
                                                value={event.title}
                                                onChange={(e) => updateEventField(index, 'title', e.target.value)}
                                                placeholder="Event title"
                                                className={eventErrors[index]?.title ? 'border-red-500' : ''}
                                            />
                                        </FormField>
                                            <FormField
                                                    label="Status"
                                                    required
                                                >
                                                <Select
                                                        value={event.status}
                                                        onValueChange={(value) => updateEventField(index, 'status', value)}
                                                    >
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Select status"/>
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="upcoming">Upcoming</SelectItem>
                                                        <SelectItem value="ongoing">Ongoing</SelectItem>
                                                        <SelectItem value="past">Past</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </FormField>
                                            <FormField
                                                label="Date"
                                                required
                                                error={eventErrors[index]?.date}
                                                helpText="e.g., June 15-30, 2023"
                                            >
                                            <Input
                                                value={event.date}
                                                onChange={(e) => updateEventField(index, 'date', e.target.value)}
                                                placeholder="e.g., June 15-30, 2023"
                                                className={eventErrors[index]?.date ? 'border-red-500' : ''}
                                            />
                                        </FormField>
                                            <FormField
                                                label="Location"
                                                required
                                                error={eventErrors[index]?.location}
                                            >
                                            <Input
                                                value={event.location}
                                                onChange={(e) => updateEventField(index, 'location', e.target.value)}
                                                placeholder="Event location"
                                                className={eventErrors[index]?.location ? 'border-red-500' : ''}
                                            />
                                        </FormField>
                                            <FormField
                                                label="Description"
                                                required
                                                error={eventErrors[index]?.description}
                                                className="md:col-span-2"
                                            >
                                            <Textarea
                                                value={event.description}
                                                onChange={(e) => updateEventField(index, 'description', e.target.value)}
                                                placeholder="Event description"
                                                rows={3}
                                                className={eventErrors[index]?.description ? 'border-red-500' : ''}
                                            />
                                        </FormField>
                                            <FormField
                                                label="Main Image"
                                                required
                                                error={eventErrors[index]?.mainImage}
                                                className="md:col-span-2"
                                            >
                                            <EnhancedImageField
                                                label=""
                                                imagePath={event.images.main}
                                                onChange={(path) => updateEventImage(index, 'main', path)}
                                                aspectRatio="wide"
                                                className={eventErrors[index]?.mainImage ? 'border border-red-500 rounded-md' : ''}
                                                required={true}
                                            />
                                        </FormField>
                                            <FormField
                                                label="Details Image"
                                                helpText="Optional additional image"
                                                className="md:col-span-2"
                                            >
                                            <EnhancedImageField
                                                label=""
                                                imagePath={event.images.details || ''}
                                                onChange={(path) => updateEventImage(index, 'details', path)}
                                                aspectRatio="wide"
                                            />
                                        </FormField>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                        )}
                    </div>
                </div>
                
                {/* Live Preview Panel */}
                {showPreview && (
                    <div className="md:col-span-1">
                        <div className="sticky top-24">
                            <div className="flex items-center mb-2">
                                <Eye className="h-4 w-4 mr-1 text-elegant-accent" />
                                <h3 className="text-sm font-medium text-elegant-text">Live Preview</h3>
                            </div>
                            <PreviewPanel content={content} type="events" />
                            <p className="text-xs text-elegant-text/60 mt-2 italic">
                                This preview shows how your content will appear on the public site
                            </p>
                        </div>
                    </div>
                )}
            </div>
            {toast}
        </AdminFormLayout>
    );
}