"use client";

import { useState, useEffect } from "react";
import { usePublicContent } from "@/hooks/use-public-content";
import { useImageResolver } from "@/hooks/use-image-resolver";
import EditButton from "@/components/EditButton";
import PageHeader from "@/components/PageHeader";
import { AboutContent } from "@/types/about";
import { ProfileCard } from "@/components/ProfileCard";
import { ShowMoreList } from "@/components/ShowMoreList";
import usePreview from "@/lib/usePreview";
import { useAdminAuth } from "@/lib/useAdminAuth";

const DEFAULT_ABOUT_CONTENT: AboutContent = {
  statement: [],
  education: [],
  exhibitions: {
    group: [],
    juried: [],
    solo: [],
    duo: [],
  },
  awards: [],
  collections: [],
  profileImage: "",
  professionalExperience: [],
  research: [],
  publications: [],
  participation: [],
  otherDetails: [],
};

export default function About() {
  const isPreviewMode = usePreview();
  const { user } = useAdminAuth();

  // Use the new hook for content loading
  const { content, loading: contentLoading } = usePublicContent<AboutContent>({
    collection: "about",
    documentId: "about",
    defaultValue: DEFAULT_ABOUT_CONTENT,
    previewMode: isPreviewMode,
  });

  const { statement, education, exhibitions, awards, collections, professionalExperience, research, publications, participation, otherDetails } = content;

  // Image resolver hook
  const { resolvedUrls, resolveImages, isResolving } = useImageResolver();
  
  const [activeExhibitionTab, setActiveExhibitionTab] = useState(() => 
    typeof window !== 'undefined' ? localStorage.getItem('about-exhibition-tab') || "solo" : "solo"
  );
  const [activeCollectionTab, setActiveCollectionTab] = useState(() => 
    typeof window !== 'undefined' ? localStorage.getItem('about-collection-tab') || "collection" : "collection"
  );
  const [activeExperienceTab, setActiveExperienceTab] = useState(() => 
    typeof window !== 'undefined' ? localStorage.getItem('about-experience-tab') || "professional" : "professional"
  );

  // Persist tab selections
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('about-exhibition-tab', activeExhibitionTab);
    }
  }, [activeExhibitionTab]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('about-collection-tab', activeCollectionTab);
    }
  }, [activeCollectionTab]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('about-experience-tab', activeExperienceTab);
    }
  }, [activeExperienceTab]);

  // Loading state
  const allGroupExhibitions = exhibitions.group;
  const allJuriedExhibitions = exhibitions.juried;
  const allSoloExhibitions = exhibitions.solo;
  const allDuoExhibitions = exhibitions.duo || [];

  // Resolve images when content changes
  useEffect(() => {
    if (content) {
      const pathsToResolve: string[] = [];
      if (content.profileImage && content.profileImage.trim() !== "") {
        pathsToResolve.push(content.profileImage);
      }
      if (pathsToResolve.length > 0) {
        resolveImages(pathsToResolve);
      }
    }
  }, [content, resolveImages]);

  // Show loading indicator - wait for both content and image resolution
  if (contentLoading || isResolving) {
    return (
      <div className="max-w-5xl mx-auto px-4 py-12 text-center">Loading...</div>
    );
  }

  return (
    <div className="max-w-5xl mx-auto px-4 py-16">
      <PageHeader
        title="About the Artist"
        subtitle="Biography"
        description="Artist Statement, Education, and Exhibition History"
      />
      <div className="flex items-center justify-end mb-2 -mt-12">
        <EditButton path="/admin/about" hidden={isPreviewMode || !user} />
      </div>
      {/* Profile + Statement with text flow */}
      <div className="bg-white elegant-card p-8 mb-12 animate-subtle-fade">
        <h2 className="text-2xl font-cormorant font-light text-elegant-text mb-6 relative decorative-line">
          Artist Statement
        </h2>
        <div className="sm:float-left sm:mr-8 sm:mb-4 mb-6 sm:max-w-xs w-full sm:w-auto flex justify-center sm:block">
          <div className="max-w-xs">
            <ProfileCard
              image={content?.profileImage && resolvedUrls[content.profileImage] ? resolvedUrls[content.profileImage] : null}
              name="Bela Raval"
              bio={null}
            />
          </div>
        </div>
        <div className="space-y-4 font-montserrat text-elegant-text/80">
          {statement.map((para, idx) => (
            <p key={idx} className="leading-relaxed sm:text-justify text-left">
              {para}
            </p>
          ))}
        </div>
        <div className="clear-both"></div>
      </div>
      
      <div className="bg-white elegant-card p-8 mb-10">
        <h2 className="text-2xl font-cormorant font-light text-elegant-text mb-6 relative decorative-line">
          Education
        </h2>
        <div className="space-y-4">
          {education.map((edu, idx) => (
            <div key={idx} className="border-l-2 border-elegant-accent/30 pl-4">
              <h3 className="font-montserrat font-medium text-elegant-text text-lg">
                {edu.heading}
              </h3>
              <p className="font-montserrat text-elegant-text/80 mt-2 leading-relaxed">
                {edu.details}
              </p>
            </div>
          ))}
        </div>
      </div>
      
      {/* Exhibitions Sections */}
      <div className="space-y-10">
        {/* Exhibitions Accordion */}
        <div className="space-y-10">
          {/* Experiences with Tabs */}
          <div className="bg-white elegant-card">
            <div className="p-8">
              <h2 className="text-2xl font-cormorant font-light text-elegant-text mb-6 relative decorative-line">
                Experiences
              </h2>
              
              {/* Tab Navigation */}
              <div className="flex border-b border-elegant/20 mb-6 overflow-x-auto gap-x-1 md:flex-wrap md:gap-y-2 scrollbar-thin scrollbar-track-transparent scrollbar-thumb-elegant/20">
                <button 
                  onClick={() => setActiveExperienceTab("professional")}
                  className={`px-4 py-2 font-montserrat text-sm whitespace-nowrap flex-shrink-0 ${activeExperienceTab === "professional" 
                    ? "border-b-2 border-elegant-accent text-elegant-text" 
                    : "text-elegant-text/60"}`}
                >
                  Professional
                </button>
                <button 
                  onClick={() => setActiveExperienceTab("research")}
                  className={`px-4 py-2 font-montserrat text-sm whitespace-nowrap flex-shrink-0 ${activeExperienceTab === "research" 
                    ? "border-b-2 border-elegant-accent text-elegant-text" 
                    : "text-elegant-text/60"}`}
                >
                  Research
                </button>
                <button 
                  onClick={() => setActiveExperienceTab("participation")}
                  className={`px-4 py-2 font-montserrat text-sm whitespace-nowrap flex-shrink-0 ${activeExperienceTab === "participation" 
                    ? "border-b-2 border-elegant-accent text-elegant-text" 
                    : "text-elegant-text/60"}`}
                >
                  Participation
                </button>
                <button 
                  onClick={() => setActiveExperienceTab("publication")}
                  className={`px-4 py-2 font-montserrat text-sm whitespace-nowrap flex-shrink-0 ${activeExperienceTab === "publication" 
                    ? "border-b-2 border-elegant-accent text-elegant-text" 
                    : "text-elegant-text/60"}`}
                >
                  Publication
                </button>
                <button 
                  onClick={() => setActiveExperienceTab("others")}
                  className={`px-4 py-2 font-montserrat text-sm whitespace-nowrap flex-shrink-0 ${activeExperienceTab === "others" 
                    ? "border-b-2 border-elegant-accent text-elegant-text" 
                    : "text-elegant-text/60"}`}
                >
                  Others
                </button>
              </div>
              
              {/* Tab Content */}
              <div className="transition-all duration-300 ease-in-out">
              {activeExperienceTab === "professional" && professionalExperience && professionalExperience.length > 0 && (
                <ShowMoreList
                  items={professionalExperience}
                  renderItem={(exp, index) => (
                    <div key={index} className="flex items-start mb-2">
                      <span className="inline-block px-4 py-1 bg-elegant-light text-elegant-text border border-elegant font-montserrat text-sm mr-5 min-w-[80px] text-center">
                        {exp.years}
                      </span>
                      <p className="text-elegant-text/90 font-montserrat flex-1">
                        {exp.details}
                      </p>
                    </div>
                  )}
                />
              )}
              
              {activeExperienceTab === "research" && (
                research && research.length > 0 ? (
                  <ShowMoreList
                    items={research}
                    renderItem={(item, index) => (
                      <div key={index} className="flex items-start mb-2">
                        <span className="inline-block px-4 py-1 bg-elegant-light text-elegant-text border border-elegant font-montserrat text-sm mr-5 min-w-[80px] text-center">
                          {item.years}
                        </span>
                        <p className="text-elegant-text/90 font-montserrat flex-1">
                          {item.details}
                        </p>
                      </div>
                    )}
                  />
                ) : (
                  <p className="text-elegant-text/70 font-montserrat italic text-center py-4">No research experience to display.</p>
                )
              )}
              
              {activeExperienceTab === "participation" && (
                participation && participation.length > 0 ? (
                  <ShowMoreList
                    items={participation}
                    renderItem={(item, index) => (
                      <div key={index} className="flex items-start mb-2">
                        <span className="inline-block px-4 py-1 bg-elegant-light text-elegant-text border border-elegant font-montserrat text-sm mr-5 min-w-[80px] text-center">
                          {item.year}
                        </span>
                        <p className="text-elegant-text/90 font-montserrat flex-1">
                          {item.details}
                        </p>
                      </div>
                    )}
                  />
                ) : (
                  <p className="text-elegant-text/70 font-montserrat italic text-center py-4">No participation experience to display.</p>
                )
              )}
              
              {activeExperienceTab === "publication" && (
                publications && publications.length > 0 ? (
                  <ShowMoreList
                    items={publications}
                    renderItem={(item, index) => (
                      <div key={index} className="flex items-start mb-2">
                        <span className="inline-block px-4 py-1 bg-elegant-light text-elegant-text border border-elegant font-montserrat text-sm mr-5 min-w-[80px] text-center">
                          {item.year}
                        </span>
                        <p className="text-elegant-text/90 font-montserrat flex-1">
                          {item.details}
                        </p>
                      </div>
                    )}
                  />
                ) : (
                  <p className="text-elegant-text/70 font-montserrat italic text-center py-4">No publication experience to display.</p>
                )
              )}
              
              {activeExperienceTab === "others" && (
                otherDetails && otherDetails.length > 0 ? (
                  <ShowMoreList
                    items={otherDetails}
                    renderItem={(item, index) => (
                      <div key={index} className="flex items-start mb-2">
                        <span className="inline-block px-4 py-1 bg-elegant-light text-elegant-text border border-elegant font-montserrat text-sm mr-5 min-w-[80px] text-center">
                          {item.years}
                        </span>
                        <p className="text-elegant-text/90 font-montserrat flex-1">
                          {item.details}
                        </p>
                      </div>
                    )}
                  />
                ) : (
                  <p className="text-elegant-text/70 font-montserrat italic text-center py-4">No other experience to display.</p>
                )
              )}
              </div>
            </div>
          </div>
          {/* Exhibitions with Tabs */}
          <div className="bg-white elegant-card">
            <div className="p-8">
              <h2 className="text-2xl font-cormorant font-light text-elegant-text mb-6 relative decorative-line">
                Exhibitions
              </h2>
              
              {/* Tab Navigation */}
              <div className="flex border-b border-elegant/20 mb-6 flex-wrap gap-x-1 gap-y-2">
                <button 
                  onClick={() => setActiveExhibitionTab("solo")}
                  className={`px-4 py-2 font-montserrat text-sm ${activeExhibitionTab === "solo" 
                    ? "border-b-2 border-elegant-accent text-elegant-text" 
                    : "text-elegant-text/60"}`}
                >
                  Solo
                </button>
                <button 
                  onClick={() => setActiveExhibitionTab("duo")}
                  className={`px-4 py-2 font-montserrat text-sm ${activeExhibitionTab === "duo" 
                    ? "border-b-2 border-elegant-accent text-elegant-text" 
                    : "text-elegant-text/60"}`}
                >
                  Duo
                </button>
                <button 
                  onClick={() => setActiveExhibitionTab("group")}
                  className={`px-4 py-2 font-montserrat text-sm ${activeExhibitionTab === "group" 
                    ? "border-b-2 border-elegant-accent text-elegant-text" 
                    : "text-elegant-text/60"}`}
                >
                  Group
                </button>
                <button 
                  onClick={() => setActiveExhibitionTab("juried")}
                  className={`px-4 py-2 font-montserrat text-sm ${activeExhibitionTab === "juried" 
                    ? "border-b-2 border-elegant-accent text-elegant-text" 
                    : "text-elegant-text/60"}`}
                >
                  Juried
                </button>
              </div>
              
              {/* Tab Content */}
              <div className="transition-all duration-300 ease-in-out">
              {activeExhibitionTab === "solo" && (
                <ShowMoreList
                  items={allSoloExhibitions}
                  renderItem={(exhibition, index) => (
                    <div key={index} className="flex items-start mb-2">
                      <span className="inline-block px-4 py-1 bg-elegant-light text-elegant-text border border-elegant font-montserrat text-sm mr-5 min-w-[80px] text-center">
                        {exhibition.year}
                      </span>
                      <p className="text-elegant-text/90 font-montserrat flex-1">
                        {exhibition.details}
                      </p>
                    </div>
                  )}
                />
              )}
              
              {activeExhibitionTab === "duo" && (
                <ShowMoreList
                  items={allDuoExhibitions}
                  renderItem={(exhibition, index) => (
                    <div key={index} className="flex items-start mb-2">
                      <span className="inline-block px-4 py-1 bg-elegant-light text-elegant-text border border-elegant font-montserrat text-sm mr-5 min-w-[80px] text-center">
                        {exhibition.year}
                      </span>
                      <p className="text-elegant-text/90 font-montserrat flex-1">
                        {exhibition.details}
                      </p>
                    </div>
                  )}
                />
              )}
              
              {activeExhibitionTab === "group" && (
                <ShowMoreList
                  items={allGroupExhibitions}
                  renderItem={(exhibition, index) => (
                    <div key={index} className="flex items-start mb-2">
                      <span className="inline-block px-4 py-1 bg-elegant-light text-elegant-text border border-elegant font-montserrat text-sm mr-5 min-w-[80px] text-center">
                        {exhibition.year}
                      </span>
                      <p className="text-elegant-text/90 font-montserrat flex-1">
                        {exhibition.details}
                      </p>
                    </div>
                  )}
                />
              )}
              
              {activeExhibitionTab === "juried" && (
                <ShowMoreList
                  items={allJuriedExhibitions}
                  renderItem={(exhibition, index) => (
                    <div key={index} className="flex items-start mb-2">
                      <span className="inline-block px-4 py-1 bg-elegant-light text-elegant-text border border-elegant font-montserrat text-sm mr-5 min-w-[80px] text-center">
                        {exhibition.year}
                      </span>
                      <p className="text-elegant-text/90 font-montserrat flex-1">
                        {exhibition.details}
                      </p>
                    </div>
                  )}
                />
              )}
              </div>
            </div>
          </div>
        </div>


        {/* Collections and Awards with Tabs */}
        <div className="bg-white elegant-card">
          <div className="p-8">
            <h2 className="text-2xl font-cormorant font-light text-elegant-text mb-6 relative decorative-line">
              Collections and Awards
            </h2>
            
            {/* Tab Navigation */}
            <div className="flex border-b border-elegant/20 mb-6 flex-wrap gap-x-1 gap-y-2">
              <button 
                onClick={() => setActiveCollectionTab("collection")}
                className={`px-4 py-2 font-montserrat text-sm ${activeCollectionTab === "collection" 
                  ? "border-b-2 border-elegant-accent text-elegant-text" 
                  : "text-elegant-text/60"}`}
              >
                Collection
              </button>
              <button 
                onClick={() => setActiveCollectionTab("awards")}
                className={`px-4 py-2 font-montserrat text-sm ${activeCollectionTab === "awards" 
                  ? "border-b-2 border-elegant-accent text-elegant-text" 
                  : "text-elegant-text/60"}`}
              >
                Awards
              </button>
            </div>
            
            {/* Tab Content */}
            <div className="transition-all duration-300 ease-in-out">
            {activeCollectionTab === "collection" && (
              <ul className="list-disc pl-5 space-y-3 text-elegant-text/90 font-montserrat">
                {collections.map((collection, index) => (
                  <li key={index}>{collection}</li>
                ))}
              </ul>
            )}
            
            {activeCollectionTab === "awards" && (
              <ShowMoreList
                items={awards}
                renderItem={(award, index) => (
                  <div key={index} className="flex items-start mb-2">
                    <span className="inline-block px-4 py-1 bg-elegant-light text-elegant-text border border-elegant font-montserrat text-sm mr-5 min-w-[80px] text-center">
                      {award.year}
                    </span>
                    <p className="text-elegant-text/90 font-montserrat flex-1">
                      {award.details}
                    </p>
                  </div>
                )}
              />
            )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}