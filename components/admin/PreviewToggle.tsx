'use client';

import { Eye, EyeOff } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';

interface PreviewToggleProps {
  isVisible: boolean;
  onToggle: () => void;
}

export function PreviewToggle({ isVisible, onToggle }: PreviewToggleProps) {
  return (
    <Button
      variant="outline"
      size="sm"
      onClick={onToggle}
      className="flex items-center gap-1 text-elegant-text/80 hover:text-elegant-text"
    >
      {isVisible ? (
        <>
          <EyeOff className="h-4 w-4" />
          <span className="text-xs">Hide Side Preview </span>
        </>
      ) : (
        <>
          <Eye className="h-4 w-4" />
          <span className="text-xs">Show Side Preview</span>
        </>
      )}
    </Button>
  );
}

export default PreviewToggle;