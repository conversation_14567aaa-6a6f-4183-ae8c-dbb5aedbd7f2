// Auto-generated by migrate-content.ts
import {
    getStorage,
    ref,
    getDownloadURL,
} from 'firebase/storage';

/**
 * Utility to get the Firebase Storage path for a public image path
 * @param publicPath - The public path (e.g. "/images/2013/08/image.jpg")
 * @returns The Firebase Storage path
 */
export function getStoragePath(publicPath: string): string {
    // Remove leading slash if present
    const normalizedPath = publicPath.startsWith('/') ? publicPath.slice(1) : publicPath;
    
    return `${process.env.NEXT_PUBLIC_STORAGE_BASE_PATH || 'bela-gallery'}/${normalizedPath.replace(/^images\//, 'images/')}`;
}

/**
 * Optionally, a helper to get the storage ref (if needed elsewhere)
 * @param publicPath - The public path (e.g. "/images/2013/08/image.jpg")
 * @returns The Firebase Storage reference
 */
export function getStorageRef(publicPath: string) {
    return ref(getStorage(), getStoragePath(publicPath));
}

/**
 * Returns a Promise that resolves to the public download URL for an image in Firebase Storage.
 * Works for both emulator and production if Firebase is configured correctly.
 * @param publicPath - The public path (e.g. "/images/2013/08/image.jpg")
 */
export async function getPublicImageUrl(publicPath: string, cacheBuster: boolean = false): Promise<string> {
    const isStaticBuild = process.env.NEXT_PUBLIC_STATIC_BUILD === 'true';
    
    if (isStaticBuild) {
        // Static mode: return path as-is (images are in public/images/)
        const normalizedPath = publicPath.startsWith('/') ? publicPath : `/${publicPath}`;
        return cacheBuster ? `${normalizedPath}?t=${Date.now()}` : normalizedPath;
    } else {
        // Firebase Storage mode (existing logic)
        const basePath = process.env.NEXT_PUBLIC_STORAGE_BASE_PATH || 'bela-gallery';
        
        // Remove leading slash for consistency
        const normalizedPath = publicPath.startsWith('/') ? publicPath.slice(1) : publicPath;
        
        const fullPath = `${basePath}/${normalizedPath}`;
        const storage = getStorage();
        const storageRef = ref(storage, fullPath);
        
        const url = await getDownloadURL(storageRef);
        
        // Add cache busting parameter if requested
        if (cacheBuster)
            return `${url}?t=${Date.now()}`;
        
        return url;
    }
}
