import React from "react";
import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import PublishButton from "./PublishButton";
import { useToast } from "@/hooks/use-toast";

// Mock the useToast hook
vi.mock("@/hooks/use-toast", () => ({
  useToast: vi.fn(),
}));

// Mock Lucide icons
vi.mock("lucide-react", () => ({
  Loader2: () => <span data-testid="loader-icon" />,
}));

// Mock fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock window.location.reload
const mockReload = vi.fn();
Object.defineProperty(window, "location", {
  value: {
    ...window.location,
    reload: mockReload,
    hostname: "localhost", // Set hostname for testing
  },
  writable: true,
});

describe("PublishButton", () => {
  const mockShowToast = vi.fn();
  
  beforeEach(() => {
    vi.clearAllMocks();
    (useToast as ReturnType<typeof vi.fn>).mockReturnValue({ showToast: mockShowToast });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("renders the publish button correctly", () => {
    render(<PublishButton />);
    
    expect(screen.getByText("Publish All")).toBeInTheDocument();
    expect(screen.queryByTestId("loader-icon")).not.toBeInTheDocument();
  });

  it("shows loading state when publishing", async () => {
    // Mock a delayed response
    mockFetch.mockImplementationOnce(() => 
      new Promise(resolve => setTimeout(() => 
        resolve({
          json: () => Promise.resolve({ success: true, versionId: "123" })
        }), 100)
      )
    );

    render(<PublishButton />);
    
    // Click the publish button
    fireEvent.click(screen.getByText("Publish All"));
    
    // Check loading state
    expect(screen.getByText("Publishing...")).toBeInTheDocument();
    expect(screen.getByTestId("loader-icon")).toBeInTheDocument();
    expect(screen.getByRole("button")).toBeDisabled();
  });

  it("shows success toast and reloads page on successful publish", async () => {
    // Mock successful response
    mockFetch.mockResolvedValueOnce({
      json: () => Promise.resolve({ success: true, versionId: "123" })
    });

    render(<PublishButton />);
    
    // Click the publish button
    fireEvent.click(screen.getByText("Publish All"));
    
    // Wait for the async operation to complete
    await waitFor(() => {
      expect(mockShowToast).toHaveBeenCalledWith(
        "Content published successfully (Version: 123)",
        { variant: "default" }
      );
      expect(mockReload).toHaveBeenCalled();
    });
  });

  it("shows error toast when publish fails with error message", async () => {
    // Mock failed response with error message
    mockFetch.mockResolvedValueOnce({
      json: () => Promise.resolve({ success: false, error: "Server error" })
    });

    render(<PublishButton />);
    
    // Click the publish button
    fireEvent.click(screen.getByText("Publish All"));
    
    // Wait for the async operation to complete
    await waitFor(() => {
      expect(mockShowToast).toHaveBeenCalledWith(
        "Publish failed: Server error",
        { variant: "destructive" }
      );
      expect(mockReload).not.toHaveBeenCalled();
    });
  });

  it("shows error toast when fetch throws an error", async () => {
    // Mock fetch throwing an error
    mockFetch.mockRejectedValueOnce(new Error("Network error"));

    render(<PublishButton />);
    
    // Click the publish button
    fireEvent.click(screen.getByText("Publish All"));
    
    // Wait for the async operation to complete
    await waitFor(() => {
      expect(mockShowToast).toHaveBeenCalledWith(
        "Failed to publish: Network error",
        { variant: "destructive" }
      );
      expect(mockReload).not.toHaveBeenCalled();
    });
  });

  it("uses the correct publish URL based on environment", async () => {
    // Mock successful response
    mockFetch.mockResolvedValueOnce({
      json: () => Promise.resolve({ success: true, versionId: "123" })
    });

    render(<PublishButton />);
    
    // Click the publish button
    fireEvent.click(screen.getByText("Publish All"));
    
    // Wait for the async operation to complete
    await waitFor(() => {
      // Check that fetch was called with the correct URL for localhost
      expect(mockFetch).toHaveBeenCalledWith(
        "http://127.0.0.1:5001/belagallery-9e01b/us-central1/publish",
        expect.any(Object)
      );
    });
  });
});