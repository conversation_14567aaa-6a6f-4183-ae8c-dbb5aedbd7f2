'use client';

import {useState, useEffect} from 'react';
import {cva, VariantProps} from 'class-variance-authority';
import {cn} from '@/lib/utils';
import {X} from 'lucide-react';

const toastVariants = cva('fixed z-50 flex items-center justify-between px-4 py-2 rounded shadow-md transition-all duration-300 transform', {
    variants: {
        variant: {
            default: 'bg-white text-gray-900 border border-gray-200',
            success: 'bg-green-500 text-white',
            error: 'bg-red-500 text-white',
            warning: 'bg-amber-500 text-white',
            info: 'bg-blue-500 text-white',
        },
        position: {
            topRight: 'top-4 right-4',
            topLeft: 'top-4 left-4',
            bottomRight: 'bottom-4 right-4',
            bottomLeft: 'bottom-4 left-4',
        },
    },
    defaultVariants: {
        variant: 'default',
        position: 'topRight',
    },
});

export interface ToastProps extends VariantProps<typeof toastVariants> {
    message: string;
    duration?: number;
    onClose?: () => void;
    className?: string;
}

export function Toast({message, duration = 3000, onClose, variant, position, className}: ToastProps) {
    const [isVisible, setIsVisible] = useState(true);
    
    useEffect(() => {
        if (!message)
            return;
        
        const timer = setTimeout(() => {
            setIsVisible(false);
            setTimeout(() => {
                onClose?.();
            }, 300);
            // Allow time for exit animation
        }, duration);
        
        return () => clearTimeout(timer);
    }, [message, duration, onClose]);
    
    if (!message)
        return null;
    
    return (
        <div
            className={cn(toastVariants({
                variant,
                position,
            }), isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2', className)}
        >
            <span>{message}</span>
            <button
                    onClick={() => {
                        setIsVisible(false);
                        setTimeout(() => onClose?.(), 300);
                    }}
                    className="ml-4 hover:opacity-70"
                >
                <X size={16}/>
            </button>
        </div>
    );
}
