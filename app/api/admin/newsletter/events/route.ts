import { NextResponse } from 'next/server';
import { adminDb } from '@/lib/firebase-admin';
import { getRelevantEvents } from '@/lib/eventsHelpers';

export async function GET() {
  try {
    
    // Get events content directly (no "published" concept)
    const eventsRef = adminDb.collection('live').doc('events');
    const eventsDoc = await eventsRef.get();
    
    
    if (!eventsDoc.exists) {
      return NextResponse.json({
        events: [],
        message: 'No events found. Create events through the admin interface first.'
      }, { status: 200 });
    }

    const eventsData = eventsDoc.data();
    
    // Get events directly from the document
    const events = eventsData?.events || [];

    // Get relevant events (ongoing and upcoming) using helper
    const relevantEvents = getRelevantEvents(events);
    
    // Format events for newsletter selection
    const formattedEvents = relevantEvents.map((event, index) => ({
      id: `event-${index}`,
      title: event.title || 'Untitled Event',
      date: event.date || '',
      description: event.description || '',
      location: event.location || '',
      image: event.images?.main || '',
      status: event._status
    }));


    return NextResponse.json({ events: formattedEvents }, { status: 200 });

  } catch (error) {
    console.error('❌ Error fetching events:', error);
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}