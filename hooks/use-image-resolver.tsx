'use client';

import {useState, useCallback} from 'react';
import {getPublicImageUrl} from '@/lib/image-paths';
import {logError} from '@/lib/errorLogger';

const isError = (a: unknown): a is Error => a instanceof Error;

interface UseImageResolverReturn {
    resolvedUrls: Record<string, string | null>;
    resolveImages: (paths: string[]) => Promise<void>;
    getResolvedUrl: (path: string) => string | null;
    isResolving: boolean;
}

/**
 * A hook for resolving image paths to public URLs
 * 
 * @returns Image resolver state and functions
 */
export function useImageResolver(): UseImageResolverReturn {
    const [resolvedUrls, setResolvedUrls] = useState<Record<string, string | null>>({});
    const [isResolving, setIsResolving] = useState(false);
    
    const resolveImages = useCallback(async (paths: string[]) => {
        if (!paths || !paths.length)
            return;
        
        setIsResolving(true);
        
        try {
            // Filter out empty paths and get unique values
            const uniquePaths = Array.from(new Set(paths.filter(Boolean)));
            
            // Only resolve paths we haven't already resolved
            const pathsToResolve = uniquePaths.filter((path) => !resolvedUrls[path]);
            
            if (!pathsToResolve.length) {
                setIsResolving(false);
                return;
            }
            
            const newUrls: Record<string, string | null> = {...resolvedUrls};
            
            await Promise.all(pathsToResolve.map(async (path) => {
                try {
                    newUrls[path] = await getPublicImageUrl(path);
                } catch(error) {
                    logError(isError(error) ? error : Error(String(error)), 'useImageResolver.resolveImages', {
                        context: {
                            path,
                        },
                    });
                    newUrls[path] = null; // Store null on resolution error
                }
            }));
            
            setResolvedUrls(newUrls);
        } catch(error) {
            logError(isError(error) ? error : Error(String(error)), 'useImageResolver.resolveImages', {
                context: {
                    paths,
                },
            });
        } finally {
            setIsResolving(false);
        }
    }, []); // eslint-disable-line react-hooks/exhaustive-deps -- resolvedUrls dependency would cause infinite loop
    
    // Helper function to get a resolved URL or return the original path
    const getResolvedUrl = useCallback((path: string) => {
        return resolvedUrls[path]; // Will return the stored URL, null, or undefined if not present
    }, [resolvedUrls]);
    
    return {
        resolvedUrls,
        resolveImages,
        getResolvedUrl,
        isResolving,
    };
}
