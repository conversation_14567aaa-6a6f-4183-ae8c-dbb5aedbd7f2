#!/bin/bash

# Default test type is "all" if not specified
TEST_TYPE=${1:-all}

# Update the test event file with the specified test type
cat > ./test-event.json << EOF
{
  "inputs": {
    "test_type": "${TEST_TYPE}"
  }
}
EOF

# Run the tests using act with our custom Docker image
# Use --pull=false to prevent <PERSON><PERSON> from trying to pull the image
# Use -W .. to set the workspace to the parent directory
# Use --workflows to specify where to find workflow files
cd ..
./action/act workflow_dispatch -b -e ./action/test-event.json --secret-file ./action/.secrets -P ubuntu-latest=bela-gallery-test-env:latest --pull=false   --workflows .github/workflows 