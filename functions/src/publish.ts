import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import { createVersionSnapshot } from "./backup";
import { logAuditEvent } from "./audit";

if (!admin.apps.length) admin.initializeApp();

const db = admin.firestore();

/**
 * Basic validation function for content references
 * This is a placeholder for future image reference tracking
 */
export const validateContentReferences = async () => {
  // This will be replaced with more robust validation in the future
  // For now, just do basic existence checks
  const errors: string[] = [];

  try {
    const previewDocs = await db.collection("preview").listDocuments();

    for (const docRef of previewDocs) {
      const doc = await docRef.get();

      if (!doc.exists)
        errors.push(`Document ${docRef.id} referenced but does not exist`);
    }

    return {
      valid: !errors.length,
      errors,
    };
  } catch (error) {
    return {
      valid: false,
      errors: [(error as Error).message],
    };
  }
};

/**
 * Updates live content from preview
 */
export const updateLiveContent = async (batch: admin.firestore.WriteBatch) => {
  const previewSnapshot = await db.collection("preview").get();

  if (previewSnapshot.empty) return;

  // Define valid page types to prevent creating unwanted collections
  const validPages = ["home", "about", "events", "menu", "contact", "gallery"];

  let galleryDoc: Record<string, unknown> | null = null;
  let galleryDocId: string | null = null;
  const slugUpdates: Record<string, Record<string, unknown>> = {};
  let foundSlugsInPreview = false;

  for (const previewDoc of previewSnapshot.docs) {
    const previewId = previewDoc.id;
    const previewData = previewDoc.data() as Record<string, unknown>;
    if (validPages.includes(previewId)) {
      if (previewId === "gallery") {
        // Save gallery doc for later merging with slugs
        galleryDoc = previewData;
        galleryDocId = previewId;
      } else {
        // preview/{page} -> live/{page}
        batch.set(db.collection("live").doc(previewId), previewData);
        functions.logger.info(
          `Publishing preview/${previewId} to live/${previewId}`
        );
      }
    } else {
      // Treat as a gallery slug
      slugUpdates[previewId] = previewData;
      foundSlugsInPreview = true;
      functions.logger.info(
        `Will merge preview/${previewId} as gallery slug into live/gallery`
      );
    }
  }

  // Merge gallery slugs into gallery doc and write to live/gallery
  if (galleryDocId === "gallery" || foundSlugsInPreview) {
    // Start with preview/gallery data or fallback to live/gallery
    let mergedGallery: Record<string, unknown> = galleryDoc
      ? { ...galleryDoc }
      : {};
    if (!galleryDoc) {
      // If no preview/gallery, try to get live/gallery as base
      const liveGallerySnap = await db.collection("live").doc("gallery").get();
      if (liveGallerySnap.exists) {
        mergedGallery = { ...liveGallerySnap.data() };
      }
    }
    // Ensure slugs array exists
    const slugsArr = Array.isArray(mergedGallery.slugs)
      ? (mergedGallery.slugs as Array<Record<string, unknown>>)
      : [];
    // Update or add each slug (only if the preview doc is not 'gallery')
    for (const [slug, data] of Object.entries(slugUpdates)) {
      // Only copy the actual slug data, not categories
      // Remove any accidental nested 'slugs' property from the slug data
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { slugs: _unused, ...cleanedData } = data;
      const idx = slugsArr.findIndex(
        (s) => typeof s.slug === "string" && s.slug === slug
      );
      if (idx >= 0) {
        slugsArr[idx] = { ...slugsArr[idx], ...cleanedData, slug };
      } else {
        slugsArr.push({ slug, ...cleanedData });
      }
      functions.logger.info(
        `Merged slug ${slug} into gallery.slugs array for live/gallery`
      );
    }
    // Always copy categories from preview/gallery if present
    if (galleryDoc && galleryDoc.categories) {
      mergedGallery.categories = galleryDoc.categories;
    }
    // Remove any accidental nested slugs under categories
    if (mergedGallery.categories && Array.isArray(mergedGallery.categories)) {
      mergedGallery.categories = mergedGallery.categories.map((cat) => {
        if (cat && typeof cat === "object" && "slugs" in cat) {
          // Remove nested slugs property
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { slugs: _unused, ...rest } = cat as Record<string, unknown>;
          return rest;
        }
        return cat;
      });
    }
    // Ensure slugs is a top-level array, not nested under categories
    mergedGallery.slugs = slugsArr;
    batch.set(db.collection("live").doc("gallery"), mergedGallery);
    functions.logger.info(
      `Publishing merged gallery (with slugs) to live/gallery`
    );
  }
};

/**
 * Clears preview collections after publish
 */
export const clearPreviewCollections = async (
  batch: admin.firestore.WriteBatch
) => {
  const previewDocs = await db.collection("preview").listDocuments();

  for (const docRef of previewDocs) {
    batch.delete(docRef);
    functions.logger.info(`Deleted preview doc: preview/${docRef.id}`);
  }
};

/**
 * Publish function - creates a backup and updates live content
 * Using onRequest instead of onCall to avoid CORS issues with emulator
 */
export const publishFunction = functions.https.onRequest(async (req, res) => {
  functions.logger.info("Publish function called");
  // Enable CORS
  res.set("Access-Control-Allow-Origin", "*");
  res.set("Access-Control-Allow-Methods", "POST, OPTIONS");
  res.set("Access-Control-Allow-Headers", "Content-Type");
  // Handle preflight OPTIONS request
  if (req.method === "OPTIONS") {
    functions.logger.info("req metod if pre-flightoptions");
    res.status(204).send("");
    return;
  }

  // Only allow POST
  if (req.method !== "POST") {
    functions.logger.info("req metod is post");
    res.status(405).send("Method Not Allowed");
    return;
  }

  try {
    // Parse request body
    const data = req.body.data || {};
    functions.logger.info("Starting publish operation", data);

    // 1. Create atomic snapshot
    const batch = db.batch();
    const versionId = new Date().toISOString();

    // Use standard Date instead of Firestore Timestamp
    // 2. Basic validation (full image reference tracking to be implemented later)
    const { valid, errors } = await validateContentReferences();

    if (!valid) {
      functions.logger.warn("Invalid references found", errors);
      res.status(400).json({
        success: false,
        error: "Invalid references",
        details: errors,
      });
      return;
    }

    // 3. Execute atomic operations
    await createVersionSnapshot(batch, versionId);

    await updateLiveContent(batch);
    // Only clear preview if explicitly requested
    if (data.clearPreview !== false) await clearPreviewCollections(batch);

    // 4. Commit transaction
    await batch.commit();
    // 5. Log audit event
    await logAuditEvent({
      action: "publish",
      user: "admin", // We don't have auth context in onRequest
      version: versionId,
      timestamp: new Date(),
      changes: ["publish operation"],
      metadata: {
        clearPreview: data.clearPreview !== false,
      },
    });
    functions.logger.info("Publish operation completed successfully", {
      versionId,
    });

    // 6. Return success response
    res.status(200).json({
      success: true,
      versionId,
    });
  } catch (error) {
    functions.logger.error("Publish operation failed", error);
    res.status(500).json({
      success: false,
      error: (error as Error).message,
    });
  }
});

/**
 * Rollback function - restores content from a backup
 */
export const rollbackFunction = functions.https.onRequest(async (req, res) => {
  // Enable CORS
  res.set("Access-Control-Allow-Origin", "*");
  res.set("Access-Control-Allow-Methods", "POST, OPTIONS");
  res.set("Access-Control-Allow-Headers", "Content-Type");
  // Handle preflight OPTIONS request
  if (req.method === "OPTIONS") {
    res.status(204).send("");
    return;
  }

  // Only allow POST
  if (req.method !== "POST") {
    res.status(405).send("Method Not Allowed");
    return;
  }

  try {
    // Parse request body
    const { versionId } = req.body.data || {};

    if (!versionId) {
      res.status(400).json({
        success: false,
        error: "Version ID is required",
      });
      return;
    }

    // 1. Verify version exists
    const metaRef = db.collection("backup_meta").doc(versionId);

    const metaSnap = await metaRef.get();

    if (!metaSnap.exists) {
      res.status(404).json({
        success: false,
        error: `Version ${versionId} not found`,
      });
      return;
    }

    const metadata = metaSnap.data()!;
    const batch = db.batch();
    const pages = metadata.pages || [];

    // 2. Restore standard pages
    for (const page of pages) {
      const backupRef = db.collection("backups").doc(`${page}_${versionId}`);

      const backupSnap = await backupRef.get();

      if (backupSnap.exists) {
        // Remove metadata from the content
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { _meta: _, ...contentData } = backupSnap.data()!;

        // Update live content
        batch.set(db.collection("live").doc(page), contentData);
      }
    }


    // 3. Commit transaction
    await batch.commit();
    // 4. Log audit event
    await logAuditEvent({
      action: "rollback",
      user: "admin", // We don't have auth context in onRequest
      version: versionId,
      timestamp: new Date(),
      changes: ["rollback operation"],
      metadata: {
        restoredPages: pages,
      },
    });

    // 5. Return success response
    res.status(200).json({
      success: true,
      versionId,
      restoredPages: pages,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: (error as Error).message,
    });
  }
});
