import React from 'react';
import Image from 'next/image';

interface ProfileCardProps {
    image: string | null;
    name: string;
    bio: React.ReactNode;
    className?: string;
}

export function ProfileCard({image, name, bio, className = ''}: ProfileCardProps) {
    return (
        <div className={`flex flex-col items-center gap-6 ${className}`}>
            <div className="w-40 h-40 md:w-56 md:h-56 relative rounded-full overflow-hidden shadow-lg transition-transform hover:scale-105">
                {image ? (
                    <Image src={image} alt={name} fill className="object-cover" priority/>
                ) : (
                    <div className="w-full h-full bg-elegant-light flex items-center justify-center">
                        <span className="text-elegant-text/50 text-xl">{name.charAt(0)}</span>
                    </div>
                )}
            </div>
            <div className="text-center">
                <h2 className="text-2xl font-semibold mb-2">{name}</h2>
                <div className="text-modernArt-mutedForeground space-y-2">{bio}</div>
            </div>
        </div>
    );
}
