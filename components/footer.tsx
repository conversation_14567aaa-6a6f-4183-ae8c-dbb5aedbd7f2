import { SubscriptionForm } from './SubscriptionForm';
import { usePathname } from 'next/navigation';

export function Footer() {
  const currentYear = new Date().getFullYear();
  const pathname = usePathname();
  const isAdminPage = pathname?.startsWith('/admin');

  return (
    <footer className="py-8 border-t border-modernArt-border bg-modernArt-cardBackground">
      <div className="max-w-4xl mx-auto px-4">
        {/* Subscription Form Section - Hidden on admin pages */}
        {!isAdminPage && (
          <div className="mb-8 pt-4 text-center">
            <h3 className="text-xl font-semibold mb-3 font-cormorant text-modernArt-foreground">Stay Updated</h3>
            <p className="mb-6 text-sm text-modernArt-mutedForeground font-montserrat">
              Subscribe to our newsletter for the latest on new acquisitions, exhibitions, and events.
            </p>
            <SubscriptionForm />
          </div>
        )}

        {/* Existing Footer Content */}
        <div className="flex flex-col items-center justify-center pt-8 border-t border-modernArt-border/50">
          <div className="mb-4">
            <h2 className="font-cormorant text-2xl font-light text-modernArt-foreground">
              Bela Gallery
            </h2>
          </div>
          <div className="w-12 h-px bg-modernArt-primaryAccent mb-4"></div>
          <p className="font-montserrat text-sm text-modernArt-mutedForeground">
            ©2000 - {currentYear} Bela Gallery. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
