import { NextPage } from 'next';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertCircle } from 'lucide-react'; // Icon for warning/invalid

const UnsubscribeInvalidTokenPage: NextPage = () => {
  return (
    <div className="container mx-auto px-4 py-12 flex justify-center items-center min-h-[calc(100vh-200px)]">
      <Card className="w-full max-w-md text-center">
        <CardHeader className="items-center">
          <AlertCircle className="w-12 h-12 text-yellow-500 mb-2" />
          <CardTitle>Invalid Link</CardTitle>
          <CardDescription>The unsubscription link is invalid or has expired.</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-6">
            We could not process your unsubscription request using this link.
            This might be because the link is incorrect, has already been used, or has expired.
          </p>
          <p className="text-sm text-gray-500 mb-4">
            If you need assistance, please contact support or try unsubscribing through a recent email if available.
          </p>
          <Button asChild>
            <Link href="/">Return to Homepage</Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default UnsubscribeInvalidTokenPage;
