import { doc, getDoc } from "firebase/firestore";
import { db } from "./firebase";

/**
 * Fetches published data for a collection/docId.
 * Tries versioned snapshot first, falls back to live content.
 */
export async function getPublished<T>(
  collection: string,
  docId: string
): Promise<T | null> {
  // Preview draft override, only when ?preview=true
  if (typeof window !== "undefined") {
    const p = new URLSearchParams(window.location.search).get("preview");

    if (p === "true") {
      // For the new structure, we need to handle preview differently
      // If we're looking for 'live/home', we need to check 'preview/home'
      const previewDocId = docId;
      const previewRef = doc(db, "preview", previewDocId);
      const previewSnap = await getDoc(previewRef);

      if (previewSnap.exists()) return previewSnap.data() as T;
    }
  }

  // Fetch meta/version
  const metaRef = doc(db, "meta", "version");
  const metaSnap = await getDoc(metaRef);
  const meta = metaSnap.exists() ? metaSnap.data() : null;
  const version = meta?.current_version;

  if (version) {
    // Try version snapshot
    const verRef = doc(db, "versions", version, collection, docId);
    const verSnap = await getDoc(verRef);

    if (verSnap.exists()) return verSnap.data() as T;
  }

  // Fallback to live
  const liveRef = doc(db, collection, docId);
  const liveSnap = await getDoc(liveRef);

  if (liveSnap.exists()) return liveSnap.data() as T;

  return null;
}
