import { POST } from './route'; // Adjust if your handler is named differently
import { NextRequest } from 'next/server';
import { adminDb } from '@/lib/firebase-admin';
import { sendEmail } from '@/lib/email';
import { v4 as uuidv4 } from 'uuid';

// Mocks
jest.mock('@/lib/firebase-admin', () => ({
  adminDb: {
    collection: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    get: jest.fn(),
    add: jest.fn(),
  },
}));
jest.mock('@/lib/email', () => ({
  sendEmail: jest.fn(),
  createConfirmationEmailHtml: jest.fn(() => '<html>Mock Email</html>'),
}));
jest.mock('uuid', () => ({ v4: jest.fn() }));

// Mock process.env
const originalEnv = process.env;
beforeEach(() => {
  process.env = {
    ...originalEnv,
    NEXT_PUBLIC_BASE_URL: 'http://localhost:3000',
    GMAIL_EMAIL: '<EMAIL>', // Mocked so sendEmail doesn't use console log path
    GMAIL_APP_PASSWORD: 'password'
  };
  jest.clearAllMocks();
});
afterEach(() => {
  process.env = originalEnv;
});


describe('POST /api/subscribe', () => {
  it('should subscribe a new email and send confirmation', async () => {
    (adminDb.get as jest.Mock).mockResolvedValueOnce({ empty: true, docs: [] }); // No existing subscriber
    (adminDb.add as jest.Mock).mockResolvedValueOnce({ id: 'new-doc-id' });
    (uuidv4 as jest.Mock).mockReturnValueOnce('test-token');
    (sendEmail as jest.Mock).mockResolvedValueOnce(undefined);

    const request = new NextRequest('http://localhost/api/subscribe', {
      method: 'POST',
      body: JSON.stringify({ email: '<EMAIL>' }),
    });

    const response = await POST(request);
    const body = await response.json();

    expect(response.status).toBe(201);
    expect(body.message).toBe('Subscription successful! Please check your email to confirm.');
    expect(adminDb.collection).toHaveBeenCalledWith('subscribers');
    expect(adminDb.add).toHaveBeenCalledWith(expect.objectContaining({
      email: '<EMAIL>',
      isConfirmed: false,
      confirmationToken: 'test-token',
    }));
    expect(sendEmail).toHaveBeenCalledWith(expect.objectContaining({
      to: '<EMAIL>',
      subject: expect.stringContaining('Confirm Your Subscription'),
    }));
  });

  it('should return 400 if email is missing', async () => {
    const request = new NextRequest('http://localhost/api/subscribe', {
      method: 'POST',
      body: JSON.stringify({}),
    });
    const response = await POST(request);
    const body = await response.json();
    expect(response.status).toBe(400);
    expect(body.error).toBe('Email is required');
  });

  it('should return 400 if email format is invalid', async () => {
    const request = new NextRequest('http://localhost/api/subscribe', {
      method: 'POST',
      body: JSON.stringify({ email: 'invalid-email' }),
    });
    const response = await POST(request);
    const body = await response.json();
    expect(response.status).toBe(400);
    expect(body.error).toBe('Invalid email format');
  });

  it('should inform if email is already subscribed and confirmed', async () => {
    (adminDb.get as jest.Mock).mockResolvedValueOnce({
      empty: false,
      docs: [{ data: () => ({ email: '<EMAIL>', isConfirmed: true }) }],
    });
    const request = new NextRequest('http://localhost/api/subscribe', {
      method: 'POST',
      body: JSON.stringify({ email: '<EMAIL>' }),
    });
    const response = await POST(request);
    const body = await response.json();
    expect(response.status).toBe(200);
    expect(body.message).toBe('Email already subscribed and confirmed');
  });

   it('should inform if email is already on list but pending confirmation', async () => {
    (adminDb.get as jest.Mock).mockResolvedValueOnce({
      empty: false,
      docs: [{ data: () => ({ email: '<EMAIL>', isConfirmed: false }) }],
    });
     const request = new NextRequest('http://localhost/api/subscribe', {
      method: 'POST',
      body: JSON.stringify({ email: '<EMAIL>' }),
    });
    const response = await POST(request);
    const body = await response.json();
    expect(response.status).toBe(200);
    expect(body.message).toBe('Email already on the list, pending confirmation');
  });


  it('should handle errors during subscription', async () => {
    (adminDb.get as jest.Mock).mockRejectedValueOnce(new Error('Firestore error'));
    const request = new NextRequest('http://localhost/api/subscribe', {
      method: 'POST',
      body: JSON.stringify({ email: '<EMAIL>' }),
    });
    const response = await POST(request);
    const body = await response.json();
    expect(response.status).toBe(500);
    expect(body.error).toBe('Firestore error');
  });
});
