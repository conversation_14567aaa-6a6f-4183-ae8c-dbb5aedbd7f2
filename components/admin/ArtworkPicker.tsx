"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Artwork } from "@/types/artworks";
import { useArtworks } from "@/hooks/use-artwork";
import { useImageResolver } from "@/hooks/use-image-resolver";
import { ArtworkGrid } from "@/components/artwork";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Filter,
  Grid,
  List,
  X,
  ChevronUp,
  ChevronDown,
} from "lucide-react";

interface ArtworkPickerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelect: (artwork: Artwork) => void;
  selectedId?: string;
  mode?: "single" | "multiple";
}

/**
 * A component for selecting artworks from the repository
 */
export function ArtworkPicker({
  open,
  onOpenChange,
  onSelect,
  selectedId,
}: ArtworkPickerProps) {
  const { artworks, isLoading } = useArtworks();
  const { resolveImages } = useImageResolver();

  // UI state
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<
    "title" | "year" | "createdAt" | "updatedAt"
  >("title");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [filterCategory, setFilterCategory] = useState<string>("");
  const [filterTags, setFilterTags] = useState<string[]>([]);
  const [selectedArtwork, setSelectedArtwork] = useState<Artwork | null>(null);

  // Derived state
  const categories = [...new Set(artworks.map((artwork) => artwork.category))]
    .filter(Boolean)
    .sort();

  const allTags = [
    ...new Set(artworks.flatMap((artwork) => artwork.tags || [])),
  ].sort();

  // Resolve all artwork images
  useEffect(() => {
    if (artworks.length > 0) {
      const imagePaths = artworks
        .map((artwork) => artwork.image)
        .filter(Boolean);

      resolveImages(imagePaths);
    }
  }, [artworks, resolveImages]);

  // Set initial selected artwork based on selectedId
  useEffect(() => {
    if (selectedId && artworks.length > 0) {
      const artwork = artworks.find((a) => a.id === selectedId);

      if (artwork) setSelectedArtwork(artwork);
    }
  }, [selectedId, artworks]);

  // Filter and sort artworks
  const filteredArtworks = artworks
    .filter((artwork) => {
      // Search term filter
      if (
        searchTerm &&
        !artwork.title.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !artwork.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
        return false;

      // Category filter
      if (filterCategory && artwork.category !== filterCategory) return false;

      // Tags filter
      return (
        filterTags.length <= 0 ||
        filterTags.some((tag) => artwork.tags?.includes(tag))
      );
    })
    .sort((a, b) => {
      // Handle sorting
      if (sortBy === "title")
        return sortDirection === "asc"
          ? a.title.localeCompare(b.title)
          : b.title.localeCompare(a.title);

      if (sortBy === "year")
        return sortDirection === "asc"
          ? a.year.localeCompare(b.year)
          : b.year.localeCompare(a.year);

      if (sortBy === "createdAt") {
        const aDate = a.createdAt ? new Date(a.createdAt).getTime() : 0;
        const bDate = b.createdAt ? new Date(b.createdAt).getTime() : 0;

        return sortDirection === "asc" ? aDate - bDate : bDate - aDate;
      }

      if (sortBy === "updatedAt") {
        const aDate = a.updatedAt ? new Date(a.updatedAt).getTime() : 0;
        const bDate = b.updatedAt ? new Date(b.updatedAt).getTime() : 0;

        return sortDirection === "asc" ? aDate - bDate : bDate - aDate;
      }

      return 0;
    });

  const toggleSortDirection = () => {
    setSortDirection(sortDirection === "asc" ? "desc" : "asc");
  };

  const addFilterTag = (tag: string) => {
    if (!filterTags.includes(tag)) setFilterTags([...filterTags, tag]);
  };

  const removeFilterTag = (tag: string) => {
    setFilterTags(filterTags.filter((t) => t !== tag));
  };

  const clearFilters = () => {
    setSearchTerm("");
    setFilterCategory("");
    setFilterTags([]);
  };

  const handleSelect = (artwork: Artwork) => {
    setSelectedArtwork(artwork);
  };

  const handleConfirm = () => {
    if (selectedArtwork) {
      onSelect(selectedArtwork);
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Select Artwork</DialogTitle>
        </DialogHeader>
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search artworks..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <div className="flex gap-2">
              <Select
                value={sortBy}
                onValueChange={(value: unknown) => setSortBy(value)}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="title">Title</SelectItem>
                  <SelectItem value="year">Year</SelectItem>
                  <SelectItem value="createdAt">Date Added</SelectItem>
                  <SelectItem value="updatedAt">Last Updated</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="icon"
                onClick={toggleSortDirection}
              >
                {sortDirection === "asc" ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() =>
                  setViewMode(viewMode === "grid" ? "list" : "grid")
                }
              >
                {viewMode === "grid" ? (
                  <List className="h-4 w-4" />
                ) : (
                  <Grid className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Filters</span>
              {(filterCategory || filterTags.length > 0) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilters}
                  className="ml-auto h-7 px-2"
                >
                  <X className="h-3 w-3 mr-1" />
                  Clear
                </Button>
              )}
            </div>
            <div className="flex flex-wrap gap-2">
              <Select value={filterCategory} onValueChange={setFilterCategory}>
                <SelectTrigger className="w-[180px] h-8">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Categories</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select
                value=""
                onValueChange={(value) => {
                  if (value) addFilterTag(value);
                }}
              >
                <SelectTrigger className="w-[180px] h-8">
                  <SelectValue placeholder="Add Tag Filter" />
                </SelectTrigger>
                <SelectContent>
                  {allTags
                    .filter((tag) => !filterTags.includes(tag))
                    .map((tag) => (
                      <SelectItem key={tag} value={tag}>
                        {tag}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
            {filterTags.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {filterTags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {tag}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => removeFilterTag(tag)}
                    />
                  </Badge>
                ))}
              </div>
            )}
          </div>
          <div className="flex-1 overflow-auto">
            {isLoading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                <p>Loading artworks...</p>
              </div>
            ) : filteredArtworks.length === 0 ? (
              <div className="text-center py-12 text-muted-foreground">
                No artworks found matching your filters.
              </div>
            ) : viewMode === "grid" ? (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {filteredArtworks.map((artwork) => (
                  <Card
                    key={artwork.id}
                    className={`overflow-hidden cursor-pointer transition-all ${
                      selectedArtwork?.id === artwork.id
                        ? "ring-4 ring-primary"
                        : "hover:ring-2 hover:ring-primary/50"
                    }`}
                    onClick={() => handleSelect(artwork)}
                  >
                    <ArtworkGrid
                      artworks={[artwork]}
                      mode="minimal"
                      columns={1}
                      gap={0}
                    />
                  </Card>
                ))}
              </div>
            ) : (
              <div className="space-y-2">
                {filteredArtworks.map((artwork) => (
                  <Card
                    key={artwork.id}
                    className={`overflow-hidden cursor-pointer transition-all ${
                      selectedArtwork?.id === artwork.id
                        ? "bg-primary/10"
                        : "hover:bg-gray-50"
                    }`}
                    onClick={() => handleSelect(artwork)}
                  >
                    <div className="flex p-2">
                      <div className="w-16 h-16 relative flex-shrink-0">
                        <ArtworkGrid
                          artworks={[artwork]}
                          mode="minimal"
                          columns={1}
                          gap={0}
                        />
                      </div>
                      <div className="flex-1 ml-3">
                        <h3 className="font-medium">{artwork.title}</h3>
                        <p className="text-xs text-muted-foreground">
                          {artwork.year} • {artwork.medium}
                        </p>
                        {artwork.tags && artwork.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-1">
                            {artwork.tags.slice(0, 3).map((tag) => (
                              <Badge
                                key={tag}
                                variant="outline"
                                className="text-xs"
                              >
                                {tag}
                              </Badge>
                            ))}
                            {artwork.tags.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{artwork.tags.length - 3}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>
        <div className="flex justify-between items-center mt-4">
          <div className="text-sm text-muted-foreground">
            {selectedArtwork
              ? `Selected: ${selectedArtwork.title}`
              : "No artwork selected"}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button onClick={handleConfirm} disabled={!selectedArtwork}>
              Select Artwork
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
