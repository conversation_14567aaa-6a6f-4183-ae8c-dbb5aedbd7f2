/**
 * Cross-platform script to stop Firebase emulators
 * Works on Windows, macOS, and Linux
 */

import { execSync } from 'child_process';
import { platform } from 'os';
import { readFileSync } from 'fs';
import { dirname, join } from 'path';

interface FirebaseConfig {
  emulators?: {
    firestore?: {
      port: number;
    };
    storage?: {
      port: number;
    };
  };
}

function getPortsFromConfig(configPath: string): { firestorePort: number; storagePort: number } {
  try {
    // Get the directory where this script is located
    const scriptDir = dirname(__filename);
    // Resolve config path relative to the project root (parent of scripts directory)
    const projectRoot = dirname(scriptDir);
    const fullConfigPath = join(projectRoot, configPath);
    
    const configFile = readFileSync(fullConfigPath, 'utf8');
    const config: FirebaseConfig = JSON.parse(configFile);
    
    const firestorePort = config.emulators?.firestore?.port || 8080;
    const storagePort = config.emulators?.storage?.port || 9199;
    
    return { firestorePort, storagePort };
  } catch (error) {
    console.error(`Error reading config file ${configPath}:`, error);
    // Fallback to default ports
    return { firestorePort: 8080, storagePort: 9199 };
  }
}

function stopEmulatorsForPorts(ports: number[], currentPlatform: string): void {
  for (const port of ports) {
    try {
      if (currentPlatform === 'win32') {
        // Windows
        execSync(`for /f "tokens=5" %a in ('netstat -ano ^| findstr :${port}') do taskkill /F /PID %a`, { stdio: 'inherit' });
      } else {
        // macOS and Linux
        execSync(`lsof -t -i:${port} | xargs -r kill -9`, { stdio: 'inherit' });
      }
    } catch {
      // Ignore errors for individual ports - they might not be running
    }
  }
}

function stopEmulator(configPath?: string): void {
  const currentPlatform = platform();
  console.log(`Stopping Firebase emulators on ${currentPlatform}...`);

  if (configPath) {
    // Stop emulators for specific config
    const { firestorePort, storagePort } = getPortsFromConfig(configPath);
    console.log(`Using config: ${configPath}`);
    console.log(`Firestore port: ${firestorePort}, Storage port: ${storagePort}`);
    
    stopEmulatorsForPorts([firestorePort, storagePort], currentPlatform);
  } else {
    // Stop emulators for both configs
    console.log('No config specified - stopping emulators for firebase.json and firebase.test.json');
    
    const prodPorts = getPortsFromConfig('firebase.json');
    const testPorts = getPortsFromConfig('firebase.test.json');
    
    console.log(`Production ports - Firestore: ${prodPorts.firestorePort}, Storage: ${prodPorts.storagePort}`);
    console.log(`Test ports - Firestore: ${testPorts.firestorePort}, Storage: ${testPorts.storagePort}`);
    
    const allPorts = [
      prodPorts.firestorePort,
      prodPorts.storagePort,
      testPorts.firestorePort,
      testPorts.storagePort
    ];
    
    // Remove duplicates
    const uniquePorts = [...new Set(allPorts)];
    stopEmulatorsForPorts(uniquePorts, currentPlatform);
  }

  console.log('Firebase emulators stopped successfully');
}

// Get config path from command line arguments
const configPath = process.argv[2];
stopEmulator(configPath);