"use client";

import Link from 'next/link';
import {usePathname} from 'next/navigation';
import {useState, useEffect} from 'react';
import {useIsMobile} from '@/hooks/use-mobile';
import {Button} from '@/components/ui/button';
import {
    Menu,
    X,
    LogOut,
} from 'lucide-react';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import {
    HoverCard,
    HoverCardContent,
    HoverCardTrigger,
} from '@/components/ui/hover-card';
import {Separator} from '@/components/ui/separator';
import {useAdminAuth} from '@/lib/useAdminAuth';
import {getPublished} from '@/lib/firestoreHelpers';
import {getAllCategories} from '@/lib/gallery';

export function Header() {
    const {user, signOut} = useAdminAuth();
    const pathname = usePathname();
    const isMobile = useIsMobile();
    const [menuOpen, setMenuOpen] = useState(false);
    
    type NavLink = {
        href: string;
        label: string;
    };
    type NavDropdown = {
        label: string;
        dropdown: ({
            href: string;
            label: string;
        } | {
            type: 'separator';
        })[];
    };
    // Dynamic top-level menu & gallery categories from Firestore
    type MenuItem = {
        label: string;
        href: string;
    };

    const [menuItems, setMenuItems] = useState<MenuItem[]>([{
        label: 'Home',
        href: '/',
    }, {
        label: 'Gallery',
        href: '/gallery',
    }, {
        label: 'Events',
        href: '/events',
    }, {
        label: 'About',
        href: '/about',
    }, {
        label: 'Contact',
        href: '/contact',
    }]);
    
    const [galleryItemsList, setGalleryItemsList] = useState<MenuItem[]>([]);
    
    useEffect(() => {
        async function initMenu() {
            try {
                const menuData = await getPublished('menu', 'content');
                
                if (menuData?.items)
                    setMenuItems(menuData.items);
            } catch {}
        }
        
        initMenu();
    }, []);
    
    useEffect(() => {
        async function initGallery() {
            try {
                // Fetch categories from Firestore using getAllCategories
                const categories = await getAllCategories();
                
                if (categories?.length > 0)
                    setGalleryItemsList(categories.map((c: {
                        title: string;
                        slug: string;
                    }) => ({
                        label: c.title,
                        href: `/gallery/${c.slug}`,
                    })));
            } catch {}
        }
        
        initGallery();
    }, []);

    // Prepare gallery dropdown items: always include categories
    const galleryDropdown = [{
        href: '/gallery',
        label: 'All works',
    }, {
        type: 'separator' as const,
    },
...galleryItemsList];
    
    const navLinks: (NavLink | NavDropdown)[] = menuItems.map((item) => item.href === '/gallery' ? {
        label: item.label,
        dropdown: galleryDropdown,
    } : {
        href: item.href,
        label: item.label,
    });
    
    return (
      <header
        className={`sticky top-0 z-50 bg-modernArt-cardBackground/95 backdrop-blur-sm border-b border-modernArt-border/50 shadow-sm ${
          isMobile ? "px-6 pt-4 pb-4" : "px-6 py-4"
        }`}
      >
        <div
          className={`container max-w-6xl mx-auto flex ${
            isMobile ? "flex-col" : "justify-between items-center"
          }`}
        >
          {/* Logo and Mobile Menu Toggle */}
          <div
            className={`flex justify-between items-center ${
              isMobile ? "w-full mb-4" : ""
            }`}
          >
            <Link href="/" className="group">
              <div className="flex flex-col items-start">
                <h1 className="text-4xl font-cormorant font-normal text-modernArt-foreground mb-0 tracking-wide transition-transform group-hover:scale-105">
                  Bela
                </h1>
                <div className="w-full h-px bg-modernArt-primaryAccent mb-1 transform origin-left transition-transform group-hover:scale-x-110"></div>
                <p className="text-modernArt-mutedForeground font-montserrat text-xs uppercase tracking-wider transition-transform group-hover:scale-105">
                  Art Gallery
                </p>
              </div>
            </Link>
            {isMobile && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setMenuOpen(!menuOpen)}
                aria-label="Toggle menu"
                className="text-modernArt-foreground hover:bg-modernArt-secondaryAccent/60"
              >
                {menuOpen ? <X size={24} className="text-modernArt-primaryAccent" /> : <Menu size={24} className="text-modernArt-foreground" />}
              </Button>
            )}
          </div>
          {/* Navigation Links - Conditionally rendered based on mobile state and menuOpen */}
          {/* Apply flex styles directly here for desktop and mobile column */}
          <nav
            className={String(
              isMobile
                ? menuOpen
                  ? "flex flex-col space-y-4"
                  : "hidden"
                : "flex space-x-10 items-center"
            )}
          >
            {navLinks.map(
              (link) =>
                "dropdown" in link ? (
                  isMobile ? (
                    <DropdownMenu key={link.label}>
                      <DropdownMenuTrigger
                        className={`font-montserrat text-sm uppercase tracking-wider text-modernArt-foreground/90 hover:text-modernArt-primaryAccent data-[state=open]:text-modernArt-primaryAccent ${
                          isMobile ? "w-full text-left" : ""
                        }`}
                      >
                        {link.label}
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        className={`${isMobile ? "w-[calc(100vw-3rem)] ml-4" : ""} border-modernArt-border bg-modernArt-cardBackground/95 backdrop-blur-sm`}
                      >
                        {link.dropdown.map((item, idx) => {
                          if ("type" in item)
                            return (
                              <Separator
                                key={`separator-${idx}`}
                                className="my-1 bg-modernArt-border"
                              />
                            );

                          return (
                            <DropdownMenuItem key={item.href} asChild className="focus:bg-modernArt-primaryAccent/20">
                              <Link
                                href={item.href}
                                className={`w-full font-montserrat text-sm modern-link-underline ${
                                  pathname === item.href
                                    ? "text-modernArt-primaryAccent font-semibold"
                                    : "text-modernArt-foreground hover:text-modernArt-primaryAccent"
                                }`}
                                onClick={() => isMobile && setMenuOpen(false)}
                              >
                                {item.label}
                              </Link>
                            </DropdownMenuItem>
                          );
                        })}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  ) : (
                    <HoverCard key={link.label} openDelay={50} closeDelay={100}>
                      <HoverCardTrigger asChild>
                        <a
                          className="font-montserrat text-sm uppercase tracking-wider text-modernArt-foreground/90 hover:text-modernArt-primaryAccent modern-link-underline cursor-default"
                          href="#"
                          onClick={(e) => e.preventDefault()}
                        >
                          {link.label}
                        </a>
                      </HoverCardTrigger>
                      <HoverCardContent className="w-auto p-1 border-modernArt-border bg-modernArt-cardBackground/95 backdrop-blur-sm">
                        {link.dropdown.map((item, itemIndex) => {
                          if ("type" in item)
                            return (
                              <Separator
                                key={`separator-${itemIndex}`}
                                className="my-1 bg-modernArt-border"
                              />
                            );

                          return (
                            <Link
                              key={item.href}
                              href={item.href}
                              className={`block px-3 py-2 text-sm rounded-sm transition-colors font-montserrat ${
                                pathname === item.href
                                  ? "text-modernArt-primaryAccent font-semibold bg-modernArt-primaryAccent/20"
                                  : "text-modernArt-foreground hover:text-modernArt-primaryAccent hover:bg-modernArt-secondaryAccent/60"
                              }`}
                            >
                              {item.label}
                            </Link>
                          );
                        })}
                      </HoverCardContent>
                    </HoverCard>
                  )
                ) : (
                  <Link
                    key={link.href}
                    href={link.href}
                    className={`font-montserrat text-sm uppercase tracking-wider transition-colors modern-link-underline ${
                      pathname === link.href
                        ? "text-modernArt-primaryAccent font-semibold"
                        : "text-modernArt-foreground/90 hover:text-modernArt-primaryAccent"
                    }`}
                    onClick={() => isMobile && setMenuOpen(false)}
                  >
                    {link.label}
                  </Link>
                )
            )}
            {user && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="text-modernArt-mutedForeground hover:text-modernArt-foreground focus:ring-modernArt-primaryAccent">
                    Admin
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="border-modernArt-border bg-modernArt-cardBackground text-modernArt-foreground">
                  <DropdownMenuItem asChild className="focus:bg-modernArt-secondaryAccent focus:text-modernArt-foreground">
                    <Link href="/admin">Dashboard</Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator className="bg-modernArt-border" />
                  <DropdownMenuItem asChild className="focus:bg-modernArt-secondaryAccent focus:text-modernArt-foreground">
                    <Link href="/admin/newsletter">Newsletter</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild className="focus:bg-modernArt-secondaryAccent focus:text-modernArt-foreground">
                    <Link href="/admin/artworks">Artwork Repository</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild className="focus:bg-modernArt-secondaryAccent focus:text-modernArt-foreground">
                    <Link href="/admin/versions">Version Management</Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator className="bg-modernArt-border" />
                  <DropdownMenuItem asChild className="focus:bg-modernArt-secondaryAccent focus:text-modernArt-foreground">
                    <Link href="/admin/home">Home Editor</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild className="focus:bg-modernArt-secondaryAccent focus:text-modernArt-foreground">
                    <Link href="/admin/gallery">Gallery Editor</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild className="focus:bg-modernArt-secondaryAccent focus:text-modernArt-foreground">
                    <Link href="/admin/events">Events Editor</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild className="focus:bg-modernArt-secondaryAccent focus:text-modernArt-foreground">
                    <Link href="/admin/about">About Editor</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild className="focus:bg-modernArt-secondaryAccent focus:text-modernArt-foreground">
                    <Link href="/admin/contact">Contact Editor</Link>
                  </DropdownMenuItem>
                  {/* <DropdownMenuItem asChild>
                    <Link href="/admin/menu">Menu Editor</Link>
                  </DropdownMenuItem> */}
                  <DropdownMenuSeparator className="bg-modernArt-border" />
                  <DropdownMenuItem
                    onClick={() => signOut()}
                    className="text-red-500 hover:text-red-600 focus:bg-red-100 focus:text-red-700 cursor-pointer"
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    Sign Out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </nav>
        </div>
      </header>
    );
}
