"use client";

import { useState, useEffect } from "react";
import { fetchContent } from "@/lib/content-repository";
import { logError } from "@/lib/errorLogger";

// Helper to check if value is an Error
const isError = (value: unknown): value is Error => value instanceof Error;

interface UsePublicContentOptions<T> {
  collection: string;
  documentId: string;
  defaultValue: T;
  previewMode?: boolean;
  transformData?: (data: unknown) => T;
}

interface UsePublicContentReturn<T> {
  content: T;
  loading: boolean;
  error: Error | null;
}

/**
 * Hook for fetching content for public-facing pages
 * Supports preview mode with fallback to live content
 */
export function usePublicContent<T>({
  collection,
  documentId,
  defaultValue,
  previewMode = false,
  transformData
}: UsePublicContentOptions<T>): UsePublicContentReturn<T> {
  const [content, setContent] = useState<T>(defaultValue);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    async function loadContent() {
      setLoading(true);
      setError(null);
      
      try {
        // In preview mode, check preview collection first, then fall back to live
        // In normal mode, just load from live collection
        const primaryCollection = previewMode ? "preview" : "live";
        const fallbackCollection = previewMode ? "live" : undefined;
        
        const data = await fetchContent<T>(
          primaryCollection,
          documentId,
          {
            fallbackCollection,
            fallbackDocId: fallbackCollection ? documentId : undefined,
            defaultValue,
            transformData
          }
        );
        
        setContent(data);
      } catch (err) {
        const error = isError(err) ? err : new Error(String(err));
        setError(error);
        logError(error, "usePublicContent.loadContent", {
          context: { collection, documentId, previewMode }
        });
      } finally {
        setLoading(false);
      }
    }
    
    loadContent();
  }, [collection, documentId, previewMode]); // eslint-disable-line react-hooks/exhaustive-deps -- defaultValue and transformData dependencies would cause infinite re-renders

  return {
    content,
    loading,
    error
  };
}