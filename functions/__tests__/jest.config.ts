import type { Config } from 'jest';

const config: Config = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  testMatch: ['**/__tests__/**/*.test.ts'],
  collectCoverage: true,
  collectCoverageFrom: ['../src/**/*.ts', '!../src/index.ts'],
  coverageDirectory: '../../test/output/coverage/functions',
  coverageReporters: ['json', 'lcov', 'text', 'clover', 'html'],
  setupFiles: ['./unit/setup.ts'],
  testTimeout: 10000,
  transform: {
    '^.+\\.tsx?$': ['ts-jest', {
      isolatedModules: true,
    }],
  },
};

export default config;