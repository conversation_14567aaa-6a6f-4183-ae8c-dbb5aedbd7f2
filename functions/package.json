{"name": "functions", "engines": {"node": "22"}, "main": "lib/src/index.js", "scriptsComment": {"Build": {"build": "Compile TypeScript to JavaScript"}, "Firebase Emulators": {"em:start": "Start emulators with data import and TypeScript watch", "em:start:clean": "Start emulators without data import and TypeScript watch", "em:stop": "Stop all Firebase emulators (both prod and test ports)", "em:stop:app": "Stop Firebase emulators for production config", "em:stop:test": "Stop Firebase emulators for test config", "em:exp": "Exp emulator data to local files"}, "Testing": {"test": "<PERSON><PERSON> to run unit tests", "test:unit": "Run unit tests", "test:int": "Run int tests with Firebase emulators", "test:int:no-em": "Run int tests without starting emulators", "test:all": "Run all tests (unit and int)", "test:unit:w": "Run unit tests in w mode", "test:int:w": "Run int tests in w mode"}}, "scripts": {"build": "tsc", "em:start": "tsc --watch & firebase emulators:start --only functions,firestore,storage,hosting --project belagallery-9e01b --import=./firebase-data", "em:start:clean": "tsc --watch &  firebase emulators:start --only functions,firestore,storage,hosting --project belagallery-9e01b", "em:stop": "tsx ../scripts/stop-emulator.ts", "em:stop:app": "tsx ../scripts/stop-emulator.ts firebase.json", "em:stop:test": "tsx ../scripts/stop-emulator.ts firebase.test.json", "em:exp": "firebase emulators:export ./firebase-data --project belagallery-9e01b", "test": "npm run test:unit", "test:unit": "jest --config __tests__/jest.config.unit.ts ${TEST_ARGS:-}", "test:int": "firebase emulators:exec --only firestore,storage --project belagallery-test --config ../firebase.test.json \"jest --config __tests__/jest.config.integration.ts ${TEST_ARGS:-}\"", "test:int:no-em": "jest --config __tests__/jest.config.integration.ts ${TEST_ARGS:-}", "test:all": "npm-run-all --parallel test:unit test:int && echo '\n=== Functions Test Coverage Summary ===' && echo 'Note: For combined project coverage, run npm run test:all from project root'", "test:unit:w": "npm run test:unit -- --watch", "test:int:w": "npm run test:int -- --watch"}, "dependencies": {"cors": "^2.8.5", "firebase-admin": "^12.6.0", "firebase-functions": "^6.1.1", "sharp": "^0.34.1"}, "devDependencies": {"@firebase/rules-unit-testing": "^4.0.1", "@types/jest": "^29.5.0", "firebase-functions-test": "^3.1.0", "jest": "^29.7.0", "npm-run-all": "^4.1.5", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}