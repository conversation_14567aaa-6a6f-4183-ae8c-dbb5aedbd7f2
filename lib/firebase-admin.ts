import * as admin from 'firebase-admin';
import { readFileSync } from 'fs';

// Ensure your service account key JSON file is correctly referenced
// and that GOOGLE_APPLICATION_CREDENTIALS environment variable is set,
// or initialize with credentials explicitly.

const serviceAccountPath = process.env.FIREBASE_ADMIN_SDK_SERVICE_ACCOUNT_PATH;
const useEmulator = process.env.NEXT_PUBLIC_FIREBASE_USE_EMULATOR === 'true';

// Set emulator environment variables if using emulator
if (useEmulator) {
  process.env.FIRESTORE_EMULATOR_HOST = 'localhost:8080';
}

if (!admin.apps.length) {
  if (useEmulator) {
    // For emulator environment - use minimal credentials
    admin.initializeApp({
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    });
  } else if (serviceAccountPath) {
    // For local development or specific environments where path is set
    try {
      // Use fs to read the service account file
      const serviceAccountData = readFileSync(serviceAccountPath, 'utf8');
      const serviceAccount = JSON.parse(serviceAccountData);
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        // Add your databaseURL if needed, though often not required with Firestore
        // databaseURL: `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`
      });
    } catch (error) {
      console.error('Failed to load service account:', error);
      throw new Error('Failed to initialize Firebase Admin SDK with service account');
    }
  } else if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
    // For environments like Google Cloud Run/Functions where GOOGLE_APPLICATION_CREDENTIALS is set
     admin.initializeApp({
        credential: admin.credential.applicationDefault(),
     });
  }
  else {
    // Fallback for environments where service account might be implicitly available
    // or for local emulators if configured to work without explicit credentials.
    // This might require specific emulator setups or rely on gcloud CLI auth.
    console.warn("Firebase Admin SDK: Attempting to initialize without explicit credentials. This may not work in all environments. Ensure GOOGLE_APPLICATION_CREDENTIALS is set or provide a serviceAccountPath.");
    admin.initializeApp();
  }
}

export const adminDb = admin.firestore();
export const adminAuth = admin.auth();
export const adminStorage = admin.storage();
