import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import { publishFunction, rollbackFunction } from "./publish";
import { listVersions, deleteVersion } from "./backup";
import { subscribeFunction, confirmSubscriptionFunction, unsubscribeFunction } from "./subscribers";
import {
  sendNewsletterFunction,
  getNewsletterHistoryFunction,
  getNewsletterArtworksFunction,
  getNewsletterEventsFunction
} from "./newsletter";
import { testEmulatorFunction } from "./utils";

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp();
}

// Export existing functions
export const publish = publishFunction;
export const rollback = rollbackFunction;

// Export subscription functions
export const subscribe = subscribeFunction;
export const confirmSubscription = confirmSubscriptionFunction;
export const unsubscribe = unsubscribeFunction;

// Export newsletter functions
export const sendNewsletter = sendNewsletterFunction;
export const getNewsletterHistory = getNewsletterHistoryFunction;
export const getNewsletterArtworks = getNewsletterArtworksFunction;
export const getNewsletterEvents = getNewsletterEventsFunction;

// Export utility functions
export const testEmulator = testEmulatorFunction;

// Version management functions
export const listVersionsFunction = functions.https.onRequest(
  async (req, res) => {
    // Enable CORS
    res.set("Access-Control-Allow-Origin", "*");
    res.set("Access-Control-Allow-Methods", "POST, OPTIONS");
    res.set("Access-Control-Allow-Headers", "Content-Type");

    // Handle preflight OPTIONS request
    if (req.method === "OPTIONS") {
      res.status(204).send("");
      return;
    }

    // Only allow POST
    if (req.method !== "POST") {
      res.status(405).send("Method Not Allowed");
      return;
    }

    try {
      const versions = await listVersions();
      res.status(200).json({
        success: true,
        versions,
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: (error as Error).message,
      });
    }
  }
);

export const deleteVersionFunction = functions.https.onRequest(
  async (req, res) => {
    // Enable CORS
    res.set("Access-Control-Allow-Origin", "*");
    res.set("Access-Control-Allow-Methods", "POST, OPTIONS");
    res.set("Access-Control-Allow-Headers", "Content-Type");

    // Handle preflight OPTIONS request
    if (req.method === "OPTIONS") {
      res.status(204).send("");
      return;
    }

    // Only allow POST
    if (req.method !== "POST") {
      res.status(405).send("Method Not Allowed");
      return;
    }

    try {
      const { versionId } = req.body.data || {};

      if (!versionId) {
        res.status(400).json({
          success: false,
          error: "Version ID is required",
        });
        return;
      }

      await deleteVersion(versionId);
      res.status(200).json({
        success: true,
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: (error as Error).message,
      });
    }
  }
);