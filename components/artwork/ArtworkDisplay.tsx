"use client";

import React from "react";
import Image from "next/image";
import { Artwork, ArtworkDisplayMode } from "@/types/artworks";
import { useArtwork } from "@/hooks/use-artwork";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

const isString = (a: unknown): a is string => typeof a === "string";

interface ArtworkDisplayProps {
  artwork: Artwork | string;

  // Can be an artwork object or an ID
  mode?: ArtworkDisplayMode;
  className?: string;
  resolvedUrls?: Record<string, string>;
}

/**
 * A component that displays artwork information based on the specified mode
 */
export function ArtworkDisplay({
  artwork,
  mode = "standard",
  className = "",
  resolvedUrls = {},
}: ArtworkDisplayProps) {
  // If artwork is a string (ID), fetch the artwork data
  const isArtworkId = isString(artwork);
  // Always call hooks unconditionally
  const { artwork: fetchedArtwork, isLoading } = useArtwork(
    isArtworkId ? artwork : undefined
  );

  // Use either the provided artwork object or the fetched one
  const artworkData: Artwork | null = isArtworkId
    ? fetchedArtwork
    : (artwork as Artwork);

  if (isLoading) return <ArtworkSkeleton mode={mode} />;

  if (!artworkData)
    return (
      <div className="text-sm text-muted-foreground">Artwork not found</div>
    );

  const imageUrl = resolvedUrls[artworkData.image];

  // Render different components based on the display mode
  switch (mode) {
    case "minimal":
      return (
        <MinimalArtworkCard
          artwork={artworkData}
          imageUrl={imageUrl}
          className={className}
        />
      );

    case "detailed":
      return (
        <DetailedArtworkView
          artwork={artworkData}
          imageUrl={imageUrl}
          className={className}
        />
      );

    case "standard":
    default:
      return (
        <StandardArtworkCard
          artwork={artworkData}
          imageUrl={imageUrl}
          className={className}
        />
      );
  }
}

// Minimal display - just image and title
function MinimalArtworkCard({
  artwork,
  imageUrl,
  className = "",
}: {
  artwork: Artwork;
  imageUrl: string | undefined;
  className?: string;
}) {
  return (
    <Card className={`overflow-hidden ${className}`}>
      <div className="relative aspect-square w-full">
        {imageUrl ? (
          <Image
            src={imageUrl}
            alt={artwork.title}
            fill
            className="object-cover"
          />
        ) : (
          <Skeleton className="aspect-square w-full" />
        )}
      </div>
      <CardFooter className="p-2">
        <p className="text-sm font-medium truncate w-full">{artwork.title}</p>
      </CardFooter>
    </Card>
  );
}

// Standard display - image, title, year
function StandardArtworkCard({
  artwork,
  imageUrl,
  className = "",
}: {
  artwork: Artwork;
  imageUrl: string | undefined;
  className?: string;
}) {
  return (
    <Card className={`overflow-hidden ${className}`}>
      <div className="relative aspect-square w-full">
        {imageUrl ? (
          <Image
            src={imageUrl}
            alt={artwork.title}
            fill
            className="object-cover"
          />
        ) : (
          <Skeleton className="aspect-square w-full" />
        )}
      </div>
      <CardHeader className="p-3 pb-0">
        <CardTitle className="text-lg">{artwork.title}</CardTitle>
      </CardHeader>
      <CardContent className="p-3 pt-1">
        <div className="flex justify-between items-center">
          <p className="text-sm text-muted-foreground">{artwork.year}</p>
          <p className="text-sm text-muted-foreground">{artwork.medium}</p>
        </div>
      </CardContent>
    </Card>
  );
}

// Detailed display - all metadata
function DetailedArtworkView({
  artwork,
  imageUrl,
  className = "",
}: {
  artwork: Artwork;
  imageUrl: string | undefined;
  className?: string;
}) {
  return (
    <Card className={`overflow-hidden ${className}`}>
      <div className="relative aspect-square w-full">
        {imageUrl ? (
          <Image
            src={imageUrl}
            alt={artwork.title}
            fill
            className="object-cover"
          />
        ) : (
          <Skeleton className="aspect-square w-full" />
        )}
      </div>
      <CardHeader className="p-4 pb-2">
        <CardTitle className="text-xl">{artwork.title}</CardTitle>
      </CardHeader>
      <CardContent className="p-4 pt-0 space-y-3">
        <p className="text-sm">{artwork.description}</p>
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div>
            <span className="font-medium">Year:</span>
            {artwork.year}
          </div>
          <div>
            <span className="font-medium">Size:</span>
            {artwork.size}
          </div>
          <div>
            <span className="font-medium">Medium:</span>
            {artwork.medium}
          </div>
          <div>
            <span className="font-medium">Category:</span>
            {artwork.category}
          </div>
        </div>
        {artwork.tags && artwork.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 pt-2">
            {artwork.tags.map((tag) => (
              <Badge key={tag} variant="outline">
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Skeleton loader for when artwork is loading
function ArtworkSkeleton({ mode }: { mode: ArtworkDisplayMode }) {
  switch (mode) {
    case "minimal":
      return (
        <Card className="overflow-hidden">
          <Skeleton className="aspect-square w-full" />
          <CardFooter className="p-2">
            <Skeleton className="h-4 w-3/4" />
          </CardFooter>
        </Card>
      );

    case "detailed":
      return (
        <Card className="overflow-hidden">
          <Skeleton className="aspect-square w-full" />
          <CardHeader className="p-4 pb-2">
            <Skeleton className="h-6 w-3/4" />
          </CardHeader>
          <CardContent className="p-4 pt-0 space-y-3">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <div className="grid grid-cols-2 gap-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
            </div>
            <div className="flex flex-wrap gap-1 pt-2">
              <Skeleton className="h-5 w-16" />
              <Skeleton className="h-5 w-16" />
              <Skeleton className="h-5 w-16" />
            </div>
          </CardContent>
        </Card>
      );

    case "standard":
    default:
      return (
        <Card className="overflow-hidden">
          <Skeleton className="aspect-square w-full" />
          <CardHeader className="p-3 pb-0">
            <Skeleton className="h-5 w-3/4" />
          </CardHeader>
          <CardContent className="p-3 pt-1">
            <div className="flex justify-between items-center">
              <Skeleton className="h-4 w-1/4" />
              <Skeleton className="h-4 w-1/3" />
            </div>
          </CardContent>
        </Card>
      );
  }
}
