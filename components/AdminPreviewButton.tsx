"use client";

import React, {useState, useEffect} from 'react';
import {Button} from '@/components/ui/button';
import {Eye} from 'lucide-react';
import {useAdminAuth} from '@/lib/useAdminAuth';
import {collection, getDocs} from 'firebase/firestore';
import {db} from '@/lib/firebase';

export default function AdminPreviewButton() {
    const {user} = useAdminAuth();
    const [mounted, setMounted] = useState(false);
    const [isPreviewMode, setIsPreviewMode] = useState(false);
    const [hasPreviewData, setHasPreviewData] = useState(false);
    const [checkingPreviewData, setCheckingPreviewData] = useState(true);
    
    // Function to check if preview data exists
    const checkPreviewData = async () => {
        try {
            setCheckingPreviewData(true);
            const previewCollection = collection(db, "preview");
            const snapshot = await getDocs(previewCollection);
            setHasPreviewData(!snapshot.empty);
        } catch (error) {
            console.error("Error checking preview data:", error);
            setHasPreviewData(false);
        } finally {
            setCheckingPreviewData(false);
        }
    };

    // Check if we're in preview mode and if preview data exists
    useEffect(() => {
        setMounted(true);
        
        if (typeof window !== 'undefined') {
            const isPreview = new URLSearchParams(window.location.search).get('preview') === 'true';
            setIsPreviewMode(isPreview);
        }

        checkPreviewData();
    }, []);

    // Listen for custom events to refresh preview data
    useEffect(() => {
        const handleRefreshPreview = () => {
            checkPreviewData();
        };

        window.addEventListener('refreshPreviewData', handleRefreshPreview);
        return () => {
            window.removeEventListener('refreshPreviewData', handleRefreshPreview);
        };
    }, []);

    // Toggle preview mode with full page reload to ensure data refresh
    const togglePreviewMode = () => {
        if (typeof window !== 'undefined') {
            const currentUrl = new URL(window.location.href);
            
            if (isPreviewMode)
                currentUrl.searchParams.delete('preview');
            else
                currentUrl.searchParams.set('preview', 'true');
            
            // Use window.location for a full page reload
            window.location.href = currentUrl.toString();
        }
    };
    
    // Only show for logged in admins and not in preview mode
    if (!mounted || !user || isPreviewMode)
        return null;
    
    return (
        <div className="fixed bottom-6 right-6 z-40">
            <Button
                onClick={togglePreviewMode}
                size="sm"
                disabled={checkingPreviewData || !hasPreviewData}
                className="rounded-full shadow-lg bg-blue-500 hover:bg-blue-600 text-white disabled:bg-gray-400 disabled:hover:bg-gray-400"
            >
            <Eye className="h-4 w-4 mr-1"/>
        {checkingPreviewData ? "Checking..." : "Preview Changes"}
      </Button>
        </div>
    );
}
