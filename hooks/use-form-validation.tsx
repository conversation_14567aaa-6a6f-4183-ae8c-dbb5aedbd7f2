'use client';

import {useState, useCallback} from 'react';

export type ValidationRule<T> = {
    validate: (value: T) => boolean;
    message: string;
};

export type FieldValidation<T> = {
    [K in keyof T]?: ValidationRule<T[K]>[];
};

export interface UseFormValidationReturn<T> {
    errors: Partial<Record<keyof T, string>>;
    validateField: (field: keyof T, value: T[keyof T]) => boolean;
    validateForm: (data: T) => boolean;
    clearErrors: () => void;
    clearFieldError: (field: keyof T) => void;
}

/**
 * A hook for form validation with support for field-level validation rules
 * 
 * @param validationRules Object containing validation rules for each field
 * @returns Form validation utilities
 */
export function useFormValidation<T extends Record<string, any>>(validationRules: FieldValidation<T>): UseFormValidationReturn<T> {
    const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>({});
    
    // Validate a single field
    const validateField = useCallback((field: keyof T, value: T[keyof T]): boolean => {
        const fieldRules = validationRules[field];
        
        if (!fieldRules)
            return true;
        
        // Find the first rule that fails
        const failedRule = fieldRules.find((rule) => !rule.validate(value));
        
        if (failedRule) {
            setErrors((prev) => ({
                ...prev,
                [field]: failedRule.message,
            }));
            return false;
        }
        
        // Clear error if validation passes
        setErrors((prev) => {
            const newErrors = {...prev};
            delete newErrors[field];
            return newErrors;
        });
        
        return true;
    }, [validationRules]);
    
    // Validate the entire form
    const validateForm = useCallback((data: T): boolean => {
        const newErrors: Partial<Record<keyof T, string>> = {};
        let isValid = true;
        
        // Check each field with validation rules
        for (const key of Object.keys(validationRules)) {
            const field = key as keyof T;
            const fieldRules = validationRules[field];
            
            if (!fieldRules)
                continue;
            
            // Find the first rule that fails
            const failedRule = fieldRules.find((rule) => !rule.validate(data[field]));
            
            if (failedRule) {
                newErrors[field] = failedRule.message;
                isValid = false;
            }
        }
        
        setErrors(newErrors);
        return isValid;
    }, [validationRules]);
    
    // Clear all errors
    const clearErrors = useCallback(() => {
        setErrors({});
    }, []);
    
    // Clear a specific field error
    const clearFieldError = useCallback((field: keyof T) => {
        setErrors((prev) => {
            const newErrors = {...prev};
            delete newErrors[field];
            return newErrors;
        });
    }, []);
    
    return {
        errors,
        validateField,
        validateForm,
        clearErrors,
        clearFieldError,
    };
}
