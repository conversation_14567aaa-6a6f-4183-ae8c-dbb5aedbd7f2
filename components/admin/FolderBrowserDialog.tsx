"use client";

import React, {
    useState,
    useCallback,
    useEffect,
} from 'react';
import {Input} from '@/components/ui/input';
import {Card} from '@/components/ui/card';
import {storage} from '@/lib/firebase';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter,
} from '@/components/ui/dialog';
import {
    ref,
    listAll,
    StorageReference,
} from 'firebase/storage';
import {Button} from '@/components/ui/button';
import {
    ChevronRight,
    Folder,
    Search,
    ArrowLeft,
    CheckCircle2,
} from 'lucide-react';

const BASE_PATH = process.env.NEXT_PUBLIC_STORAGE_BASE_PATH || 'images';

interface StorageFolder {
    ref: StorageReference;
    name: string;
    path: string;
    parentPath: string;
}

interface FolderBrowserDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onSelect: (folderPath: string) => void;
    currentFolder?: string;
}

export function FolderBrowserDialog({open, onOpenChange, onSelect, currentFolder = 'artworks'}: FolderBrowserDialogProps) {
    const [folders, setFolders] = useState<StorageFolder[]>([]);
    const [currentPath, setCurrentPath] = useState(BASE_PATH);
    const [searchTerm, setSearchTerm] = useState('');
    const [loading, setLoading] = useState(false);
    const [, setError] = useState<string | null>(null);
    
    // Set initial path based on currentFolder
    useEffect(() => {
        if (open && currentFolder) {
            const fullPath = `${BASE_PATH}/${currentFolder}`.replace(/\/+/g, '/');
            setCurrentPath(fullPath);
        }
    }, [open, currentFolder]);

    // Load folders
    const loadFolders = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);
            
            const storageRef = ref(storage, currentPath);
            const result = await listAll(storageRef);
            
            // Get all folders
            const folderItems: StorageFolder[] = result.prefixes.map((prefixRef) => ({
                ref: prefixRef,
                name: prefixRef.name,
                path: prefixRef.fullPath,
                parentPath: currentPath,
            }));
            
            setFolders(folderItems);
        } catch {
            setError('Failed to load folders');
        }
 finally {
            setLoading(false);
        }
    }, [currentPath]);
    
    // Load folders when dialog opens or path changes
    useEffect(() => {
        if (open)
            loadFolders();
    }, [loadFolders, open]);

    // Navigate to parent folder
    const navigateToParent = () => {
        const parentPath = currentPath
            .split('/')
            .slice(0, -1)
            .join('/');
        
        if (parentPath.length >= BASE_PATH.length)
            setCurrentPath(parentPath);
    };
    
    // Handle folder click
    const handleFolderClick = (folderRef: StorageReference) => {
        setCurrentPath(folderRef.fullPath);
    };
    
    // Get relative path for selected folder
    const getRelativePath = (fullPath: string) => {
        return fullPath
            .replace(`${BASE_PATH}/`, '')
            .replace(BASE_PATH, '');
    };
    
    // Filter folders based on search term
    const filteredFolders = React.useMemo(() => {
        if (!folders)
            return [];
        
        if (!searchTerm)
            return folders;
        
        const searchLower = searchTerm.toLowerCase();
        
        return folders.filter((folder) => folder
            .name
            .toLowerCase()
            .includes(searchLower));
    }, [folders, searchTerm]);
    
    // Path navigation breadcrumbs
    const pathSegments = currentPath
        .split('/')
        .filter(Boolean);
    
    const baseFolderName = BASE_PATH
        .split('/')
        .pop() || 'root';
    
    // Get current folder relative path
    const currentRelativePath = getRelativePath(currentPath);
    
    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="max-w-2xl max-h-[90vh] flex flex-col overflow-hidden p-0">
                <DialogHeader className="p-6 pb-2">
                    <DialogTitle className="text-xl">Select Folder</DialogTitle>
                </DialogHeader>
                <div className="flex-1 flex flex-col overflow-hidden px-6">
                    {/* Path navigation */}
                    <div className="flex items-center gap-1 text-sm text-muted-foreground mb-4 bg-gray-50 p-2 rounded-md overflow-x-auto">
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setCurrentPath(BASE_PATH)}
                            className="flex items-center shrink-0"
                        >
                        <Folder className="h-4 w-4 mr-1"/>
                        {baseFolderName}
                    </Button>
                        {pathSegments.map((segment, index) => {
                        if (segment === baseFolderName)
                            return null;
                        
                        const path = pathSegments
                            .slice(0, index + 1)
                            .join('/');
                        
                        return (
                            <React.Fragment key={path}>
                                <ChevronRight className="h-4 w-4 shrink-0"/>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => setCurrentPath(`${BASE_PATH}/${path}`.replace(/\/+/g, '/'))}
                                    className="shrink-0"
                                >
                                {segment}
                            </Button>
                            </React.Fragment>
                        );
                    })}
                    </div>
                    {/* Controls */}
                    <div className="flex flex-wrap gap-2 mb-4">
                        <div className="relative flex-1 min-w-[200px]">
                            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"/>
                            <Input
                                type="text"
                                placeholder="Search folders..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="pl-8"
                            />
                        </div>
                        {currentPath !== BASE_PATH && (
<Button
                            variant="outline"
                            onClick={navigateToParent}
                            className="flex items-center"
                            title="Go up one level"
                        >
                            <ArrowLeft className="h-4 w-4 mr-1"/>
                Up
              </Button>
                        )}
                    </div>
                    {/* Content area */}
                    <div
                            className="flex-1 overflow-auto mb-4"
                            style={{
                                maxHeight: 'calc(70vh - 200px)',
                            }}
                        >
                        {loading ? (
                        <div className="flex items-center justify-center h-40">
                            <div className="text-center">
                                <div className="animate-spin rounded-full h-10 w-10 border-4 border-gray-200 border-t-primary mx-auto mb-3"></div>
                                <p className="text-gray-600">Loading folders...</p>
                            </div>
                        </div>
                        ) : filteredFolders.length === 0 ? (
                        <div className="flex flex-col items-center justify-center h-40 bg-gray-50 rounded-lg">
                            <Folder className="h-12 w-12 text-gray-300 mb-2"/>
                            <div className="text-center text-muted-foreground">
                                {searchTerm ? 'No matching folders found' : 'No subfolders in this location'}
                            </div>
                            <p className="text-sm text-gray-400 mt-2">
                                {searchTerm ? 'Try a different search term' : 'Use the New Folder button to create subfolders'}
                            </p>
                        </div>
                        ) : (
                        <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                            {filteredFolders.map((folder) => (
                            <Card
                                key={folder.path}
                                className="relative aspect-square cursor-pointer hover:ring-2 hover:ring-primary transition-all flex items-center justify-center overflow-hidden group"
                                onClick={() => handleFolderClick(folder.ref)}
                            >
                                <div className="absolute inset-0 bg-gray-50 flex items-center justify-center">
                                    <Folder className="h-16 w-16 text-gray-300 group-hover:text-gray-400 transition-colors"/>
                                </div>
                                <div className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-2 text-center">
                                    <div className="text-sm font-medium truncate">
                                        {folder.name}
                                    </div>
                                </div>
                            </Card>
                            ))}
                        </div>
                        )}
                    </div>
                </div>
                {/* Footer */}
                <DialogFooter className="border-t p-4 mt-auto bg-gray-50">
                    <div className="flex justify-between items-center w-full">
                        <div className="text-sm">
                            <span className="text-muted-foreground mr-2">
                Current folder:
              </span>
                            <span className="font-medium">
                                {currentRelativePath || baseFolderName}
                            </span>
                        </div>
                        <div className="flex gap-2">
                            <Button variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
                            <Button
                                    onClick={() => onSelect(currentRelativePath)}
                                    variant="default"
                                >
                                <CheckCircle2 className="h-4 w-4 mr-2"/>
                Select This Folder
              </Button>
                        </div>
                    </div>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
