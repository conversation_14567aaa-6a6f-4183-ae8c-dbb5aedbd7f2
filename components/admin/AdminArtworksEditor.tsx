"use client";

import { useEffect, useState, useMemo } from "react";
import { useAdminContent } from "@/hooks/use-admin-content";
import { useImageResolver } from "@/hooks/use-image-resolver";
import { useToast } from "@/hooks/use-toast";
import { useFormValidation } from "@/hooks/use-form-validation";
import AdminFormLayout from "@/components/admin/AdminFormLayout";
import { FormField } from "@/components/ui/form-field";
import { EnhancedImageField } from "@/components/admin";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Trash2, Plus } from "lucide-react";
import { Artwork, GalleryCategory } from "@/types/artworks";
import { doc, setDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { logError } from "@/lib/errorLogger";

const isError = (a: unknown): a is Error => a instanceof Error;

interface AdminArtworksEditorProps {
  slug: string;
}

export default function AdminArtworksEditor({
  slug,
}: AdminArtworksEditorProps) {
  // Memoize defaultValue to avoid infinite reloads in useAdminContent
  const defaultValue = useMemo(
    () => ({
      categories: [] as GalleryCategory[],
      slugs: [] as { slug: string; artworks: Artwork[] }[],
      slug,
    }),
    [slug]
  );

  const {
    content,
    setContent,
    loading,
    saving,
    isDirty,
    resetContent,
  } = useAdminContent<{
    categories: GalleryCategory[];
    slugs: { slug: string; artworks: Artwork[] }[];
    slug: string;
  }>({
    previewCollection: "preview",
    previewDocId: "gallery",
    liveCollection: "live",
    liveDocId: "gallery",
    defaultValue,
    createPreviewIfMissing: false, // Only create preview doc on save, not on load
  });

  // Always treat slugs as an array of { slug, artworks }
  const slugsArr = Array.isArray(content.slugs) ? content.slugs : [];

  const found = slugsArr.find((s) => s.slug === slug);
  const artworks = useMemo(() => found?.artworks || [], [found]);

  const { toast, showToast } = useToast();
  const { resolveImages } = useImageResolver();

  // Form validation
  const { errors } = useFormValidation<Artwork>({
    title: [
      {
        validate: (value) => !!value && value.trim().length > 0,
        message: "Title is required",
      },
    ],
    image: [
      {
        validate: (value) => !!value && value.trim().length > 0,
        message: "Image is required",
      },
    ],
  });

  // Track validation errors for each artwork
  const [validationErrors, setValidationErrors] = useState<
    Record<number, Partial<Record<keyof Artwork, string>>>
  >({});

  // Resolve all image paths when content changes
  useEffect(() => {
    if (artworks) {
      const imagePaths = artworks
        .map((artwork) => artwork.image)
        .filter(Boolean);

      resolveImages(imagePaths);
    }
  }, [artworks, resolveImages]);

  // Add a new artwork
  const addArtwork = () => {
    const updatedArtworks = [
      ...artworks,
      {
        title: "",
        image: "",
        description: "",
        year: "",
        size: "",
        medium: "",
        category: "",
        tags: [],
        galleryPath: "",
      },
    ];
    const updatedSlugsArr = slugsArr.map((s) =>
      s.slug === slug ? { ...s, artworks: updatedArtworks } : s
    );
    setContent({
      ...content,
      slugs: updatedSlugsArr,
    });
  };

  // Update an artwork field
  const updateArtworkField = (
    index: number,
    field: keyof Artwork,
    value: string
  ) => {
    const updatedArtworks = artworks.slice();
    updatedArtworks[index] = {
      ...updatedArtworks[index],
      [field]: value,
    };
    const updatedSlugsArr = slugsArr.map((s) =>
      s.slug === slug ? { ...s, artworks: updatedArtworks } : s
    );
    setContent({
      ...content,
      slugs: updatedSlugsArr,
    });

    // Validate the field if it has validation rules
    if (field === "title" || field === "image") {
      const isValid = !!value && value.trim().length > 0;

      setValidationErrors((prev) => ({
        ...prev,
        [index]: {
          ...prev[index],
          [field]: isValid ? undefined : errors[field],
        },
      }));
    }
  };

  // Remove an artwork
  const removeArtwork = (index: number) => {
    const updatedArtworks = artworks.filter((_, i) => i !== index);
    const updatedSlugsArr = slugsArr.map((s) =>
      s.slug === slug ? { ...s, artworks: updatedArtworks } : s
    );
    setContent({
      ...content,
      slugs: updatedSlugsArr,
    });

    // Remove validation errors for this artwork
    setValidationErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[index];
      return newErrors;
    });
  };

  const handleSave = async () => {
    // Validate all artworks before saving
    let isValid = true;
    const newValidationErrors: Record<
      number,
      Partial<Record<keyof Artwork, string>>
    > = {};

    for (const [index, artwork] of artworks.entries()) {
      // Validate required fields
      if (!artwork.title || !artwork.title.trim().length) {
        isValid = false;
        newValidationErrors[index] = {
          ...newValidationErrors[index],
          title: "Title is required",
        };
      }

      if (!artwork.image || !artwork.image.trim().length) {
        isValid = false;
        newValidationErrors[index] = {
          ...newValidationErrors[index],
          image: "Image is required",
        };
      }
    }

    setValidationErrors(newValidationErrors);

    if (!isValid) {
      showToast("Please fix the validation errors before saving", {
        variant: "destructive",
      });
      return;
    }

    // Prepare slugs for saving (always array)
    const slugsToSave = slugsArr;

    // Save to Firestore directly
    try {
      const previewRef = doc(db, "preview", "gallery");
      await setDoc(previewRef, { ...content, slugs: slugsToSave });
      showToast("Artworks saved successfully", {
        variant: "default",
      });
    } catch (err: unknown) {
      logError(
        isError(err) ? err : Error(String(err)),
        "AdminArtworksEditor.save"
      );
      showToast(
        `Failed to save: ${
          isError(err) ? (err as Error).message : "Unknown error"
        }`,
        {
          variant: "destructive",
        }
      );
    }
  };

  return (
    <AdminFormLayout
      title={`Edit Artworks: ${slug}`}
      description="Manage artworks for this gallery category."
      isLoading={loading}
      isSaving={saving}
      isDirty={isDirty}
      onSave={handleSave}
      onReset={resetContent}
      previewUrl={`/gallery/${slug}`}
      previewDocId="gallery"
    >
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <p className="text-gray-600">
            Add, edit, or remove artworks. Title and image are required.
          </p>
          <Button type="button" onClick={addArtwork}>
            <Plus className="h-4 w-4 mr-1" /> Add Artwork
          </Button>
        </div>
        {artworks.length === 0 ? (
          <div className="text-center py-12 border rounded-md bg-gray-50">
            <p className="text-gray-500 mb-4">No artworks added yet</p>
            <Button type="button" onClick={addArtwork}>
              <Plus className="h-4 w-4 mr-1" /> Add Your First Artwork
            </Button>
          </div>
        ) : (
          <div className="space-y-6">
            {artworks.map((artwork, index) => (
              <div key={index} className="border rounded-md p-4 space-y-4">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">
                    {artwork.title || `Artwork ${index + 1}`}
                  </h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => removeArtwork(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    label="Title"
                    required
                    error={validationErrors[index]?.title}
                  >
                    <Input
                      value={artwork.title}
                      onChange={(e) =>
                        updateArtworkField(index, "title", e.target.value)
                      }
                      placeholder="Artwork title"
                      className={
                        validationErrors[index]?.title ? "border-red-500" : ""
                      }
                    />
                  </FormField>
                  <FormField
                    label="Gallery Path"
                    helpText="Optional link to a gallery page"
                  >
                    <Input
                      value={artwork.galleryPath || ""}
                      onChange={(e) =>
                        updateArtworkField(index, "galleryPath", e.target.value)
                      }
                      placeholder={`/gallery/${slug}`}
                    />
                  </FormField>
                  <FormField label="Year">
                    <Input
                      value={artwork.year}
                      onChange={(e) =>
                        updateArtworkField(index, "year", e.target.value)
                      }
                      placeholder="Year"
                    />
                  </FormField>
                  <FormField label="Size">
                    <Input
                      value={artwork.size}
                      onChange={(e) =>
                        updateArtworkField(index, "size", e.target.value)
                      }
                      placeholder="Size"
                    />
                  </FormField>
                  <FormField label="Description" className="md:col-span-2">
                    <Textarea
                      value={artwork.description}
                      onChange={(e) =>
                        updateArtworkField(index, "description", e.target.value)
                      }
                      placeholder="Description"
                      rows={3}
                    />
                  </FormField>
                  <FormField
                    label="Artwork Image"
                    required
                    error={validationErrors[index]?.image}
                    className="md:col-span-2"
                  >
                    <EnhancedImageField
                      label=""
                      imagePath={artwork.image}
                      onChange={(path) =>
                        updateArtworkField(index, "image", path)
                      }
                      aspectRatio="wide"
                      className={
                        validationErrors[index]?.image
                          ? "border border-red-500 rounded-md"
                          : ""
                      }
                      required={true}
                    />
                  </FormField>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      {toast}
    </AdminFormLayout>
  );
}
