"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Home, User, Calendar, Image, Menu, History, Palette } from "lucide-react";

interface AdminModuleProps {
  title: string;
  description: string;
  href: string;
  icon: React.ReactNode;
}

function AdminModule({ title, description, href, icon }: AdminModuleProps) {
  return (
    <Card className="hover:shadow-md transition-shadow flex flex-col h-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <div className="mr-2 bg-blue-100 p-2 rounded-full">{icon}</div>
          {title}
        </CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardFooter className="mt-auto">
        <Button asChild className="w-full">
          <Link href={href}>{title}</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}

export default function AdminPage() {
  return (
    <div className="space-y-6 max-w-5xl mx-auto">
      <h2 className="text-3xl font-bold">Welcome to Admin Dashboard</h2>
      <div className="bg-gray-50 p-6 rounded-lg border border-gray-100 mb-8">
        <p className="text-xl font-bold mb-4">Manage Artwork & Versions:</p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <AdminModule
            title="Artwork Repository"
            description="Manage Artwork"
            href="/admin/artworks"
            icon={<Palette className="h-5 w-5 text-blue-600" />}
          />
          <AdminModule
            title="Version Management"
            description="Manage content versions, rollback, and prune backups"
            href="/admin/versions"
            icon={<History className="h-5 w-5 text-blue-600" />}
          />
        </div>
      </div>

      <div className="bg-gray-50 p-6 rounded-lg border border-gray-100">
        <p className="text-xl font-bold mb-4">Manage Site content:</p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <AdminModule
            title="Home Page"
            description="Edit hero content, featured artworks, and about text"
            href="/admin/home"
            icon={<Home className="h-5 w-5 text-blue-600" />}
          />
          <AdminModule
            title="Gallery"
            description="Manage gallery categories and artworks"
            href="/admin/gallery"
            icon={
              <Image
                className="h-5 w-5 text-blue-600"
                alt=""
                aria-hidden="true"
              />
            }
          />
          <AdminModule
            title="Events Page"
            description="Manage upcoming, ongoing, and past events"
            href="/admin/events"
            icon={<Calendar className="h-5 w-5 text-blue-600" />}
          />
          <AdminModule
            title="About Page"
            description="Edit artist statement, education, exhibitions, and awards"
            href="/admin/about"
            icon={<User className="h-5 w-5 text-blue-600" />}
          />
          <AdminModule
            title="Contact Page"
            description="Edit contact information and social links"
            href="/admin/contact"
            icon={<Menu className="h-5 w-5 text-blue-600" />}
          />
          {/* <AdminModule
            title="Navigation Menu"
            description="Edit the site navigation menu"
            href="/admin/menu"
            icon={<Menu className="h-5 w-5 text-blue-600" />}
          /> */}
        </div>
      </div>
    </div>
  );
}
