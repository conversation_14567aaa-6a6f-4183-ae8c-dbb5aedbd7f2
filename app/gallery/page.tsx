"use client";

import { useEffect } from "react";
import CategoryCard from "@/components/CategoryCard";
import EditButton from "@/components/EditButton";
import PageHeader from "@/components/PageHeader";
import usePreview from "@/lib/usePreview";
import { usePublicContent } from "@/hooks/use-public-content";
import { useImageResolver } from "@/hooks/use-image-resolver";
import { GalleryContent } from "@/types/gallery";
import { useAdminAuth } from "@/lib/useAdminAuth";

const DEFAULT_GALLERY_CONTENT: GalleryContent = {
  categories: [],
  slugs: [],
};

const PLACEHOLDER_IMAGE_SRC = "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7";

export default function Gallery() {
  const isPreviewMode = usePreview();
  const { user } = useAdminAuth();

  // Use the new hook for content loading
  const { content, loading, error } = usePublicContent<GalleryContent>({
    collection: "live",
    documentId: "gallery",
    defaultValue: DEFAULT_GALLERY_CONTENT,
    previewMode: isPreviewMode,
  });

  // Use the image resolver hook
  const { resolvedUrls: resolvedImageUrls, resolveImages } = useImageResolver();

  // Resolve images when content changes
  useEffect(() => {
    if (content?.categories) {
      const imagePaths = content.categories
        .map((cat) => cat.image)
        .filter(Boolean);
      resolveImages(imagePaths);
    }
  }, [content, resolveImages]);

  return (
    <div className="max-w-6xl mx-auto px-4 py-16">
      <PageHeader
        title="Gallery"
        subtitle="Collections"
        description="Explore the full collection of original artworks by category."
      />
      <div className="flex items-center justify-end mb-2 -mt-12">
        <EditButton path="/admin/gallery" hidden={isPreviewMode || !user} />
      </div>
      {loading ? (
        <div className="text-center text-modernArt-mutedForeground/80 font-montserrat">
          Loading categories...
        </div>
      ) : error ? (
        <div className="text-center text-modernArt-mutedForeground/80 font-montserrat">
          Error loading gallery content
        </div>
      ) : !content?.categories?.length ? (
        <div className="text-center text-modernArt-mutedForeground/80 italic font-montserrat">
          No categories found.
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-10">
          {content.categories.map((cat) => (
            <CategoryCard
              key={cat.slug}
              category={cat}
              imageUrl={resolvedImageUrls[cat.image] || PLACEHOLDER_IMAGE_SRC}
            />
          ))}
        </div>
      )}
    </div>
  );
}
