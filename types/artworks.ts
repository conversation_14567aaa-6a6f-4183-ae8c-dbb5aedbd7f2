export interface Artwork {
    id?: string;

    // Unique identifier
    title: string;

    // Artwork title
    image: string;

    // Storage path
    description: string;

    // Full description
    year: string;

    // Year created
    size: string;

    // Dimensions
    medium: string;

    // Medium used
    category: string;

    // Category/collection
    tags: string[];

    // For filtering/organization
    galleryPath?: string;

    // Legacy field for backward compatibility
    createdAt?: Date;

    // Upload timestamp
    updatedAt?: Date;

    // Last modified timestamp
}

export interface GalleryCategory {
    title: string;
    slug: string;
    image: string;
}

export interface ArtworkCategory {
    artworks: Artwork[];
}

// Reference-based content model
export interface ArtworkReference {
    id: string;
    displayMode?: 'minimal' | 'standard' | 'detailed';
}

// Different display modes for artworks
export type ArtworkDisplayMode = 'minimal' | 'standard' | 'detailed';
