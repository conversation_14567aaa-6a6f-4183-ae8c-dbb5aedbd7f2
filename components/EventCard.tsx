import React from "react";
import Image from "next/image";
import { Event } from "@/lib/types";

export interface EventCardProps extends Event {
  onImageClick?: (imageIndex: number) => void;
  eventIndex?: number;
}

export function EventCard({
  title,
  date,
  location,
  description,
  images,
  onImageClick,
  eventIndex = 0,
}: EventCardProps) {
  return (
    <div className="relative p-8 border border-modernArt-border bg-modernArt-cardBackground rounded-lg shadow-sm transition-all duration-300">
      {/* Event Details */}
      <div className="mb-12">
        <h3 className="text-3xl md:text-4xl font-cormorant font-light text-modernArt-foreground mb-6 tracking-wide">
          {title}
        </h3>
        <div className="flex flex-col sm:flex-row sm:justify-between mb-8 gap-4">
          <span className="text-xl font-cormorant text-modernArt-mutedForeground">{date}</span>
          <span className="text-xl font-cormorant text-modernArt-mutedForeground">{location}</span>
        </div>
        <div className="text-lg leading-relaxed font-montserrat text-modernArt-mutedForeground space-y-6">
          {description
            .split(". ")
            .filter(Boolean)
            .map((sentence, idx) => (
              <p key={idx}>
                {sentence.trim()}
                {sentence.trim().endsWith(".") ? "" : "."}
              </p>
            ))}
        </div>
      </div>
      {/* Event Images */}
      <div
        className={`grid grid-cols-1 ${
          images.details
            ? "lg:grid-cols-2"
            : "lg:grid-cols-1 lg:max-w-lg lg:mx-auto"
        } gap-10`}
      >
        {/* Main Image */}
        {images.main && (
          <div
            className={`relative ${
              images.details ? "aspect-[4/3]" : "aspect-[3/4]"
            } w-full overflow-hidden image-zoom-container`}
          >
            <button
              onClick={() =>
                onImageClick &&
                onImageClick(images.details ? eventIndex * 2 : eventIndex)
              }
              className="w-full h-full"
              title={`View ${title} invitation`}
            >
              <Image
                src={images.main}
                alt={`${title} - Main Invitation`}
                fill
                className="object-contain image-zoom"
                sizes={`(max-width: 768px) 100vw, ${
                  images.details ? "50vw" : "33vw"
                }`}
                priority={true}
              />
            </button>
          </div>
        )}
        {/* Details Image */}
        {images.details && (
          <div className="relative aspect-[4/3] w-full overflow-hidden image-zoom-container">
            <button
              onClick={() => onImageClick && onImageClick(eventIndex * 2 + 1)}
              className="w-full h-full"
              title={`View ${title} details`}
            >
              <Image
                src={images.details}
                alt={`${title} - Event Details`}
                fill
                className="object-contain image-zoom"
                sizes="(max-width: 768px) 100vw, (min-width: 1024px) 450px, 50vw"
                priority={true}
              />
            </button>
          </div>
        )}
      </div>
    </div>
  );
}