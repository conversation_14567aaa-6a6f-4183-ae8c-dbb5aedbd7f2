import { Artwork } from "@/types/artworks";
import {
  collection,
  doc,
  getDocs,
  getDoc,
  setDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
} from "firebase/firestore";
import { db } from "./firebase";
import { logError } from "./errorLogger";

const isError = (a: unknown): a is Error => a instanceof Error;

const ARTWORKS_COLLECTION = "artworks";

/**
 * Creates or updates an artwork in the repository
 * @param artwork The artwork data to save
 * @returns The ID of the created/updated artwork
 */
export async function saveArtwork(artwork: Artwork): Promise<string> {
  try {
    // Generate ID from title if not provided
    const id = artwork.id || createArtworkId(artwork.title);
    const artworkRef = doc(db, ARTWORKS_COLLECTION, id);

    // Add timestamps
    const now = new Date();

    const artworkData = {
      ...artwork,
      id,
      updatedAt: now,
      createdAt: artwork.createdAt || now,
    };

    await setDoc(artworkRef, artworkData);
    return id;
  } catch (error) {
    logError(
      isError(error) ? error : Error(String(error)),
      "artwork-repository.saveArtwork",
      {
        context: {
          artwork,
        },
      }
    );
    throw error;
  }
}

/**
 * Retrieves an artwork by ID
 * @param id The artwork ID
 * @returns The artwork data or null if not found
 */
export async function getArtwork(id: string): Promise<Artwork | null> {
  try {
    const artworkRef = doc(db, ARTWORKS_COLLECTION, id);
    const artworkSnap = await getDoc(artworkRef);

    if (!artworkSnap.exists()) return null;

    return artworkSnap.data() as Artwork;
  } catch (error) {
    logError(
      isError(error) ? error : Error(String(error)),
      "artwork-repository.getArtwork",
      {
        context: {
          id,
        },
      }
    );
    throw error;
  }
}

/**
 * Retrieves artworks with pagination support
 * @param options Query options including pagination parameters
 * @returns Object containing artworks array, pagination info, and metadata
 */
export async function getArtworks(
  options: {
    category?: string;
    tags?: string[];
    sortBy?: "title" | "year" | "createdAt" | "updatedAt";
    sortDirection?: "asc" | "desc";
    limit?: number;
    page?: number;
    pageSize?: number;
    lastVisible?: unknown;
  } = {}
): Promise<{
  artworks: Artwork[];
  lastVisible: unknown;
  hasMore: boolean;
  totalCount?: number;
}> {
  try {
    const {
      category,
      tags,
      sortBy = "title",
      sortDirection = "asc",
      limit: queryLimit,
      page = 1,
      pageSize = 12,
      lastVisible,
    } = options;

    let q = collection(db, ARTWORKS_COLLECTION);
    const constraints = [];

    // Apply filters
    if (category) constraints.push(where("category", "==", category));

    if (tags && tags.length > 0)
      // Firebase doesn't support array contains any with other filters
      // For complex queries, we might need to use composite indexes
      constraints.push(where("tags", "array-contains-any", tags));

    // Apply sorting
    constraints.push(orderBy(sortBy, sortDirection));

    // Apply pagination
    // If lastVisible is provided, use startAfter for cursor-based pagination
    if (lastVisible) constraints.push(startAfter(lastVisible));

    // Use the provided limit, pageSize, or default to 12 items per page
    const limitValue = queryLimit || pageSize;
    constraints.push(limit(limitValue + 1));
    // Get one extra to check if there are more items
    q = query(q, ...constraints);

    const querySnapshot = await getDocs(q);
    const artworks = querySnapshot.docs.slice(0, limitValue).map(
      (doc) =>
        ({
          ...doc.data(),
          id: doc.id,
        } as Artwork)
    );

    // Check if there are more items
    const hasMore = querySnapshot.docs.length > limitValue;

    // Get the last visible document for cursor-based pagination
    const newLastVisible =
      querySnapshot.docs.length > 0
        ? querySnapshot.docs[
            querySnapshot.docs.length > limitValue
              ? limitValue - 1
              : querySnapshot.docs.length - 1
          ]
        : null;

    // Get total count (optional, can be expensive for large collections)
    let totalCount;

    if (page === 1 && artworks.length < 100) {
      // Only do this for the first page and if the result set is small
      // This is a simple approach - for production, consider using a counter document
      const countQuery = query(collection(db, ARTWORKS_COLLECTION));

      const countSnapshot = await getDocs(countQuery);

      totalCount = countSnapshot.size;
    }

    return {
      artworks,
      lastVisible: newLastVisible,
      hasMore,
      totalCount,
    };
  } catch (error) {
    logError(
      isError(error) ? error : Error(String(error)),
      "artwork-repository.getArtworks",
      {
        context: {
          options,
        },
      }
    );
    throw error;
  }
}

/**
 * Deletes an artwork by ID
 * @param id The artwork ID
 */
export async function deleteArtwork(id: string): Promise<void> {
  try {
    const artworkRef = doc(db, ARTWORKS_COLLECTION, id);
    await deleteDoc(artworkRef);
  } catch (error) {
    logError(
      isError(error) ? error : Error(String(error)),
      "artwork-repository.deleteArtwork",
      {
        context: {
          id,
        },
      }
    );
    throw error;
  }
}

/**
 * Creates a URL-friendly ID from an artwork title
 * @param title The artwork title
 * @returns A URL-friendly ID
 */
export function createArtworkId(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\da-z]+/g, "-")
    .replace(/^-|-$/g, "");
}

/**
 * Retrieves artworks by their IDs
 * @param ids Array of artwork IDs
 * @returns Array of artworks
 */
export async function getArtworksByIds(ids: string[]): Promise<Artwork[]> {
  try {
    if (!ids || !ids.length) return [];

    // Firebase doesn't support getting multiple documents by ID in a single query
    // So we need to fetch them individually
    const artworkPromises = ids.map(async (id) => await getArtwork(id));
    const artworks = await Promise.all(artworkPromises);

    // Filter out null values (artworks that weren't found)
    return artworks.filter(Boolean) as Artwork[];
  } catch (error) {
    logError(
      isError(error) ? error : Error(String(error)),
      "artwork-repository.getArtworksByIds",
      {
        context: {
          ids,
        },
      }
    );
    throw error;
  }
}
