import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import type { Category } from '@/types/gallery'; // Assuming Category type is defined here or similar

interface CategoryCardProps {
  category: Category;
  imageUrl?: string; // Allow passing resolved image URL
}

export function CategoryCard({ category, imageUrl }: CategoryCardProps) {
  const { slug, title, description, image } = category;
  const displayImageUrl = imageUrl || image; // Use resolved URL if provided, else fallback to original

  return (
    <Link
      href={`/gallery/${slug}`}
      className="group block overflow-hidden elegant-card bg-white shadow-md hover:shadow-lg transition-shadow duration-300"
    >
      <div className="relative aspect-[4/3] bg-elegant-light image-zoom-container">
        {displayImageUrl ? (
          <Image
            src={displayImageUrl}
            alt={title}
            fill
            className="object-cover image-zoom"
            sizes="(max-width: 768px) 100vw, 33vw"
          />
        ) : (
          <div className="flex items-center justify-center h-full bg-gray-100">
            <p className="text-sm text-gray-500">No image</p>
          </div>
        )}
      </div>
      <div className="p-6">
        <h2 className="text-xl font-cormorant font-medium text-elegant-text group-hover:text-elegant-accent transition-colors">
          {title}
        </h2>
        {description && (
          <p className="text-elegant-text/70 font-montserrat text-sm mt-2">
            {description}
          </p>
        )}
      </div>
    </Link>
  );
}

export default CategoryCard;
