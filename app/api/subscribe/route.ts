import { NextRequest, NextResponse } from 'next/server';
import { adminDb } from '@/lib/firebase-admin'; // We'll need to create this admin init
import { v4 as uuidv4 } from 'uuid';
// import { getAuth } from 'firebase-admin/auth'; // No longer explicitly used here
import { sendEmail, createConfirmationEmailHtml } from '@/lib/email';

// Helper function to initialize Firebase Admin SDK (if not already initialized)
// This should ideally be in a separate file like 'lib/firebase-admin.ts'
// For now, let's assume it's correctly set up and adminDb is available.

export async function POST(request: NextRequest) {
  try {
    const { name, email } = await request.json();

    if (!name || typeof name !== 'string') {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 });
    }

    if (!email || typeof email !== 'string') {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }

    // Validate email format (basic)
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({ error: 'Invalid email format' }, { status: 400 });
    }

    const subscribersRef = adminDb.collection('subscribers');
    const existingSubscriber = await subscribersRef.where('email', '==', email).limit(1).get();

    if (!existingSubscriber.empty) {
      // Check if already confirmed; if not, maybe resend confirmation?
      // For now, let's just say they are already on the list.
      const subData = existingSubscriber.docs[0].data();
      if (subData.isConfirmed) {
        return NextResponse.json({ message: 'Email already subscribed and confirmed' }, { status: 200 });
      } else {
        // Potentially resend confirmation, or inform them they need to confirm
        // For simplicity in this step, we'll treat it as "already exists"
        return NextResponse.json({ message: 'Email already on the list, pending confirmation' }, { status: 200 });
      }
    }

    const confirmationToken = uuidv4();
    const subscribedAt = new Date();

    // Construct the confirmation URL
    // Note: Make sure NEXT_PUBLIC_BASE_URL is set in your environment variables
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const confirmationUrl = `${baseUrl}/api/confirm/${confirmationToken}`;

    await subscribersRef.add({
      name: name.trim(),
      email,
      isConfirmed: false,
      confirmationToken,
      subscribedAt,
      confirmationEmailSentAt: new Date(), // Track when the confirmation email was sent
    });

    // Send confirmation email using Firebase Admin SDK (or a preferred email service)
    // Firebase Auth's generateEmailVerificationLink is more for user accounts,
    // for a newsletter, we might need a custom email sending setup.
    // Let's assume for now we have a function sendCustomVerificationEmail.
    // This part will need to be fleshed out with an actual email sending mechanism.

    try {
      const emailHtml = createConfirmationEmailHtml(name.trim(), confirmationUrl);
      await sendEmail({
        to: email,
        subject: 'Confirm Your Subscription - Bela Gallery',
        html: emailHtml,
      });
      console.log(`Confirmation email sent to ${email}`);
    } catch (emailError) {
      console.error(`Failed to send confirmation email to ${email}:`, emailError);
      // Decide how to handle this. The subscription is in DB but email failed.
      // Options:
      // 1. Return success to user but log the email error for admin to check.
      // 2. Return an error to user indicating subscription happened but email failed.
      // 3. Attempt to delete the Firestore record (or mark as 'email_failed').
      // For now, let's log it and still return success to the user, as the main action (DB write) succeeded.
      // The user can be informed on the frontend or via a different message if needed.
    }

    return NextResponse.json({ message: 'Subscription successful! Please check your email to confirm.' }, { status: 201 });

  } catch (error) {
    console.error('Subscription error:', error);
    // Check if error is an instance of Error to safely access message property
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
