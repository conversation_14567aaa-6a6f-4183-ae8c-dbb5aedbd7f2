import type {Metada<PERSON>} from 'next';
import {Inter} from 'next/font/google';
import MainLayout from '@/components/MainLayout';
import './globals.css';
import '../styles/modern-fine-art-theme.css';

const inter = Inter({
    subsets: ['latin'],
    variable: '--font-inter',
});

export const metadata: Metadata = {
    title: 'Bela Raval Art Gallery',
    description: 'Explore the artwork of Bela Raval',
};

export default function RootLayout({children}: {
    children: React.ReactNode;
}) {
    return (
        <html lang="en">
            <body className={`${inter.variable} font-sans bg-modernArt-background text-modernArt-foreground`}>
                <MainLayout>
                    {children}
                </MainLayout>
            </body>
        </html>
    );
}
