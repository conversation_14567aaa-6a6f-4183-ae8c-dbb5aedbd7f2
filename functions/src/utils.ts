import * as functions from "firebase-functions";

// Test emulator function
export const testEmulatorFunction = functions.https.onRequest(async (req, res) => {
  // Enable CORS
  res.set("Access-Control-Allow-Origin", "*");
  res.set("Access-Control-Allow-Methods", "GET, OPTIONS");
  res.set("Access-Control-Allow-Headers", "Content-Type");
  
  if (req.method === "OPTIONS") {
    res.status(204).send("");
    return;
  }

  if (req.method !== "GET") {
    res.status(405).send("Method Not Allowed");
    return;
  }

  try {
    const emulatorHost = process.env.NEXT_PUBLIC_FIREBASE_STORAGE_EMULATOR_HOST || 'localhost:9199';
    const testUrl = `http://${emulatorHost}/v0/b/test/o/test?alt=media`;
    
    functions.logger.info(`Testing emulator access from server: ${testUrl}`);
    
    const response = await fetch(testUrl, {
      method: 'HEAD',
      signal: AbortSignal.timeout(3000)
    });
    
    res.status(200).json({
      success: true,
      status: response.status,
      emulatorHost,
      message: 'Emulator is accessible from server-side'
    });
  } catch (error: any) {
    functions.logger.error('Server-side emulator test failed:', error);
    res.status(200).json({
      success: false,
      error: error.message,
      code: error.code,
      message: 'Emulator not accessible from server-side'
    });
  }
});
