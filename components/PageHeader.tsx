import React from 'react';

interface PageHeaderProps {
  title: string;
  subtitle?: string; // For text like 'Fine Art', 'Biography', 'Calendar'
  description?: string; // For the introductory paragraph below the header
  className?: string;
}

export function PageHeader({ title, subtitle, description, className = '' }: PageHeaderProps) {
  return (
    <div className={`w-full text-center mb-12 ${className}`}>
      <h1 className="text-4xl md:text-5xl font-cormorant font-light text-elegant-text mt-8 mb-4 tracking-wide">
        {title}
      </h1>
      {subtitle && (
        <div className="flex justify-center items-center mb-6">
          <div className="w-16 h-px bg-elegant-accent mx-2"></div>
          <span className="font-montserrat text-xs uppercase tracking-widest text-elegant-text/70">
            {subtitle}
          </span>
          <div className="w-16 h-px bg-elegant-accent mx-2"></div>
        </div>
      )}
      {description && (
        <p className="text-center font-montserrat text-elegant-text/80 text-lg max-w-2xl mx-auto">
          {description}
        </p>
      )}
    </div>
  );
}

export default PageHeader;
