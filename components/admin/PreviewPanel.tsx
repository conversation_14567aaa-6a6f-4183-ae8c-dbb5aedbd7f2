"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { getPublicImageUrl } from "@/lib/image-paths";

interface Exhibition {
  title: string;
  date: string;
  location: string;
}

interface Award {
  title: string;
  year: string;
}

interface HomeContent {
  heroTitle?: string;
  heroText?: string;
  profileImage?: string;
  aboutText?: string[];
  artworks?: Array<{ title: string; image: string }>;
}

interface EventContent {
  events?: Array<{
    title: string;
    date: string;
    location: string;
    description: string;
    images?: { main?: string; details?: string };
  }>;
}

interface GalleryContent {
  categories?: Array<{
    title: string;
    image: string;
  }>;
}

interface AboutContent {
  profileImage?: string;
  statement?: string[];
  exhibitions?: {
    solo?: Exhibition[];
    group?: Exhibition[];
    juried?: Exhibition[];
  };
  awards?: Award[];
}

interface ContactContent {
  header?: string;
  sectionTitle?: string;
  description?: string;
}

type PreviewContent =
  | HomeContent
  | EventContent
  | GalleryContent
  | AboutContent
  | ContactContent;

interface PreviewPanelProps {
  content: PreviewContent;
  type: "home" | "events" | "gallery" | "about" | "contact";
}

function isHomeContent(content: PreviewContent): content is HomeContent {
  return "heroTitle" in content;
}

function isEventContent(content: PreviewContent): content is EventContent {
  return "events" in content;
}

function isGalleryContent(content: PreviewContent): content is GalleryContent {
  return "categories" in content;
}

function isAboutContent(content: PreviewContent): content is AboutContent {
  return "statement" in content;
}

// Define a placeholder image as data URL to avoid 404 errors
const placeholderImage = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='%23f0f0f0'/%3E%3Ctext x='50' y='50' font-family='Arial' font-size='14' text-anchor='middle' alignment-baseline='middle' fill='%23999999'%3ENo Image%3C/text%3E%3C/svg%3E";

export function PreviewPanel({ content, type }: PreviewPanelProps) {
  const [resolvedImageUrls, setResolvedImageUrls] = useState<
    Record<string, string>
  >({});

  useEffect(() => {
    // Only resolve images if the component is actually being shown
    // This prevents unnecessary Firestore operations
    async function resolveAllImages() {
      if (!content) return;

      let imagePaths: string[] = [];

      // Collect image paths based on content type
      switch (type) {
        case "home":
          if (isHomeContent(content)) {
            const paths = [
              content.profileImage,
              ...(content.artworks?.map((a) => a.image) || []),
            ].filter((path): path is string => Boolean(path));
            imagePaths = paths;
          }
          break;
        case "events":
          if (isEventContent(content)) {
            const paths =
              content.events
                ?.flatMap((event) => [
                  event.images?.main,
                  event.images?.details,
                ])
                .filter((path): path is string => Boolean(path)) || [];
            imagePaths = paths;
          }
          break;
        case "gallery":
          if (isGalleryContent(content)) {
            const paths =
              content.categories
                ?.map((cat) => cat.image)
                .filter((path): path is string => Boolean(path)) || [];
            imagePaths = paths;
          }
          break;
        case "about":
          if (isAboutContent(content) && content.profileImage) {
            imagePaths = [content.profileImage];
          }
          break;
        default:
          break;
      }

      // Resolve all collected image paths
      if (imagePaths.length > 0) {
        const uniquePaths = Array.from(new Set(imagePaths));
        const urls: Record<string, string> = {};

        await Promise.all(
          uniquePaths.map(async (path) => {
            if (path && path.trim()) {
              try {
                const url = await getPublicImageUrl(path);
                urls[path] = url;
              } catch (error) {
                console.error("Error resolving image:", error);
                urls[path] = placeholderImage;
              }
            }
          })
        );

        setResolvedImageUrls(urls);
      }
    }

    resolveAllImages();
  }, [content, type]);

  // Render preview based on content type
  const renderPreview = () => {
    if (!content)
      return <div className="text-center p-4">No content to preview</div>;

    switch (type) {
      case "home":
        if (!isHomeContent(content)) return null;
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-cormorant font-light text-elegant-text">
                {content.heroTitle || "Hero Title"}
              </h2>
              {content.heroText && (
                <p className="text-elegant-text/80 font-montserrat text-sm mt-2">
                  {content.heroText}
                </p>
              )}
            </div>

            {content.profileImage && (
              <div className="flex items-center space-x-4">
                <div className="relative w-20 h-20 overflow-hidden rounded-full">
                  <Image
                    src={
                      resolvedImageUrls[content.profileImage] ||
                      placeholderImage
                    }
                    alt="Profile"
                    width={80}
                    height={80}
                    className="object-cover"
                  />
                </div>
                <div>
                  <h3 className="font-cormorant">Bela Raval</h3>
                  {content.aboutText?.[0] && (
                    <p className="text-sm text-elegant-text/80 font-montserrat">
                      {content.aboutText[0].length > 60
                        ? content.aboutText[0].substring(0, 60) + "..."
                        : content.aboutText[0]}
                    </p>
                  )}
                </div>
              </div>
            )}

            {content.artworks && content.artworks.length > 0 && (
              <div className="space-y-2">
                <h3 className="font-cormorant text-lg">Featured Artworks</h3>
                <div className="grid grid-cols-3 gap-2">
                  {content.artworks.slice(0, 3).map((artwork, i) => (
                    <div key={i} className="border border-elegant p-1">
                      <div className="relative aspect-square w-full">
                        <Image
                          src={
                            resolvedImageUrls[artwork.image] ||
                            placeholderImage
                          }
                          alt={artwork.title}
                          width={100}
                          height={100}
                          className="object-cover w-full h-full"
                        />
                      </div>
                      <p className="text-xs font-montserrat mt-1 truncate">
                        {artwork.title}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        );

      case "events":
        if (!isEventContent(content)) return null;
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-cormorant font-light text-elegant-text text-center">
              Events & Exhibitions
            </h2>

            {content.events && content.events.length > 0 ? (
              <div className="space-y-4">
                {content.events.slice(0, 2).map((event, i) => (
                  <div key={i} className="border border-elegant p-2">
                    <h3 className="font-cormorant text-lg">{event.title}</h3>
                    <div className="flex justify-between text-xs font-montserrat text-elegant-text/80">
                      <span>{event.date}</span>
                      <span>{event.location}</span>
                    </div>
                    {event.images?.main && (
                      <div className="relative h-16 mt-2">
                        <Image
                          src={
                            resolvedImageUrls[event.images.main] ||
                            placeholderImage
                          }
                          alt={event.title}
                          width={50}
                          height={32}
                          className="object-contain mx-auto"
                        />
                      </div>
                    )}
                    <p className="text-xs mt-2">
                      {event.description.length > 80
                        ? event.description.substring(0, 80) + "..."
                        : event.description}
                    </p>
                  </div>
                ))}
                {content.events.length > 2 && (
                  <p className="text-xs text-center text-elegant-text/60">
                    +{content.events.length - 2} more events
                  </p>
                )}
              </div>
            ) : (
              <p className="text-center text-sm text-elegant-text/60">
                No events added
              </p>
            )}
          </div>
        );

      case "gallery":
        if (!isGalleryContent(content)) return null;
        return (
          <div className="space-y-4">
            <h2 className="text-xl font-cormorant font-light text-elegant-text text-center">
              Gallery
            </h2>

            {content.categories && content.categories.length > 0 ? (
              <div className="grid grid-cols-2 gap-2">
                {content.categories.slice(0, 4).map((category, i) => (
                  <div key={i} className="border border-elegant p-1">
                    {category.image && (
                      <div className="relative h-20">
                        <Image
                          src={
                            resolvedImageUrls[category.image] ||
                            placeholderImage
                          }
                          alt={category.title}
                          width={100}
                          height={80}
                          className="object-cover w-full h-full"
                        />
                      </div>
                    )}
                    <p className="text-xs font-montserrat mt-1 truncate">
                      {category.title}
                    </p>
                  </div>
                ))}
                {content.categories.length > 4 && (
                  <p className="text-xs text-center text-elegant-text/60 col-span-2">
                    +{content.categories.length - 4} more categories
                  </p>
                )}
              </div>
            ) : (
              <p className="text-center text-sm text-elegant-text/60">
                No categories added
              </p>
            )}
          </div>
        );

      case "about":
        if (!isAboutContent(content)) return null;
        return (
          <div className="space-y-4">
            <h2 className="text-xl font-cormorant font-light text-elegant-text text-center">
              About the Artist
            </h2>

            {content.profileImage && (
              <div className="flex justify-center">
                <div className="relative w-20 h-20 overflow-hidden rounded-full">
                  <Image
                    src={
                      resolvedImageUrls[content.profileImage] ||
                      placeholderImage
                    }
                    alt="Profile"
                    width={80}
                    height={80}
                    className="object-cover w-full h-full"
                  />
                </div>
              </div>
            )}

            {content.statement && content.statement.length > 0 && (
              <div>
                <h3 className="text-sm font-medium">Artist Statement</h3>
                <p className="text-xs">
                  {content.statement[0].length > 100
                    ? content.statement[0].substring(0, 100) + "..."
                    : content.statement[0]}
                </p>
              </div>
            )}

            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>
                <h3 className="text-sm font-medium">Exhibitions</h3>
                <p>
                  {content.exhibitions?.solo?.length || 0} Solo,
                  {content.exhibitions?.group?.length || 0} Group,
                  {content.exhibitions?.juried?.length || 0} Juried
                </p>
              </div>
              <div>
                <h3 className="text-sm font-medium">Awards</h3>
                <p>{content.awards?.length || 0} Awards</p>
              </div>
            </div>
          </div>
        );

      case "contact":
        if (!("header" in content)) return null;
        return (
          <div className="space-y-4">
            <h2 className="text-xl font-cormorant font-light text-elegant-text text-center">
              {content.header || "Contact"}
            </h2>

            <div className="space-y-2">
              <div className="border border-elegant p-2">
                <h3 className="text-sm font-medium">
                  {content.sectionTitle || "Get in Touch"}
                </h3>
                <p className="text-xs">
                  {content.description && content.description.length > 80
                    ? content.description.substring(0, 80) + "..."
                    : content.description || "Contact description"}
                </p>
              </div>
              <div className="border border-elegant p-2">
                <p className="text-xs font-medium">Contact Form</p>
                <div className="flex flex-col gap-1 mt-1">
                  <div className="h-2 bg-elegant/20 rounded w-full"></div>
                  <div className="h-2 bg-elegant/20 rounded w-full"></div>
                  <div className="h-2 bg-elegant/20 rounded w-full"></div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return <div className="text-center p-4">Preview not available</div>;
    }
  };

  return (
    <div className="border rounded-md p-4 bg-elegant-light/50">
      <h3 className="text-sm font-medium mb-3 text-elegant-text/80">
        Live Preview
      </h3>
      {renderPreview()}
      <p className="text-xs text-elegant-text/60 mt-4 italic">
        This is a simplified preview. Content is intentionally truncated.
      </p>
    </div>
  );
}

export default PreviewPanel;