import { describe, it, expect, vi, beforeEach } from "vitest";
import { getAllCategories, getCategoryBySlug } from "./gallery";
import { fetchContent } from "./content-repository";

// Mock content-repository
vi.mock("./content-repository", () => ({
  fetchContent: vi.fn(),
}));

describe("gallery", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("getAllCategories", () => {
    it("returns empty array when fetchContent returns empty object", async () => {
      // Mock fetchContent to return empty object
      (fetchContent as any).mockResolvedValue({});

      const result = await getAllCategories();
      expect(result).toEqual([]);
      expect(fetchContent).toHaveBeenCalledWith('live', 'gallery', {
        defaultValue: {}
      });
    });

    it("returns categories from standard structure", async () => {
      const mockCategories = [
        { name: "Category 1", slug: "category-1" },
        { name: "Category 2", slug: "category-2" },
      ];

      // Mock fetchContent to return data with categories
      (fetchContent as any).mockResolvedValue({
        categories: mockCategories,
      });

      const result = await getAllCategories();
      expect(result).toEqual(mockCategories);
    });

    it("returns categories from nested structure", async () => {
      const mockCategories = [
        { name: "Category 1", slug: "category-1" },
        { name: "Category 2", slug: "category-2" },
      ];

      // Mock fetchContent to return data with nested categories
      (fetchContent as any).mockResolvedValue({
        categories: {
          categories: mockCategories,
        },
      });

      const result = await getAllCategories();
      expect(result).toEqual(mockCategories);
    });

    it("returns empty array when categories structure is invalid", async () => {
      // Mock fetchContent to return data with invalid categories structure
      (fetchContent as any).mockResolvedValue({
        categories: "not an array",
      });

      const result = await getAllCategories();
      expect(result).toEqual([]);
    });
  });

  describe("getCategoryBySlug", () => {
    it("returns null when fetchContent returns empty object", async () => {
      // Mock fetchContent to return empty object
      (fetchContent as any).mockResolvedValue({});

      const result = await getCategoryBySlug("test-slug");
      expect(result).toBeNull();
      expect(fetchContent).toHaveBeenCalledWith('live', 'gallery', {
        defaultValue: {}
      });
    });

    it("returns null when categories structure is invalid", async () => {
      // Mock fetchContent to return data with invalid categories structure
      (fetchContent as any).mockResolvedValue({
        categories: "not an array",
      });

      const result = await getCategoryBySlug("test-slug");
      expect(result).toBeNull();
    });

    it("returns null when category is not found", async () => {
      const mockCategories = [
        { name: "Category 1", slug: "category-1" },
        { name: "Category 2", slug: "category-2" },
      ];

      // Mock fetchContent to return data with categories
      (fetchContent as any).mockResolvedValue({
        categories: mockCategories,
      });

      const result = await getCategoryBySlug("non-existent-slug");
      expect(result).toBeNull();
    });

    it("returns category with artworks from slugs array", async () => {
      const mockCategories = [
        { name: "Category 1", slug: "category-1" },
        { name: "Category 2", slug: "category-2" },
      ];

      const mockArtworks = [
        { title: "Artwork 1", image: "/image1.jpg" },
        { title: "Artwork 2", image: "/image2.jpg" },
      ];

      // Mock fetchContent to return data with categories and slugs array
      (fetchContent as any).mockResolvedValue({
        categories: mockCategories,
        slugs: [
          { slug: "category-1", artworks: mockArtworks },
          { slug: "category-2", artworks: [] },
        ],
      });

      const result = await getCategoryBySlug("category-1");
      expect(result).toEqual({ artworks: mockArtworks });
    });

    it("returns category with artworks from slugs object", async () => {
      const mockCategories = [
        { name: "Category 1", slug: "category-1" },
        { name: "Category 2", slug: "category-2" },
      ];

      const mockArtworks = [
        { title: "Artwork 1", image: "/image1.jpg" },
        { title: "Artwork 2", image: "/image2.jpg" },
      ];

      // Mock fetchContent to return data with categories and slugs object
      (fetchContent as any).mockResolvedValue({
        categories: mockCategories,
        slugs: {
          "category-1": mockArtworks,
          "category-2": [],
        },
      });

      const result = await getCategoryBySlug("category-1");
      expect(result).toEqual({ artworks: mockArtworks });
    });
  });
});