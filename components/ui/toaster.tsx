"use client";

import {useToast} from '@/components/ui/use-toast';
import {Toast} from '@/components/ui/toast';

export function Toaster() {
    const {toasts} = useToast();
    
    return (
        <>
            {toasts.map(({id, title, description, ...props}) => (
            <Toast
                key={id}
                message={title ? `${title}: ${description}` : description?.toString() || ''}
                {...props}
            />
        ))}
        </>
    );
}
