import { NextRequest, NextResponse } from 'next/server';
import { adminDb } from '@/lib/firebase-admin';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category') || '';
    const limit = parseInt(searchParams.get('limit') || '20');

    // Get artworks from the artworks collection
    const artworksRef = adminDb.collection('artworks');
    
    // Build query with category filter if specified
    const query = category && category !== 'all'
      ? artworksRef.where('category', '==', category)
      : artworksRef;

    // Get the artworks
    const artworksSnapshot = await query.limit(limit * 2).get(); // Get more to allow for filtering

    interface ArtworkData {
      id: string;
      title?: string;
      description?: string;
      medium?: string;
      tags?: string[];
      image?: string;
      year?: string;
      category?: string;
    }

    let artworks: ArtworkData[] = artworksSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as ArtworkData));

    // Apply search filter in memory (since Firestore doesn't support full-text search easily)
    if (search) {
      const searchLower = search.toLowerCase();
      artworks = artworks.filter((artwork) =>
        artwork.title?.toLowerCase().includes(searchLower) ||
        artwork.description?.toLowerCase().includes(searchLower) ||
        artwork.medium?.toLowerCase().includes(searchLower) ||
        artwork.tags?.some((tag: string) => tag.toLowerCase().includes(searchLower))
      );
    }

    // Limit results after filtering
    artworks = artworks.slice(0, limit);

    // Format artworks for newsletter selection
    const formattedArtworks = artworks.map((artwork) => ({
      id: artwork.id,
      title: artwork.title || 'Untitled',
      description: artwork.description || '',
      imageUrl: artwork.image || '',
      year: artwork.year || '',
      medium: artwork.medium || '',
      category: artwork.category || '',
      galleryUrl: artwork.category ? `/gallery/${artwork.category}` : '/gallery'
    }));

    // Get unique categories for filtering
    const allArtworks = await adminDb.collection('artworks').get();
    const categories = [...new Set(allArtworks.docs.map(doc => doc.data().category).filter(Boolean))].sort();

    return NextResponse.json({ 
      artworks: formattedArtworks,
      categories 
    }, { status: 200 });

  } catch (error) {
    console.error('Error fetching artworks:', error);
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}