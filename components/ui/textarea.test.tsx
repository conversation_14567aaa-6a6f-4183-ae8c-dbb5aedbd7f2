import React from "react";
import { describe, it, expect } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { Textarea } from "./textarea";

describe("Textarea", () => {
  it("renders with default props", () => {
    render(<Textarea data-testid="textarea" />);
    const textarea = screen.getByTestId("textarea");
    expect(textarea).toBeInTheDocument();
    expect(textarea.tagName).toBe("TEXTAREA");
    expect(textarea).toHaveClass("min-h-[80px]");
    expect(textarea).toHaveClass("w-full");
    expect(textarea).toHaveClass("rounded-md");
  });

  it("applies custom className", () => {
    render(<Textarea data-testid="textarea" className="custom-class" />);
    const textarea = screen.getByTestId("textarea");
    expect(textarea).toHaveClass("custom-class");
  });

  it("forwards ref correctly", () => {
    const ref = React.createRef<HTMLTextAreaElement>();
    render(<Textarea ref={ref} data-testid="textarea" />);
    expect(ref.current).not.toBeNull();
    expect(ref.current).toBe(screen.getByTestId("textarea"));
  });

  it("passes additional props to the textarea element", () => {
    render(
      <Textarea
        data-testid="textarea"
        placeholder="Enter text"
        disabled
        required
        rows={5}
        aria-label="Textarea Label"
      />
    );
    const textarea = screen.getByTestId("textarea");
    expect(textarea).toHaveAttribute("placeholder", "Enter text");
    expect(textarea).toBeDisabled();
    expect(textarea).toBeRequired();
    expect(textarea).toHaveAttribute("rows", "5");
    expect(textarea).toHaveAttribute("aria-label", "Textarea Label");
  });

  it("handles value changes", async () => {
    render(<Textarea data-testid="textarea" />);
    const textarea = screen.getByTestId("textarea");
    
    await userEvent.type(textarea, "Hello World");
    expect(textarea).toHaveValue("Hello World");
  });

  it("handles controlled textarea", () => {
    const onChange = vi.fn();
    render(<Textarea data-testid="textarea" value="Controlled Value" onChange={onChange} />);
    const textarea = screen.getByTestId("textarea");
    
    expect(textarea).toHaveValue("Controlled Value");
    
    fireEvent.change(textarea, { target: { value: "New Value" } });
    expect(onChange).toHaveBeenCalledTimes(1);
  });

  it("applies disabled styles when disabled", () => {
    render(<Textarea data-testid="textarea" disabled />);
    const textarea = screen.getByTestId("textarea");
    expect(textarea).toHaveClass("disabled:cursor-not-allowed");
    expect(textarea).toHaveClass("disabled:opacity-50");
    expect(textarea).toBeDisabled();
  });
});