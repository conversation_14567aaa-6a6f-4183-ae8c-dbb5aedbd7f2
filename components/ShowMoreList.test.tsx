import React from "react";
import { describe, it, expect } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import { ShowMoreList } from "./ShowMoreList";

describe.skip("ShowMoreList", () => {
  const testItems = [
    "Item 1",
    "Item 2",
    "Item 3",
    "Item 4",
    "Item 5",
    "Item 6",
    "Item 7",
    "Item 8",
  ];

  it("renders initial items correctly", () => {
    render(
      <ShowMoreList initialCount={3}>
        {testItems.map((item, index) => (
          <div key={index} data-testid={`item-${index}`}>
            {item}
          </div>
        ))}
      </ShowMoreList>
    );

    // Check that only the first 3 items are visible
    expect(screen.getByTestId("item-0")).toBeInTheDocument();
    expect(screen.getByTestId("item-1")).toBeInTheDocument();
    expect(screen.getByTestId("item-2")).toBeInTheDocument();
    
    // Check that the rest are not visible
    expect(screen.queryByTestId("item-3")).not.toBeInTheDocument();
    expect(screen.queryByTestId("item-7")).not.toBeInTheDocument();
    
    // Check that the "Show More" button is visible
    expect(screen.getByText("Show More")).toBeInTheDocument();
  });

  it("shows more items when 'Show More' is clicked", () => {
    render(
      <ShowMoreList initialCount={3}>
        {testItems.map((item, index) => (
          <div key={index} data-testid={`item-${index}`}>
            {item}
          </div>
        ))}
      </ShowMoreList>
    );

    // Click the "Show More" button
    fireEvent.click(screen.getByText("Show More"));
    
    // Check that all items are now visible
    for (let i = 0; i < testItems.length; i++) {
      expect(screen.getByTestId(`item-${i}`)).toBeInTheDocument();
    }
    
    // Check that the "Show More" button is no longer visible
    expect(screen.queryByText("Show More")).not.toBeInTheDocument();
  });

  it("doesn't show 'Show More' button when all items fit within initialCount", () => {
    render(
      <ShowMoreList initialCount={10}>
        {testItems.map((item, index) => (
          <div key={index} data-testid={`item-${index}`}>
            {item}
          </div>
        ))}
      </ShowMoreList>
    );

    // Check that all items are visible
    for (let i = 0; i < testItems.length; i++) {
      expect(screen.getByTestId(`item-${i}`)).toBeInTheDocument();
    }
    
    // Check that the "Show More" button is not visible
    expect(screen.queryByText("Show More")).not.toBeInTheDocument();
  });

  it("applies custom className to the container", () => {
    render(
      <ShowMoreList initialCount={3} className="custom-class">
        {testItems.map((item, index) => (
          <div key={index}>{item}</div>
        ))}
      </ShowMoreList>
    );

    // The first div should have the custom class
    const container = screen.getByText("Item 1").parentElement?.parentElement;
    expect(container).toHaveClass("custom-class");
  });

  it("renders with custom button text", () => {
    render(
      <ShowMoreList initialCount={3} showMoreText="Load More Items">
        {testItems.map((item, index) => (
          <div key={index}>{item}</div>
        ))}
      </ShowMoreList>
    );

    expect(screen.getByText("Load More Items")).toBeInTheDocument();
  });

  it("handles empty children gracefully", () => {
    render(<ShowMoreList initialCount={3}>{[]}</ShowMoreList>);
    
    // Should not crash and should not show the button
    expect(screen.queryByText("Show More")).not.toBeInTheDocument();
  });
});