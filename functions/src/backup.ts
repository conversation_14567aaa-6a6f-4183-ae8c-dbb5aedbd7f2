import * as admin from "firebase-admin";
import * as functions from "firebase-functions";

if (!admin.apps.length) admin.initializeApp();

const db = admin.firestore();

/**
 * Creates a version snapshot of the current content
 * Using a flatter structure with composite IDs to avoid Firestore limitations
 */
export const createVersionSnapshot = async (
  batch: admin.firestore.WriteBatch,
  versionId: string
) => {
  // Pages to snapshot (all live pages)
  const pages = ["home", "about", "events", "menu", "contact", "gallery"];

  // Create version metadata with empty arrays for pages and gallery items
  const metaRef = db.collection("backup_meta").doc(versionId);

  const now = new Date();

  batch.set(metaRef, {
    timestamp: now,
    createdAt: now,
    pages: [],
    // gallery_items: [],
  });

  const includedPages: string[] = [];

  // Backup all live pages (home, about, events, menu, contact)
  for (const page of pages) {
    const liveSnap = await db.collection("live").doc(page).get();
    if (liveSnap.exists) {
      const data = liveSnap.data()!;
      const target = db.collection("backups").doc(`${page}_${versionId}`);
      functions.logger.info(
        `Backing up page: live/${page} to backups/${page}_${versionId}`
      );
      batch.set(target, {
        ...data,
        _meta: {
          page,
          versionId,
          timestamp: now,
        },
      });
      includedPages.push(page);
    } else {
      functions.logger.info(`No live content found for page: ${page}`);
    }
  }

  // Update the metadata with all pages and gallery items at once
  if (includedPages.length > 0)
    batch.update(metaRef, {
      pages: includedPages,
    });

};

/**
 * Retrieves a specific version of content
 */
export const getVersionContent = async (versionId: string) => {
  const metaRef = db.collection("backup_meta").doc(versionId);

  const metaSnap = await metaRef.get();

  if (!metaSnap.exists) throw Error(`Version ${versionId} not found`);

  const metadata = metaSnap.data()!;
  const pages: string[] = metadata.pages || [];

  // Get all page content
  const pagePromises = pages.map(async (page: string) => {
    const docRef = db.collection("backups").doc(`${page}_${versionId}`);

    const docSnap = await docRef.get();

    return {
      page,
      data: docSnap.exists ? docSnap.data() : null,
    };
  });


  const pageResults = await Promise.all(pagePromises);

  return {
    metadata,
    pages: pageResults.reduce<Record<string, unknown>>(
      (acc, { page, data }) => {
        if (data) acc[page] = data;

        return acc;
      },
      {}
    ),
  };
};

/**
 * Lists all available versions
 */
export const listVersions = async () => {
  const metaSnap = await db
    .collection("backup_meta")
    .orderBy("timestamp", "desc")
    .get();

  return metaSnap.docs.map((doc) => ({
    id: doc.id,
    ...doc.data(),
  }));
};

/**
 * Deletes a specific version
 */
export const deleteVersion = async (versionId: string) => {
  const metaRef = db.collection("backup_meta").doc(versionId);
  const metaSnap = await metaRef.get();

  if (!metaSnap.exists) {
    throw new Error(`Version ${versionId} not found`);
  }

  const metadata = metaSnap.data()!;
  const pages = metadata.pages || [];
  const batch = db.batch();

  // Delete backup documents for pages
  for (const page of pages) {
    const backupRef = db.collection("backups").doc(`${page}_${versionId}`);
    batch.delete(backupRef);
  }

  // Delete the metadata document
  batch.delete(metaRef);

  // Commit the batch
  await batch.commit();

  return { success: true };
};
