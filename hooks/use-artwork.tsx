'use client';

import {
    useState,
    useEffect,
    useCallback,
} from 'react';
import {Artwork} from '@/types/artworks';
import {
    getArtwork,
    getArtworks,
    getArtworksByIds,
} from '@/lib/artwork-repository';
import {useImageResolver} from './use-image-resolver';

const noop = () => {};
const isError = (a) => a instanceof Error;

interface UseArtworkReturn {
    artwork: Artwork | null;
    isLoading: boolean;
    error: Error | null;
}

interface UseArtworksReturn {
    artworks: Artwork[];
    isLoading: boolean;
    error: Error | null;
    pagination: {
        hasMore: boolean;
        loadMore: () => void;
        isLoadingMore: boolean;
        totalCount?: number;
        page: number;
    };
}

/**
 * Hook to fetch a single artwork by ID
 */
export function useArtwork(id: string | undefined): UseArtworkReturn {
    const [artwork, setArtwork] = useState<Artwork | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<Error | null>(null);
    const {resolveImages} = useImageResolver();
    
    useEffect(() => {
        if (!id) {
            setArtwork(null);
            return;
        }
        
        const fetchArtwork = async () => {
            setIsLoading(true);
            setError(null);
            
            try {
                const data = await getArtwork(id);
                setArtwork(data);
                // Resolve image URL
                if (data?.image)
                    resolveImages([data.image]);
            } catch(err) {
                setError(isError(err) ? err : Error('Failed to fetch artwork'));
            }
 finally {
                setIsLoading(false);
            }
        };
        
        fetchArtwork();
    }, [id, resolveImages]);
    
    return {
        artwork,
        isLoading,
        error,
    };
}

/**
 * Hook to fetch multiple artworks with filtering options and pagination
 */
export function useArtworks(options: {
    category?: string;
    tags?: string[];
    sortBy?: 'title' | 'year' | 'createdAt' | 'updatedAt';
    sortDirection?: 'asc' | 'desc';
    limit?: number;
    pageSize?: number;
} = {}): UseArtworksReturn {
    const [artworks, setArtworks] = useState<Artwork[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
    const [error, setError] = useState<Error | null>(null);
    const [page, setPage] = useState<number>(1);
    const [hasMore, setHasMore] = useState<boolean>(false);
    const [lastVisible, setLastVisible] = useState<any>(null);
    const [totalCount, setTotalCount] = useState<number | undefined>(undefined);
    const {resolveImages} = useImageResolver();
    
    const {pageSize = 12, ...restOptions} = options;
    
    // Initial data fetch
    useEffect(() => {
        const fetchArtworks = async () => {
            setIsLoading(true);
            setError(null);
            
            try {
                const result = await getArtworks({
                    ...restOptions,
                    pageSize,
                    page: 1,
                });
                
                setArtworks(result.artworks);
                setLastVisible(result.lastVisible);
                setHasMore(result.hasMore);
                setTotalCount(result.totalCount);
                setPage(1);
                // Resolve all image URLs

                                const imagePaths = result
                    .artworks
                    .map((artwork) => artwork.image)
                    .filter(Boolean);
                
                if (imagePaths.length > 0)
                    resolveImages(imagePaths);
            } catch(err) {
                setError(isError(err) ? err : Error('Failed to fetch artworks'));
            }
 finally {
                setIsLoading(false);
            }
        };
        
        fetchArtworks();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [JSON.stringify(restOptions), pageSize]);

    // Load more function for pagination
    const loadMore = useCallback(async () => {
        if (!hasMore || isLoadingMore)
            return;
        
        setIsLoadingMore(true);
        
        try {
            const result = await getArtworks({
                ...restOptions,
                pageSize,
                lastVisible,
            });
            
            setArtworks((prev) => [
                ...prev,
                ...result.artworks,
            ]);
            setLastVisible(result.lastVisible);
            setHasMore(result.hasMore);
            setPage((prev) => prev + 1);
            // Resolve new image URLs

                        const imagePaths = result
                .artworks
                .map((artwork) => artwork.image)
                .filter(Boolean);
            
            if (imagePaths.length > 0)
                resolveImages(imagePaths);
        } catch(err) {
            setError(isError(err) ? err : Error('Failed to load more artworks'));
        }
 finally {
            setIsLoadingMore(false);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        hasMore,
        isLoadingMore,
        lastVisible,
        pageSize,
        JSON.stringify(restOptions),
    ]);
    
    return {
        artworks,
        isLoading,
        error,
        pagination: {
            hasMore,
            loadMore,
            isLoadingMore,
            totalCount,
            page,
        },
    };
}

/**
 * Hook to fetch multiple artworks by their IDs
 */
export function useArtworksByIds(ids: string[] = []): UseArtworksReturn {
    const [artworks, setArtworks] = useState<Artwork[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<Error | null>(null);
    const {resolveImages} = useImageResolver();
    
    useEffect(() => {
        if (!ids || !ids.length) {
            setArtworks([]);
            return;
        }
        
        const fetchArtworks = async () => {
            setIsLoading(true);
            setError(null);
            
            try {
                const data = await getArtworksByIds(ids);
                setArtworks(data);
                // Resolve all image URLs

                                const imagePaths = data
                    .map((artwork) => artwork.image)
                    .filter(Boolean);
                
                if (imagePaths.length > 0)
                    resolveImages(imagePaths);
            } catch(err) {
                setError(isError(err) ? err : Error('Failed to fetch artworks'));
            }
 finally {
                setIsLoading(false);
            }
        };
        
        fetchArtworks();
    }, [ids, resolveImages]);

    // Provide a dummy pagination object for consistency with useArtworks
    return {
        artworks,
        isLoading,
        error,
        pagination: {
            hasMore: false,
            loadMore: noop,
            isLoadingMore: false,
            page: 1,
        },
    };
}
