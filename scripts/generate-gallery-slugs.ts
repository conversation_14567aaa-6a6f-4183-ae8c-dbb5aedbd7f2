import 'dotenv/config';
import { config } from 'dotenv';
import { writeFileSync } from 'node:fs';
import { exit } from 'node:process';
import { initializeApp, applicationDefault } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';

// Load environment variables
config({
  path: '.env.local',
});

// Use emulator if NEXT_PUBLIC_FIREBASE_USE_EMULATOR is set to true
if (process.env.NEXT_PUBLIC_FIREBASE_USE_EMULATOR === 'true') {
  process.env.FIRESTORE_EMULATOR_HOST = process.env.FIRESTORE_EMULATOR_HOST || 'localhost:8080';
}

// Ensure the Admin SDK uses the same project ID as the emulator/app
process.env.GCLOUD_PROJECT = process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'belagallery-9e01b';
process.env.FIREBASE_CONFIG = JSON.stringify({
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'belagallery-9e01b',
});

// Initialize Firebase Admin SDK
initializeApp({
  credential: applicationDefault(),
});

const db = getFirestore();

interface Category {
  slug: string;
  title: string;
  description?: string;
  image?: string;
}

interface GalleryData {
  categories?: Category[] | {
    categories: Category[];
  };
}

async function main(): Promise<void> {
  const galleryDoc = await db
    .collection('live')
    .doc('gallery')
    .get();
  
  if (!galleryDoc.exists) {
    throw new Error('No gallery document found in Firestore.');
  }
  
  const data = galleryDoc.data() as GalleryData;
  let categories: Category[] = [];
  
  // Handle both possible structures
  if (data.categories && Array.isArray(data.categories)) {
    categories = data.categories;
  } else if (data.categories && typeof data.categories === 'object' && 'categories' in data.categories && Array.isArray(data.categories.categories)) {
    // Handle nested structure if present
    categories = data.categories.categories;
  }
  
  const slugs = categories.map((cat) => cat.slug);
  const correctOutputPath = '/mnt/bottomleft/projects/bela/bela-gallery/app/gallery/[slug]/slugs.json';
  
  writeFileSync(correctOutputPath, JSON.stringify(slugs, null, 2));
}

main().catch((error: Error) => {
  console.error('\x1b[31m%s\x1b[0m', 'Error generating gallery slugs:');
  
  // Check for common Firestore connection errors
  const isConnectionError = (msg: string): boolean => 
    msg?.includes('UNAVAILABLE') || msg?.includes('ECONNREFUSED');
    
  if (isConnectionError(error.message)) {
    console.error('\x1b[33m%s\x1b[0m', 'Firestore is not reachable. If in emulator mode, emulator needs to be started:');
    console.error('\x1b[36m%s\x1b[0m', 'cd functions && npm run start');
    console.error('\x1b[33m%s\x1b[0m', 'Then try building again.');
  } else {
    console.error(error);
  }
  
  exit(1);
});