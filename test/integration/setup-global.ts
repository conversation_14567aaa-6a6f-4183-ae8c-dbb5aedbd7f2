import { FullConfig } from '@playwright/test';
import { execSync } from 'child_process';
import { readFileSync } from 'fs';
import { resolve } from 'path';

// Load emulator ports from firebase.test.json
const configPath = resolve(__dirname, '../../firebase.test.json');
const config = JSON.parse(readFileSync(configPath, 'utf8'));
const firestorePort = config.emulators.firestore.port;
const storagePort = config.emulators.storage.port;

// Global setup function that runs before all tests
async function globalSetup(_config: FullConfig) {
  console.log('Starting Firebase emulators...');
  console.log(`Using ports - Firestore: ${firestorePort}, Storage: ${storagePort}`);
  
  try {
    // Start Firebase emulators
    execSync('firebase emulators:start --only firestore,storage --project belagallery-test &', {
      stdio: 'inherit',
    });
    
    // Give emulators time to start
    console.log('Waiting for emulators to start...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    console.log('Firebase emulators started successfully');
  } catch (error) {
    console.error('Failed to start Firebase emulators:', error);
    throw error;
  }
}

// Global teardown function that runs after all tests
async function globalTeardown() {
  console.log('Stopping Firebase emulators...');
  
  try {
    // Kill emulator processes using ports from config
    execSync(`kill $(lsof -t -i:${firestorePort}) || true`, { stdio: 'inherit' });
    execSync(`kill $(lsof -t -i:${storagePort}) || true`, { stdio: 'inherit' });
    
    console.log('Firebase emulators stopped');
  } catch (error) {
    console.error('Error stopping emulators:', error);
  }
}

export { globalSetup, globalTeardown };