import type { NextConfig } from 'next';

const isStaticBuild = process.env.NEXT_PUBLIC_STATIC_BUILD === 'true';
const isDevelopment = process.env.NODE_ENV === 'development';

const nextConfig: NextConfig = {
  // Only use export output for static builds, not during development
  ...(isStaticBuild && { output: 'export' }),
  trailingSlash: true,
  // Only set custom distDir for builds, not development
  ...(isStaticBuild && { distDir: 'out' }),
  ...(!isStaticBuild && !isDevelopment && { distDir: 'out-dynamic' }),
  images: {
    unoptimized: true,
    domains: ['firebasestorage.googleapis.com'],
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  // Disable React strict mode in development to prevent double rendering
  reactStrictMode: false,
};

export default nextConfig;