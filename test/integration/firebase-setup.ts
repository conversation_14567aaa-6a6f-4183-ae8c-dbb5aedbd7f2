import { initializeApp } from 'firebase/app';
import { getFirestore, connectFirestoreEmulator, doc, setDoc, deleteDoc, collection, getDocs } from 'firebase/firestore';
import { getStorage, connectStorageEmulator } from 'firebase/storage';

// Firebase configuration for test emulator
const firebaseConfig = {
  projectId: 'belagallery-test',
  apiKey: 'fake-api-key',
  authDomain: 'localhost',
};

// Sample event data with more events to ensure tabs are visible
export const sampleEvents = {
  events: [
    {
      id: 'event1',
      title: 'Current Exhibition',
      description: 'A showcase of latest artworks',
      startDate: new Date().toISOString(), // Today
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
      location: 'Main Gallery',
      images: {
        main: 'events/exhibition1.jpg',
        details: 'events/exhibition1-detail.jpg',
      },
    },
    {
      id: 'event2',
      title: 'Upcoming Workshop',
      description: 'Learn watercolor techniques',
      startDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days from now
      endDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(), // 15 days from now
      location: 'Workshop Space',
      images: {
        main: 'events/workshop.jpg',
      },
    },
    {
      id: 'event3',
      title: 'Past Exhibition',
      description: 'Previous collection showcase',
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days ago
      endDate: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(), // 20 days ago
      location: 'East Wing',
      images: {
        main: 'events/past-exhibition.jpg',
      },
    },
    // Add more events to ensure tabs are visible
    {
      id: 'event4',
      title: 'Another Current Show',
      description: 'More amazing artworks',
      startDate: new Date().toISOString(), // Today
      endDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(), // 10 days from now
      location: 'West Gallery',
      images: {
        main: 'events/exhibition2.jpg',
      },
    },
    {
      id: 'event5',
      title: 'Future Exhibition',
      description: 'Coming soon',
      startDate: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000).toISOString(), // 20 days from now
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
      location: 'Main Hall',
      images: {
        main: 'events/future.jpg',
      },
    },
  ],
};

// Initialize Firebase with emulator connections
export function setupFirebase() {
  try {
    console.log('Setting up Firebase with emulator connections');
    
    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const db = getFirestore(app);
    const storage = getStorage(app);
    
    // Connect to Firestore emulator (using test ports)
    connectFirestoreEmulator(db, 'localhost', 8180);
    
    // Connect to Storage emulator (using test ports)
    connectStorageEmulator(storage, 'localhost', 9299);
    
    return { app, db, storage };
  } catch (error) {
    console.error('Error setting up Firebase:', error);
    throw error;
  }
}

// Clean up previous test data
export async function cleanupTestData() {
  console.log('Cleaning up previous test data');
  
  try {
    const { db } = setupFirebase();
    
    // Clean up live collection
    const liveSnapshot = await getDocs(collection(db, 'live'));
    for (const docRef of liveSnapshot.docs) {
      await deleteDoc(doc(db, 'live', docRef.id));
    }
    
    // Clean up preview collection
    const previewSnapshot = await getDocs(collection(db, 'preview'));
    for (const docRef of previewSnapshot.docs) {
      await deleteDoc(doc(db, 'preview', docRef.id));
    }
    
    console.log('Successfully cleaned up test data');
  } catch (error) {
    console.error('Error cleaning up test data:', error);
  }
}

// Seed the Firestore emulator with test data
export async function seedEventData() {
  console.log('Seeding event data to Firestore emulator');
  
  try {
    // Clean up previous test data first
    await cleanupTestData();
    
    const { db } = setupFirebase();
    
    // Add events data to Firestore
    await setDoc(doc(db, 'live', 'events'), sampleEvents);
    console.log('Successfully seeded event data');
    
    // Add the same data to preview collection for testing preview mode
    await setDoc(doc(db, 'preview', 'events'), sampleEvents);
    console.log('Successfully seeded preview event data');
    
    return true;
  } catch (error) {
    console.error('Error seeding event data:', error);
    return false;
  }
}