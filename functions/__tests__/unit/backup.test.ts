// functions/__tests__/backup.test.ts
import * as admin from 'firebase-admin';
import * as functions from 'firebase-functions';
import {
  createVersionSnapshot,
  getVersionContent,
  listVersions,
  deleteVersion
} from '../../src/backup';

// Import setup to ensure mocks are initialized
import './setup';

describe('Backup Functions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createVersionSnapshot', () => {
    const versionId = 'test-version-id';
    const batch = admin.firestore().batch();
    // Pages array as defined in backup.ts
    const PAGES = ['home', 'about', 'events', 'menu', 'contact', 'gallery'];

    it('should create a version snapshot with available live pages', async () => {
      // Setup - mock live pages data
      const livePagesData: Record<string, any> = {
        'home': { content: 'Home content', title: 'Home' },
        'about': { content: 'About content', title: 'About' },
        // Other pages don't exist in live
      };

      // Mock collection and doc behavior for specific paths
      jest.spyOn(admin.firestore(), 'collection').mockImplementation((collectionName: string) => {
        if (collectionName === 'live') {
          return {
            doc: jest.fn((pageId: string) => ({
              get: jest.fn().mockResolvedValue({
                exists: !!livePagesData[pageId],
                data: () => livePagesData[pageId],
                id: pageId,
              }),
            })),
          } as any;
        }
        return {
          doc: jest.fn((docPath: string) => ({
            _pathForTest: `${collectionName}/${docPath}`,
            set: jest.fn(),
            update: jest.fn(),
          })),
        } as any;
      });

      // Mock batch operations
      const mockBatchSet = jest.fn();
      const mockBatchUpdate = jest.fn();
      jest.spyOn(batch, 'set').mockImplementation(mockBatchSet);
      jest.spyOn(batch, 'update').mockImplementation(mockBatchUpdate);

      // Execute
      await createVersionSnapshot(batch, versionId);

      // Verify initial metadata document creation
      expect(mockBatchSet).toHaveBeenCalledWith(
        expect.objectContaining({ _pathForTest: `backup_meta/${versionId}` }),
        expect.objectContaining({
          timestamp: expect.any(Date),
          createdAt: expect.any(Date),
          pages: [],
        })
      );

      // Verify each page backup behavior
      PAGES.forEach(page => {
        if (livePagesData[page]) {
          // Page exists in live, should be backed up
          expect(mockBatchSet).toHaveBeenCalledWith(
            expect.objectContaining({ _pathForTest: `backups/${page}_${versionId}` }),
            expect.objectContaining({
              ...livePagesData[page],
              _meta: expect.objectContaining({
                page,
                versionId,
                timestamp: expect.any(Date),
              }),
            })
          );
          expect(functions.logger.info).toHaveBeenCalledWith(
            `Backing up page: live/${page} to backups/${page}_${versionId}`
          );
        } else {
          // Page doesn't exist in live, should log but not back up
          expect(functions.logger.info).toHaveBeenCalledWith(
            `No live content found for page: ${page}`
          );
        }
      });

      // Verify metadata update with included pages
      const includedPages = Object.keys(livePagesData);
      expect(mockBatchUpdate).toHaveBeenCalledWith(
        expect.objectContaining({ _pathForTest: `backup_meta/${versionId}` }),
        { pages: includedPages }
      );
    });

    it('should handle scenario where no pages exist in live', async () => {
      // Setup - no live pages exist
      jest.spyOn(admin.firestore(), 'collection').mockImplementation((collectionName: string) => {
        if (collectionName === 'live') {
          return {
            doc: jest.fn(() => ({
              get: jest.fn().mockResolvedValue({ exists: false, data: () => undefined }),
            })),
          } as any;
        }
        return {
          doc: jest.fn((docPath: string) => ({
            _pathForTest: `${collectionName}/${docPath}`,
            set: jest.fn(),
            update: jest.fn(),
          })),
        } as any;
      });

      // Mock batch operations
      const mockBatchSet = jest.fn();
      const mockBatchUpdate = jest.fn();
      jest.spyOn(batch, 'set').mockImplementation(mockBatchSet);
      jest.spyOn(batch, 'update').mockImplementation(mockBatchUpdate);

      // Execute
      await createVersionSnapshot(batch, versionId);

      // Verify initial metadata document creation
      expect(mockBatchSet).toHaveBeenCalledWith(
        expect.objectContaining({ _pathForTest: `backup_meta/${versionId}` }),
        expect.objectContaining({
          timestamp: expect.any(Date),
          createdAt: expect.any(Date),
          pages: [],
        })
      );

      // Verify each page was checked but not backed up
      PAGES.forEach(page => {
        expect(functions.logger.info).toHaveBeenCalledWith(
          `No live content found for page: ${page}`
        );
      });

      // Verify metadata was not updated with pages (since none were included)
      expect(mockBatchUpdate).not.toHaveBeenCalledWith(
        expect.objectContaining({ _pathForTest: `backup_meta/${versionId}` }),
        expect.objectContaining({ pages: expect.anything() })
      );
    });
  });

  describe('getVersionContent', () => {
    const versionId = 'test-version-content-id';

    it('should return version content successfully if version and pages exist', async () => {
      // Setup
      const metaData = { 
        timestamp: new Date(), 
        pages: ['home', 'about'], 
        createdAt: new Date() 
      };
      const homePageData = { 
        content: 'Home content', 
        title: 'Home', 
        _meta: { page: 'home', versionId, timestamp: new Date() } 
      };
      const aboutPageData = { 
        content: 'About content', 
        title: 'About', 
        _meta: { page: 'about', versionId, timestamp: new Date() } 
      };

      // Mock document get behavior for specific paths
      const mockDocGet = jest.fn().mockImplementation(function(this: any) {
        const path = this._pathForTest;
        if (path === `backup_meta/${versionId}`) {
          return Promise.resolve({ exists: true, data: () => metaData, id: versionId });
        }
        if (path === `backups/home_${versionId}`) {
          return Promise.resolve({ exists: true, data: () => homePageData });
        }
        if (path === `backups/about_${versionId}`) {
          return Promise.resolve({ exists: true, data: () => aboutPageData });
        }
        return Promise.resolve({ exists: false });
      });

      // Mock collection and doc behavior
      jest.spyOn(admin.firestore(), 'collection').mockImplementation((collectionName: string) => ({
        doc: jest.fn((docId: string) => ({ 
          _pathForTest: `${collectionName}/${docId}`,
          get: mockDocGet
        })),
      } as any));

      // Execute
      const result = await getVersionContent(versionId);

      // Verify
      expect(result.metadata).toEqual(metaData);
      expect(result.pages['home']).toEqual(homePageData);
      expect(result.pages['about']).toEqual(aboutPageData);
      expect(admin.firestore().collection).toHaveBeenCalledWith('backup_meta');
      expect(admin.firestore().collection).toHaveBeenCalledWith('backups');
    });

    it('should throw an error if version metadata does not exist', async () => {
      // Setup
      const mockDocGet = jest.fn().mockResolvedValue({ exists: false });
      jest.spyOn(admin.firestore(), 'collection').mockImplementation((name: string) => ({
        doc: jest.fn(() => ({
          get: mockDocGet
        })),
      } as any));

      // Execute & Verify
      await expect(getVersionContent(versionId)).rejects.toThrow(
        `Version ${versionId} not found`
      );
    });

    it('should return empty pages object if metadata.pages is undefined or empty', async () => {
      // Setup for undefined pages
      const metaNoPages = { 
        timestamp: new Date(), 
        createdAt: new Date() 
        // pages field is undefined
      };
      const mockDocGet = jest.fn().mockResolvedValue({ exists: true, data: () => metaNoPages });
      jest.spyOn(admin.firestore(), 'collection').mockImplementation((name: string) => ({
        doc: jest.fn(() => ({
          get: mockDocGet
        })),
      } as any));

      // Execute
      let result = await getVersionContent(versionId);

      // Verify
      expect(result.metadata).toEqual(metaNoPages);
      expect(result.pages).toEqual({});

      // Reset for empty pages array test
      jest.clearAllMocks();

      // Setup for empty pages array
      const metaEmptyPages = { 
        timestamp: new Date(), 
        pages: [], 
        createdAt: new Date() 
      };
      const mockDocGet2 = jest.fn().mockResolvedValue({ exists: true, data: () => metaEmptyPages });
      jest.spyOn(admin.firestore(), 'collection').mockImplementation((name: string) => ({
        doc: jest.fn(() => ({
          get: mockDocGet2
        })),
      } as any));

      // Execute
      result = await getVersionContent(versionId);

      // Verify
      expect(result.metadata).toEqual(metaEmptyPages);
      expect(result.pages).toEqual({});
      expect(admin.firestore().collection).toHaveBeenCalledTimes(1);
      expect(admin.firestore().collection).toHaveBeenCalledWith('backup_meta');
    });

    it('should exclude page data if a page in metadata.pages does not exist in backups', async () => {
      // Setup
      const metaWithMissingPage = { 
        timestamp: new Date(), 
        pages: ['home', 'missing_page'], 
        createdAt: new Date() 
      };
      const homeData = { 
        content: 'Home content', 
        _meta: { page: 'home', versionId, timestamp: new Date() } 
      };

      // Mock document get behavior for specific paths
      const mockDocGet = jest.fn().mockImplementation(function(this: any) {
        const path = this._pathForTest;
        if (path === `backup_meta/${versionId}`) {
          return Promise.resolve({ exists: true, data: () => metaWithMissingPage });
        }
        if (path === `backups/home_${versionId}`) {
          return Promise.resolve({ exists: true, data: () => homeData });
        }
        if (path === `backups/missing_page_${versionId}`) {
          return Promise.resolve({ exists: false });
        }
        return Promise.resolve({ exists: false });
      });

      // Mock collection and doc behavior
      jest.spyOn(admin.firestore(), 'collection').mockImplementation((collectionName: string) => ({
        doc: jest.fn((docId: string) => ({ 
          _pathForTest: `${collectionName}/${docId}`,
          get: mockDocGet
        })),
      } as any));

      // Execute
      const result = await getVersionContent(versionId);

      // Verify
      expect(result.metadata).toEqual(metaWithMissingPage);
      expect(result.pages['home']).toEqual(homeData);
      expect(result.pages['missing_page']).toBeUndefined();
    });
  });

  describe('listVersions', () => {
    it('should return a list of versions ordered by timestamp descending', async () => {
      // Setup
      const versionData = [
        { 
          timestamp: new Date('2023-01-02T00:00:00Z'), 
          pages: ['home'], 
          createdAt: new Date('2023-01-02T00:00:00Z') 
        },
        { 
          timestamp: new Date('2023-01-01T00:00:00Z'), 
          pages: ['home', 'about'], 
          createdAt: new Date('2023-01-01T00:00:00Z') 
        },
      ];
      const firestoreDocs = [
        { id: 'v2', data: () => versionData[0] },
        { id: 'v1', data: () => versionData[1] },
      ];
      
      const mockGet = jest.fn().mockResolvedValue({ empty: false, docs: firestoreDocs });
      const mockOrderBy = jest.fn().mockReturnThis();
      
      jest.spyOn(admin.firestore(), 'collection').mockReturnValue({
        orderBy: mockOrderBy,
        get: mockGet
      } as any);

      // Execute
      const result = await listVersions();

      // Verify
      expect(result).toEqual([
        { id: 'v2', ...versionData[0] },
        { id: 'v1', ...versionData[1] },
      ]);
      expect(admin.firestore().collection).toHaveBeenCalledWith('backup_meta');
      expect(mockOrderBy).toHaveBeenCalledWith('timestamp', 'desc');
    });

    it('should return an empty array if no versions exist', async () => {
      // Setup
      const mockGet = jest.fn().mockResolvedValue({ empty: true, docs: [] });
      const mockOrderBy = jest.fn().mockReturnThis();
      
      jest.spyOn(admin.firestore(), 'collection').mockReturnValue({
        orderBy: mockOrderBy,
        get: mockGet
      } as any);

      // Execute
      const result = await listVersions();

      // Verify
      expect(result).toEqual([]);
      expect(admin.firestore().collection).toHaveBeenCalledWith('backup_meta');
      expect(mockOrderBy).toHaveBeenCalledWith('timestamp', 'desc');
    });
  });

  describe('deleteVersion', () => {
    const versionId = 'test-version-delete-id';

    it('should delete version metadata and all associated page backups successfully', async () => {
      // Setup
      const metaData = { 
        timestamp: new Date(), 
        pages: ['home', 'about'], 
        createdAt: new Date() 
      };

      // Mock document references
      const metaDocRef = { 
        get: jest.fn().mockResolvedValue({ exists: true, data: () => metaData }), 
        _pathForTest: `backup_meta/${versionId}`, 
      };
      const homeBackupRef = { 
        _pathForTest: `backups/home_${versionId}`, 
      };
      const aboutBackupRef = { 
        _pathForTest: `backups/about_${versionId}`, 
      };

      // Mock collection and doc behavior
      jest.spyOn(admin.firestore(), 'collection').mockImplementation((colName: string) => {
        if (colName === 'backup_meta') {
          return { doc: jest.fn(() => metaDocRef) } as any;
        }
        if (colName === 'backups') {
          return { 
            doc: jest.fn((docId: string) => {
              if (docId === `home_${versionId}`) return homeBackupRef;
              if (docId === `about_${versionId}`) return aboutBackupRef;
              return {};
            }) 
          } as any;
        }
        return {} as any;
      });

      // Mock batch operations
      const mockBatchDelete = jest.fn();
      const mockBatchCommit = jest.fn().mockResolvedValue(undefined);
      jest.spyOn(admin.firestore(), 'batch').mockReturnValue({
        delete: mockBatchDelete,
        commit: mockBatchCommit
      } as any);

      // Execute
      const result = await deleteVersion(versionId);

      // Verify
      expect(result).toEqual({ success: true });
      expect(mockBatchDelete).toHaveBeenCalledWith(metaDocRef);
      expect(mockBatchDelete).toHaveBeenCalledWith(homeBackupRef);
      expect(mockBatchDelete).toHaveBeenCalledWith(aboutBackupRef);
      expect(mockBatchCommit).toHaveBeenCalledTimes(1);
    });

    it('should throw an error if version metadata does not exist for deletion', async () => {
      // Setup
      const metaDocRef = { 
        get: jest.fn().mockResolvedValue({ exists: false }) 
      };
      jest.spyOn(admin.firestore(), 'collection').mockImplementation((colName: string) => {
        if (colName === 'backup_meta') {
          return { doc: jest.fn(() => metaDocRef) } as any;
        }
        return {} as any;
      });

      // Mock batch operations
      const mockBatchCommit = jest.fn();
      jest.spyOn(admin.firestore(), 'batch').mockReturnValue({
        delete: jest.fn(),
        commit: mockBatchCommit
      } as any);

      // Execute & Verify
      await expect(deleteVersion(versionId)).rejects.toThrow(
        `Version ${versionId} not found`
      );
      expect(mockBatchCommit).not.toHaveBeenCalled();
    });

    it('should only delete metadata if metadata.pages is undefined or empty', async () => {
      // Setup for undefined pages
      const metaNoPages = { 
        timestamp: new Date(), 
        createdAt: new Date() 
        // pages field is undefined
      };
      const metaDocRef = { 
        get: jest.fn().mockResolvedValue({ exists: true, data: () => metaNoPages }), 
        _pathForTest: `backup_meta/${versionId}`, 
      };
      jest.spyOn(admin.firestore(), 'collection').mockImplementation((colName: string) => {
        if (colName === 'backup_meta') {
          return { doc: jest.fn(() => metaDocRef) } as any;
        }
        return {} as any;
      });

      // Mock batch operations
      const mockBatchDelete = jest.fn();
      const mockBatchCommit = jest.fn().mockResolvedValue(undefined);
      jest.spyOn(admin.firestore(), 'batch').mockReturnValue({
        delete: mockBatchDelete,
        commit: mockBatchCommit
      } as any);

      // Execute
      await deleteVersion(versionId);

      // Verify
      expect(mockBatchDelete).toHaveBeenCalledWith(metaDocRef);
      expect(mockBatchDelete).toHaveBeenCalledTimes(1);
      expect(mockBatchCommit).toHaveBeenCalledTimes(1);

      // Reset for empty pages array test
      jest.clearAllMocks();

      // Setup for empty pages array
      const metaEmptyPages = { 
        timestamp: new Date(), 
        pages: [], 
        createdAt: new Date() 
      };
      const metaDocRefEmpty = { 
        get: jest.fn().mockResolvedValue({ exists: true, data: () => metaEmptyPages }), 
        _pathForTest: `backup_meta/${versionId}`, 
      };
      jest.spyOn(admin.firestore(), 'collection').mockImplementation((colName: string) => {
        if (colName === 'backup_meta') {
          return { doc: jest.fn(() => metaDocRefEmpty) } as any;
        }
        return {} as any;
      });

      // Mock batch operations again
      const mockBatchDelete2 = jest.fn();
      const mockBatchCommit2 = jest.fn().mockResolvedValue(undefined);
      jest.spyOn(admin.firestore(), 'batch').mockReturnValue({
        delete: mockBatchDelete2,
        commit: mockBatchCommit2
      } as any);

      // Execute
      await deleteVersion(versionId);

      // Verify
      expect(mockBatchDelete2).toHaveBeenCalledWith(metaDocRefEmpty);
      expect(mockBatchDelete2).toHaveBeenCalledTimes(1);
      expect(mockBatchCommit2).toHaveBeenCalledTimes(1);
    });

    it('should re-throw error if batch commit fails during deletion', async () => {
      // Setup
      const metaData = { 
        timestamp: new Date(), 
        pages: ['home'], 
        createdAt: new Date() 
      };
      const metaDocRef = { 
        get: jest.fn().mockResolvedValue({ exists: true, data: () => metaData }), 
        _pathForTest: `backup_meta/${versionId}`, 
      };
      const homeBackupRef = { 
        _pathForTest: `backups/home_${versionId}`, 
      };

      jest.spyOn(admin.firestore(), 'collection').mockImplementation((colName: string) => {
        if (colName === 'backup_meta') {
          return { doc: jest.fn(() => metaDocRef) } as any;
        }
        if (colName === 'backups') {
          return { doc: jest.fn(() => homeBackupRef) } as any;
        }
        return {} as any;
      });

      // Mock batch operations with error
      const commitError = new Error('Firestore commit operation failed!');
      const mockBatchDelete = jest.fn();
      const mockBatchCommit = jest.fn().mockRejectedValue(commitError);
      jest.spyOn(admin.firestore(), 'batch').mockReturnValue({
        delete: mockBatchDelete,
        commit: mockBatchCommit
      } as any);

      // Execute & Verify
      await expect(deleteVersion(versionId)).rejects.toThrow(commitError);
      expect(mockBatchDelete).toHaveBeenCalledWith(metaDocRef);
      expect(mockBatchDelete).toHaveBeenCalledWith(homeBackupRef);
      expect(mockBatchCommit).toHaveBeenCalledTimes(1);
    });
  });
});