import React from "react";
import { describe, it, expect } from "vitest";
import { render, screen } from "@testing-library/react";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "./card";

describe("Card Components", () => {
  describe("Card", () => {
    it("renders with default props", () => {
      render(<Card data-testid="card">Card Content</Card>);
      const card = screen.getByTestId("card");
      expect(card).toBeInTheDocument();
      expect(card).toHaveClass("rounded-lg border bg-card text-card-foreground shadow-sm");
      expect(card).toHaveTextContent("Card Content");
    });

    it("applies custom className", () => {
      render(<Card data-testid="card" className="custom-class">Card Content</Card>);
      const card = screen.getByTestId("card");
      expect(card).toHaveClass("custom-class");
    });

    it("forwards ref correctly", () => {
      const ref = React.createRef<HTMLDivElement>();
      render(<Card ref={ref} data-testid="card">Card Content</Card>);
      expect(ref.current).not.toBeNull();
      expect(ref.current).toBe(screen.getByTestId("card"));
    });
  });

  describe("CardHeader", () => {
    it("renders with default props", () => {
      render(<CardHeader data-testid="card-header">Header Content</CardHeader>);
      const header = screen.getByTestId("card-header");
      expect(header).toBeInTheDocument();
      expect(header).toHaveClass("flex flex-col space-y-1.5 p-6");
      expect(header).toHaveTextContent("Header Content");
    });

    it("applies custom className", () => {
      render(<CardHeader data-testid="card-header" className="custom-class">Header Content</CardHeader>);
      const header = screen.getByTestId("card-header");
      expect(header).toHaveClass("custom-class");
    });
  });

  describe("CardTitle", () => {
    it("renders with default props", () => {
      render(<CardTitle data-testid="card-title">Title Content</CardTitle>);
      const title = screen.getByTestId("card-title");
      expect(title).toBeInTheDocument();
      expect(title).toHaveClass("text-lg font-semibold leading-none tracking-tight");
      expect(title).toHaveTextContent("Title Content");
    });

    it("applies custom className", () => {
      render(<CardTitle data-testid="card-title" className="custom-class">Title Content</CardTitle>);
      const title = screen.getByTestId("card-title");
      expect(title).toHaveClass("custom-class");
    });
  });

  describe("CardDescription", () => {
    it("renders with default props", () => {
      render(<CardDescription data-testid="card-description">Description Content</CardDescription>);
      const description = screen.getByTestId("card-description");
      expect(description).toBeInTheDocument();
      expect(description).toHaveClass("text-sm text-muted-foreground");
      expect(description).toHaveTextContent("Description Content");
    });

    it("applies custom className", () => {
      render(<CardDescription data-testid="card-description" className="custom-class">Description Content</CardDescription>);
      const description = screen.getByTestId("card-description");
      expect(description).toHaveClass("custom-class");
    });
  });

  describe("CardContent", () => {
    it("renders with default props", () => {
      render(<CardContent data-testid="card-content">Content</CardContent>);
      const content = screen.getByTestId("card-content");
      expect(content).toBeInTheDocument();
      expect(content).toHaveClass("p-6 pt-0");
      expect(content).toHaveTextContent("Content");
    });

    it("applies custom className", () => {
      render(<CardContent data-testid="card-content" className="custom-class">Content</CardContent>);
      const content = screen.getByTestId("card-content");
      expect(content).toHaveClass("custom-class");
    });
  });

  describe("CardFooter", () => {
    it("renders with default props", () => {
      render(<CardFooter data-testid="card-footer">Footer Content</CardFooter>);
      const footer = screen.getByTestId("card-footer");
      expect(footer).toBeInTheDocument();
      expect(footer).toHaveClass("flex items-center p-6 pt-0");
      expect(footer).toHaveTextContent("Footer Content");
    });

    it("applies custom className", () => {
      render(<CardFooter data-testid="card-footer" className="custom-class">Footer Content</CardFooter>);
      const footer = screen.getByTestId("card-footer");
      expect(footer).toHaveClass("custom-class");
    });
  });

  it("renders a complete card with all components", () => {
    render(
      <Card data-testid="card">
        <CardHeader data-testid="card-header">
          <CardTitle data-testid="card-title">Card Title</CardTitle>
          <CardDescription data-testid="card-description">Card Description</CardDescription>
        </CardHeader>
        <CardContent data-testid="card-content">Card Content</CardContent>
        <CardFooter data-testid="card-footer">Card Footer</CardFooter>
      </Card>
    );

    expect(screen.getByTestId("card")).toBeInTheDocument();
    expect(screen.getByTestId("card-header")).toBeInTheDocument();
    expect(screen.getByTestId("card-title")).toBeInTheDocument();
    expect(screen.getByTestId("card-description")).toBeInTheDocument();
    expect(screen.getByTestId("card-content")).toBeInTheDocument();
    expect(screen.getByTestId("card-footer")).toBeInTheDocument();

    expect(screen.getByTestId("card-title")).toHaveTextContent("Card Title");
    expect(screen.getByTestId("card-description")).toHaveTextContent("Card Description");
    expect(screen.getByTestId("card-content")).toHaveTextContent("Card Content");
    expect(screen.getByTestId("card-footer")).toHaveTextContent("Card Footer");
  });
});