"use client";

import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ref, uploadBytes, getDownloadURL } from "firebase/storage";
import { saveArtwork } from "@/lib/artwork-repository";
import { Artwork } from "@/types/artworks";
import { Button } from "@/components/ui/button";
import { FormField } from "@/components/ui/form-field";
import { storage } from "@/lib/firebase";
import { Badge } from "@/components/ui/badge";
import {
  Loader2,
  Upload,
  FolderOpen,
  CheckCircle2,
  X,
  FolderPlus,
  Plus,
} from "lucide-react";
import { FolderBrowserDialog } from "./FolderBrowserDialog";

// Add explicit type for parameter
const isError = (a: unknown): a is Error => a instanceof Error;

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_TYPES = ["image/jpeg", "image/png", "image/gif", "image/webp"];

const BASE_PATH = process.env.NEXT_PUBLIC_STORAGE_BASE_PATH || "images";

interface AddNewArtworkDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onArtworkCreated: (artwork: Artwork) => void;
  defaultFolder?: string;
  artwork?: Artwork;

  // For editing mode
  categories?: string[];

  // Available categories
}

export function AddNewArtworkDialog({
  open,
  onOpenChange,
  onArtworkCreated,
  defaultFolder = "images",
  artwork,
  categories = [],
}: AddNewArtworkDialogProps) {
  const isEditing = !!artwork;

  // Form state
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [year, setYear] = useState("");
  const [size, setSize] = useState("");
  const [medium, setMedium] = useState("");
  const [category, setCategory] = useState("");
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState("");
  const [folder, setFolder] = useState(defaultFolder);

  // Folder management
  const [isFolderDialogOpen, setIsFolderDialogOpen] = useState(false);
  const [showNewFolderInput, setShowNewFolderInput] = useState(false);
  const [newFolderName, setNewFolderName] = useState("");

  // Upload state
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data when in edit mode
  useEffect(() => {
    if (isEditing && artwork) {
      setTitle(artwork.title || "");
      setDescription(artwork.description || "");
      setYear(artwork.year || "");
      setSize(artwork.size || "");
      setMedium(artwork.medium || "");
      setCategory(artwork.category || "");
      setTags(artwork.tags || []);
      // Extract folder from image path
      if (artwork.image) {
        const lastSlashIndex = artwork.image.lastIndexOf("/");

        if (lastSlashIndex > -1)
          setFolder(artwork.image.substring(0, lastSlashIndex));
      }
      // No need to set preview URL as it will be loaded by the image field
    }
  }, [isEditing, artwork]);

  // Handle file selection
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (!file) return;

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      setError(`File size must be less than 5MB`);
      return;
    }

    // Validate file type
    if (!ALLOWED_TYPES.includes(file.type)) {
      setError(`Only JPEG, PNG, GIF, and WebP images are allowed`);
      return;
    }

    setSelectedFile(file);
    setError(null);
    // Create preview URL
    const reader = new FileReader();

    reader.onloadend = () => {
      setPreviewUrl(reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  // Create new folder
  const createNewFolder = async () => {
    if (!newFolderName) return;

    try {
      // Create an empty .keep file in the new folder to make it exist
      const newFolderPath = folder
        ? `${folder}/${newFolderName}`
        : newFolderName;

      const newFolderRef = ref(storage, `${BASE_PATH}/${newFolderPath}/.keep`);

      const emptyBlob = new Blob([""], {
        type: "text/plain",
      });

      await uploadBytes(newFolderRef, emptyBlob);
      // Update current folder to the newly created one
      setFolder(newFolderPath);
      setNewFolderName("");
      setShowNewFolderInput(false);
      // Show success message
      setSuccess(`Folder "${newFolderName}" created successfully`);
      // Auto-clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch {
      setError("Failed to create folder");
    }
  };

  // Handle folder selection
  const handleFolderSelect = (selectedFolder: string) => {
    setFolder(selectedFolder);
    setIsFolderDialogOpen(false);
  };

  // Add tag
  const addTag = () => {
    if (!tagInput.trim()) return;

    if (!tags.includes(tagInput.trim())) setTags([...tags, tagInput.trim()]);

    setTagInput("");
  };

  // Remove tag
  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove));
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!title.trim()) newErrors.title = "Title is required";
    if (!year.trim()) newErrors.year = "Year is required";
    if (!medium.trim()) newErrors.medium = "Medium is required";
    if (!category.trim()) newErrors.category = "Category is required";

    // Only require image for new artworks
    if (
      !isEditing &&
      !selectedFile &&
      !(
        artwork &&
        typeof artwork === "object" &&
        artwork !== null &&
        (artwork as Partial<Artwork>).image &&
        typeof (artwork as Partial<Artwork>).image === "string" &&
        (artwork as Partial<Artwork>).image.trim() !== ""
      )
    ) {
      newErrors.file = "Image file is required";
    }

    setErrors(newErrors);
    return !Object.keys(newErrors).length;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSaving(true);
    setError(null);
    setSuccess(null);

    try {
      let imagePath = artwork?.image || "";

      // 1. Upload the image file if a new one is selected
      if (selectedFile) {
        setIsUploading(true);
        setUploadProgress(0);
        // Create storage path
        const storagePath = `${BASE_PATH}/${folder}/${selectedFile.name}`;
        const storageRef = ref(storage, storagePath);

        // Upload file
        await uploadBytes(storageRef, selectedFile);
        setUploadProgress(100);
        // Get download URL (not needed for our use case, but good to verify upload)
        await getDownloadURL(storageRef);
        // Set the relative path
        imagePath = `${folder}/${selectedFile.name}`;
      }

      // 2. Create or update artwork document
      const artworkData: Artwork = {
        ...(artwork || {}),

        // Keep existing data for editing
        title,
        description,
        year,
        size,
        medium,
        category,
        tags,
        image: imagePath,
        updatedAt: new Date(),
      };

      // For new artworks, set creation date
      if (!isEditing) artworkData.createdAt = new Date();

      // Save to Firestore
      const artworkId = await saveArtwork(artworkData);

      // Update with ID
      const savedArtwork: Artwork = {
        ...artworkData,
        id: artworkId,
      };

      setSuccess(
        isEditing
          ? "Artwork updated successfully"
          : "Artwork created successfully"
      );

      // Notify parent component
      onArtworkCreated(savedArtwork);
      // Close dialog after a short delay
      setTimeout(() => {
        onOpenChange(false);
        resetForm();
      }, 1500);
    } catch (err) {
      setError(isError(err) ? err.message : "Failed to save artwork");
    } finally {
      setIsUploading(false);
      setIsSaving(false);
    }
  };

  // Reset form
  const resetForm = () => {
    setTitle("");
    setDescription("");
    setYear("");
    setSize("");
    setMedium("");
    setCategory("");
    setTags([]);
    setTagInput("");
    setFolder(defaultFolder);
    setSelectedFile(null);
    setPreviewUrl(null);
    setUploadProgress(0);
    setError(null);
    setSuccess(null);
    setErrors({});
  };

  // Common mediums
  const commonMediums = [
    "Oil on Canvas",
    "Acrylic on Canvas",
    "Watercolor on Paper",
    "Charcoal on Paper",
    "Digital Print",
    "Photograph",
    "Mixed Media",
    "Sculpture",
    "Ink on Paper",
    "Pencil on Paper",
  ];

  // Get all available categories
  const availableCategories =
    categories.length > 0
      ? categories
      : [
          "Paintings",
          "Drawings",
          "Prints",
          "Photography",
          "Sculpture",
          "Mixed Media",
          "Digital Art",
          "Watercolor",
          "Oil",
          "Acrylic",
        ];

  return (
    <Dialog
      open={open}
      onOpenChange={(newOpen) => {
        if (!isSaving) {
          onOpenChange(newOpen);

          if (!newOpen) resetForm();
        }
      }}
    >
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl">
            {isEditing ? "Edit Artwork" : "Add New Artwork"}
          </DialogTitle>
        </DialogHeader>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-4">
          {/* Left column - Image upload */}
          <div className="space-y-4">
            <FormField
              label="Artwork Image"
              required={!isEditing}
              error={errors.file}
            >
              <div
                className={`border-2 border-dashed rounded-md overflow-hidden transition-all hover:border-primary ${
                  previewUrl || (isEditing && artwork?.image)
                    ? "border-transparent"
                    : "border-gray-300"
                }`}
              >
                {previewUrl ? (
                  <div className="relative aspect-square w-full">
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img
                      src={previewUrl}
                      alt="Selected artwork"
                      className="object-contain w-full h-full"
                    />
                    <Button
                      variant="destructive"
                      size="icon"
                      className="absolute top-2 right-2"
                      onClick={() => {
                        setSelectedFile(null);
                        setPreviewUrl(null);
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ) : isEditing && artwork?.image ? (
                  <div className="relative aspect-square w-full">
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img
                      src={`/api/image?path=${artwork.image}`}
                      alt="Current artwork"
                      className="object-contain w-full h-full"
                    />
                  </div>
                ) : (
                  <label
                    htmlFor="artwork-upload"
                    className="flex flex-col items-center justify-center aspect-square cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors"
                  >
                    <Upload className="h-8 w-8 text-gray-400 mb-2" />
                    <span className="text-sm text-gray-500">
                      Click to select an image
                    </span>
                    <span className="text-xs text-gray-400 mt-1">
                      JPEG, PNG, GIF, WebP • Max 5MB
                    </span>
                  </label>
                )}
              </div>
              <Input
                id="artwork-upload"
                type="file"
                accept={ALLOWED_TYPES.join(",")}
                onChange={handleFileSelect}
                className="hidden"
              />
            </FormField>
            <FormField
              label="Storage Folder"
              helpText="Choose where to store the image"
            >
              <div className="flex gap-2">
                <Input
                  value={folder}
                  onChange={(e) => setFolder(e.target.value)}
                  placeholder="e.g., artworks/paintings"
                  className="flex-1"
                  readOnly
                />
                <Button
                  variant="outline"
                  onClick={() => setIsFolderDialogOpen(true)}
                  className="flex items-center"
                  title="Browse folders"
                >
                  <FolderOpen className="h-4 w-4 mr-1" />
                  Browse
                </Button>
              </div>
              <div className="mt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowNewFolderInput(true)}
                  className="flex items-center"
                >
                  <FolderPlus className="h-4 w-4 mr-1" />
                  New Folder
                </Button>
              </div>
              {/* New folder input */}
              {showNewFolderInput && (
                <div className="flex gap-2 mt-2 p-3 bg-gray-50 rounded-md">
                  <Input
                    type="text"
                    placeholder="Enter folder name"
                    value={newFolderName}
                    onChange={(e) => setNewFolderName(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") createNewFolder();

                      if (e.key === "Escape") {
                        setNewFolderName("");
                        setShowNewFolderInput(false);
                      }
                    }}
                    autoFocus
                  />
                  <Button onClick={createNewFolder}>Create</Button>
                  <Button
                    variant="ghost"
                    onClick={() => {
                      setNewFolderName("");
                      setShowNewFolderInput(false);
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </FormField>
          </div>
          {/* Right column - Metadata */}
          <div className="space-y-4">
            <FormField label="Title" required error={errors.title}>
              <Input
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Enter artwork title"
              />
            </FormField>
            <div className="grid grid-cols-2 gap-4">
              <FormField label="Year" required error={errors.year}>
                <Input
                  value={year}
                  onChange={(e) => setYear(e.target.value)}
                  placeholder="e.g., 2023"
                />
              </FormField>
              <FormField label="Size" error={errors.size}>
                <Input
                  value={size}
                  onChange={(e) => setSize(e.target.value)}
                  placeholder="e.g., 24 x 36 in"
                />
              </FormField>
            </div>
            <FormField label="Medium" required error={errors.medium}>
              <Select value={medium} onValueChange={setMedium}>
                <SelectTrigger>
                  <SelectValue placeholder="Select medium" />
                </SelectTrigger>
                <SelectContent>
                  {commonMediums.map((m) => (
                    <SelectItem key={m} value={m}>
                      {m}
                    </SelectItem>
                  ))}
                  <SelectItem value="custom">Custom...</SelectItem>
                </SelectContent>
              </Select>
              {medium === "custom" && (
                <Input
                  value={medium === "custom" ? "" : medium}
                  onChange={(e) => setMedium(e.target.value)}
                  placeholder="Enter custom medium"
                  className="mt-2"
                />
              )}
            </FormField>
            <FormField label="Category" required error={errors.category}>
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {availableCategories.map((c) => (
                    <SelectItem key={c} value={c}>
                      {c}
                    </SelectItem>
                  ))}
                  <SelectItem value="custom">Custom...</SelectItem>
                </SelectContent>
              </Select>
              {category === "custom" && (
                <Input
                  value={category === "custom" ? "" : category}
                  onChange={(e) => setCategory(e.target.value)}
                  placeholder="Enter custom category"
                  className="mt-2"
                />
              )}
            </FormField>
            <FormField label="Tags" helpText="Separate tags with commas">
              <div className="flex gap-2">
                <Input
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  placeholder="Add a tag"
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      addTag();
                    }
                  }}
                />
                <Button type="button" onClick={addTag}>
                  <Plus className="h-4 w-4 mr-1" />
                  Add
                </Button>
              </div>
              <div className="flex flex-wrap gap-2 mt-2">
                {tags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {tag}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => removeTag(tag)}
                    />
                  </Badge>
                ))}
              </div>
            </FormField>
            <FormField label="Description">
              <Textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Enter artwork description"
                rows={3}
              />
            </FormField>
          </div>
        </div>
        {/* Error and success messages */}
        {error && (
          <div className="text-red-500 text-sm mb-4 p-3 bg-red-50 rounded-md flex items-center">
            <X className="h-4 w-4 mr-2 text-red-500" />
            {error}
          </div>
        )}
        {success && (
          <div className="text-green-600 text-sm mb-4 p-3 bg-green-50 rounded-md flex items-center">
            <CheckCircle2 className="h-4 w-4 mr-2 text-green-600" />
            {success}
          </div>
        )}
        {/* Upload progress */}
        {isUploading && (
          <div className="mb-4">
            <div className="flex justify-between text-sm mb-1">
              <span>Uploading...</span>
              <span>{uploadProgress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-300 ease-out"
                style={{
                  width: `${uploadProgress}%`,
                }}
              ></div>
            </div>
          </div>
        )}
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSaving}
          >
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isSaving || isUploading}>
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                {isEditing ? "Updating..." : "Creating..."}
              </>
            ) : isEditing ? (
              "Update Artwork"
            ) : (
              "Create Artwork"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
      {/* Folder browser dialog */}
      <FolderBrowserDialog
        open={isFolderDialogOpen}
        onOpenChange={setIsFolderDialogOpen}
        onSelect={handleFolderSelect}
        currentFolder={folder}
      />
    </Dialog>
  );
}
