import React from "react";
import { describe, it, expect } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { Input } from "./input";

describe("Input", () => {
  it("renders with default props", () => {
    render(<Input data-testid="input" />);
    const input = screen.getByTestId("input");
    expect(input).toBeInTheDocument();
    expect(input.tagName).toBe("INPUT");
    expect(input).toHaveClass("flex h-10 w-full rounded-md border");
  });

  it("renders with type attribute", () => {
    render(<Input data-testid="input" type="password" />);
    const input = screen.getByTestId("input");
    expect(input).toHaveAttribute("type", "password");
  });

  it("applies custom className", () => {
    render(<Input data-testid="input" className="custom-class" />);
    const input = screen.getByTestId("input");
    expect(input).toHaveClass("custom-class");
  });

  it("forwards ref correctly", () => {
    const ref = React.createRef<HTMLInputElement>();
    render(<Input ref={ref} data-testid="input" />);
    expect(ref.current).not.toBeNull();
    expect(ref.current).toBe(screen.getByTestId("input"));
  });

  it("passes additional props to the input element", () => {
    render(
      <Input
        data-testid="input"
        placeholder="Enter text"
        disabled
        required
        aria-label="Input Label"
      />
    );
    const input = screen.getByTestId("input");
    expect(input).toHaveAttribute("placeholder", "Enter text");
    expect(input).toBeDisabled();
    expect(input).toBeRequired();
    expect(input).toHaveAttribute("aria-label", "Input Label");
  });

  it("handles value changes", async () => {
    render(<Input data-testid="input" />);
    const input = screen.getByTestId("input");
    
    await userEvent.type(input, "Hello World");
    expect(input).toHaveValue("Hello World");
  });

  it("handles controlled input", () => {
    const onChange = vi.fn();
    render(<Input data-testid="input" value="Controlled Value" onChange={onChange} />);
    const input = screen.getByTestId("input");
    
    expect(input).toHaveValue("Controlled Value");
    
    fireEvent.change(input, { target: { value: "New Value" } });
    expect(onChange).toHaveBeenCalledTimes(1);
  });

  it("renders with file input type", () => {
    render(<Input data-testid="input" type="file" />);
    const input = screen.getByTestId("input");
    expect(input).toHaveAttribute("type", "file");
    expect(input).toHaveClass("file:border-0");
    expect(input).toHaveClass("file:bg-transparent");
  });

  it("applies disabled styles when disabled", () => {
    render(<Input data-testid="input" disabled />);
    const input = screen.getByTestId("input");
    expect(input).toHaveClass("disabled:cursor-not-allowed");
    expect(input).toHaveClass("disabled:opacity-50");
    expect(input).toBeDisabled();
  });
});