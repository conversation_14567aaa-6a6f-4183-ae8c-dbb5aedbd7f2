"use client";

import { useState, useEffect } from "react";
import ImageSlideshow from "@/components/ImageSlideshow";
import { But<PERSON> } from "@/components/ui/button";
import usePreview from "@/lib/usePreview";
import { HomeContent } from "@/types/home";
import { ProfileCard } from "@/components/ProfileCard";
import { FeaturedArtworkCard } from "@/components/FeaturedArtworkCard";
import EditButton from "@/components/EditButton";
import PageHeader from "@/components/PageHeader";
import Link from "next/link";
import { useImageResolver } from "@/hooks/use-image-resolver";
import { usePublicContent } from "@/hooks/use-public-content";
import { useAdminAuth } from "@/lib/useAdminAuth";

const DEFAULT_HOME_CONTENT: HomeContent = {
  heroTitle: "",
  heroText: "",
  aboutText: [],
  artworks: [],
  profileImage: "",
  ctaText: "",
  ctaLink: "",
};



export default function Home() {
  const isPreviewMode = usePreview();
  const { user } = useAdminAuth();

  // Use the new hook for content loading
  const { content, loading: contentLoading } = usePublicContent<HomeContent>({
    collection: "home",
    documentId: "home",
    defaultValue: DEFAULT_HOME_CONTENT,
    previewMode: isPreviewMode,
  });

  // Destructure all content fields to use them in the component
  const { heroText, ctaText, ctaLink } = content || {};

  // Image resolver hook
  const { resolvedUrls, resolveImages, isResolving } = useImageResolver();

  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [isSlideShowOpen, setIsSlideShowOpen] = useState(false);

  const handleImageClick = (index: number) => {
    setSelectedIndex(index);
    setIsSlideShowOpen(true);
  };

  // Resolve images when content changes
  useEffect(() => {
    if (content) {
      const pathsToResolve: string[] = [];
      if (content.profileImage && content.profileImage.trim() !== "") {
        pathsToResolve.push(content.profileImage);
      }
      (content.artworks || []).forEach(a => {
        if (a.image) {
          pathsToResolve.push(a.image);
        }
      });
      if (pathsToResolve.length > 0) {
        resolveImages(pathsToResolve);
      }
    }
  }, [content, resolveImages]);

  // Show loading indicator - wait for both content and image resolution
  if (contentLoading || isResolving) {
    return (
      <div className="max-w-4xl mx-auto text-center py-20">Loading...</div>
    );
  }

  // Only show artworks that have been successfully resolved
  const homeArtworks = (content?.artworks || [])
    .filter((artwork) => artwork.image && resolvedUrls[artwork.image]) // Only include resolved images
    .map((artwork) => ({
      ...artwork,
      image: resolvedUrls[artwork.image], // Use resolved URL
    }));

  // Debug: log the artworks being rendered
  return (
    <div className="max-w-5xl mx-auto px-4">
      {/* Preview banner is now handled by PreviewProvider */}
      <PageHeader
        title={content?.heroTitle || "Bela Raval"}
        subtitle="Fine Art"
        description={heroText}
      />
      <div className="flex items-center justify-end mb-2 -mt-12">
        <EditButton path="/admin/home" hidden={isPreviewMode || !user} />
      </div>
      <div className="flex flex-col md:flex-row items-center gap-12 mb-20 py-12 animate-subtle-fade">
        <ProfileCard
          image={content?.profileImage && resolvedUrls[content.profileImage] ? resolvedUrls[content.profileImage] : null}
          name="Bela Raval"
          bio={
            content?.aboutText?.length ? (
              <>
                {content.aboutText.map((paragraph, index) => (
                  <p
                    key={index}
                    className="font-montserrat text-modernArt-mutedForeground"
                  >
                    {paragraph}
                  </p>
                ))}
              </>
            ) : null
          }
        />
      </div>
      <div className="mb-8">
        <h2 className="text-3xl font-cormorant font-light text-modernArt-foreground mb-12 text-center">
          Featured Works
        </h2>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-10 mb-20">
        {homeArtworks.map((artwork, index) => (
          <div key={index} className="rounded-sm overflow-hidden"> {/* Removed elegant-card */}
            <FeaturedArtworkCard
              {...artwork}
              onClick={() => handleImageClick(index)}
            />
          </div>
        ))}
      </div>
      <ImageSlideshow
        artworks={homeArtworks}
        currentIndex={selectedIndex}
        isOpen={isSlideShowOpen}
        onClose={() => setIsSlideShowOpen(false)}
      />
      <div className="text-center mt-16 mb-20">
        <Button
          asChild
          size="lg"
          variant="default" // Changed to default for primary accent background
          className="font-sans text-lg px-10 py-3 hover:opacity-90" // Applied modern styling
        >
          <Link href={ctaLink || "/gallery"}>{ctaText || "View Gallery"}</Link>
        </Button>
      </div>
    </div>
  );
}
