import React from "react";
import { describe, it, expect } from "vitest";
import { render, screen } from "@testing-library/react";
import { Skeleton } from "./skeleton";

describe.skip("Skeleton", () => {
  it("renders with default props", () => {
    render(<Skeleton data-testid="skeleton" />);
    const skeleton = screen.getByTestId("skeleton");
    expect(skeleton).toBeInTheDocument();
    expect(skeleton).toHaveClass("animate-pulse");
    expect(skeleton).toHaveClass("rounded-md");
    expect(skeleton).toHaveClass("bg-muted");
  });

  it("applies custom className", () => {
    render(<Skeleton data-testid="skeleton" className="custom-class" />);
    const skeleton = screen.getByTestId("skeleton");
    expect(skeleton).toHaveClass("custom-class");
  });

  it("passes additional props to the div element", () => {
    render(
      <Skeleton
        data-testid="skeleton"
        aria-label="Loading"
        role="status"
        style={{ width: "100px", height: "20px" }}
      />
    );
    const skeleton = screen.getByTestId("skeleton");
    expect(skeleton).toHaveAttribute("aria-label", "Loading");
    expect(skeleton).toHaveAttribute("role", "status");
    expect(skeleton).toHaveStyle({ width: "100px", height: "20px" });
  });

  it("renders with children", () => {
    render(
      <Skeleton data-testid="skeleton">
        <div data-testid="child">Child content</div>
      </Skeleton>
    );
    const skeleton = screen.getByTestId("skeleton");
    const child = screen.getByTestId("child");
    expect(skeleton).toContainElement(child);
    expect(child).toHaveTextContent("Child content");
  });
});