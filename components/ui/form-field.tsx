'use client';

import {ReactNode} from 'react';

interface FormFieldProps {
    label: string;
    error?: string;
    children: ReactNode;
    required?: boolean;
    className?: string;
    helpText?: string;
}

/**
 * A reusable form field component that provides consistent styling and error display
 */
export function FormField({label, error, children, required = false, className = '', helpText}: FormFieldProps) {
    return (
        <div className={`space-y-1 ${className}`}>
            <label className="block text-sm font-medium text-gray-700">
                {label}
                {required && <span className="text-red-500 ml-1">*</span>}
            </label>
            {children}
            {helpText && !error && (
<p className="text-sm text-gray-500 mt-1">{helpText}</p>
        )}
            {error && (
<p className="text-sm text-red-500 mt-1">{error}</p>
        )}
        </div>
    );
}
