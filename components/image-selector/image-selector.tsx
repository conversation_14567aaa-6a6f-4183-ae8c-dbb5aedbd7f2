"use client";

import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import {
  ref,
  uploadBytes,
  listAll,
  getDownloadURL,
  StorageReference,
} from "firebase/storage";
import { ChevronRight, FolderPlus, Folder } from "lucide-react";
import Image from "next/image";
import { storage } from "@/lib/firebase";
import { Button } from "@/components/ui/button";

const MAX_FILE_SIZE = 5 * 1024 * 1024;

// 5MB
const ALLOWED_TYPES = ["image/jpeg", "image/png", "image/gif", "image/webp"];

const BASE_PATH = process.env.NEXT_PUBLIC_STORAGE_BASE_PATH || "images";

export interface ImageSelectorProps {
  /** Called when an image is selected. The path will be relative to BASE_PATH
   * e.g. if BASE_PATH is "bela-gallery" and image is at "bela-gallery/sub1/image.jpg",
   * the path will be "sub1/image.jpg" */
  onSelect: (relativePath: string) => void;
  className?: string;
  startPath?: string;
}

interface StorageItem {
  ref: StorageReference;
  url?: string;
  type: "folder" | "file";
  parentPath: string;
}

export function ImageSelector({
  onSelect,
  className,
  startPath,
}: ImageSelectorProps) {
  const [allItems, setAllItems] = useState<StorageItem[]>([]);
  const [currentPath, setCurrentPath] = useState(startPath || BASE_PATH);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [newFolderName, setNewFolderName] = useState("");
  const [showNewFolderInput, setShowNewFolderInput] = useState(false);
  const [, setIsSearching] = useState(false);

  const loadAllContent = useCallback(
    async (path: string = BASE_PATH): Promise<StorageItem[]> => {
      try {
        const storageRef = ref(storage, path);
        const result = await listAll(storageRef);

        // Get all folders
        const folderItems: StorageItem[] = result.prefixes.map((prefixRef) => ({
          ref: prefixRef,
          type: "folder",
          parentPath: path,
        }));

        // Get all files with URLs
        const filePromises = result.items.map(async (itemRef) => {
          const url = await getDownloadURL(itemRef);

          return {
            ref: itemRef,
            url,
            type: "file" as const,
            parentPath: path,
          };
        });

        const fileItems = await Promise.all(filePromises);

        // Recursively get items from all subfolders
        const subFolderPromises = result.prefixes.map((prefix) =>
          loadAllContent(prefix.fullPath)
        );

        const subFolderResults = await Promise.all(subFolderPromises);
        const allSubItems = subFolderResults.flat();

        return [...folderItems, ...fileItems, ...allSubItems];
      } catch {
        return [];
      }
    },
    []
  );

  // Remove currentPath from dependency array (not needed)
  const loadContent = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      // Always load all items on initial load
      if (!allItems.length) {
        const items = await loadAllContent();
        setAllItems(items);
      }
    } catch {
      setError("Failed to load content");
    } finally {
      setLoading(false);
    }
  }, [loadAllContent, allItems.length]);

  // Load content on mount and when current path changes
  useEffect(() => {
    loadContent();
  }, [loadContent]);

  // Clear search when changing folders
  useEffect(() => {
    setSearchTerm("");
    setIsSearching(false);
  }, [currentPath]);

  const handleFolderClick = (folderRef: StorageReference) => {
    try {
      setLoading(true);
      setError(null);
      setCurrentPath(folderRef.fullPath);
    } catch {
      setError("Failed to navigate to folder");
    } finally {
      setLoading(false);
    }
  };

  const navigateToParent = () => {
    const parentPath = currentPath.split("/").slice(0, -1).join("/");

    if (parentPath.length >= BASE_PATH.length)
      try {
        setLoading(true);
        setError(null);
        setCurrentPath(parentPath);
      } catch {
        setError("Failed to navigate to parent folder");
      } finally {
        setLoading(false);
      }
  };

  const createNewFolder = async () => {
    if (!newFolderName) return;

    try {
      // Create an empty .keep file in the new folder to make it exist
      const newFolderRef = ref(
        storage,
        `${currentPath}/${newFolderName}/.keep`
      );

      const emptyBlob = new Blob([""], {
        type: "text/plain",
      });

      await uploadBytes(newFolderRef, emptyBlob);

      setNewFolderName("");
      setShowNewFolderInput(false);
      await loadContent();
    } catch {
      setError("Failed to create folder");
    }
  };

  const pathSegments = currentPath.split("/").filter(Boolean);

  const baseFolderName = BASE_PATH.split("/").pop() || "root";

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];

    if (!file) return;

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      setError("File size must be less than 5MB");
      return;
    }

    // Validate file type
    if (!ALLOWED_TYPES.includes(file.type)) {
      setError("Only JPEG, PNG, GIF, and WebP images are allowed");
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const storageRef = ref(storage, `${currentPath}/${file.name}`);
      await uploadBytes(storageRef, file);
      await loadContent();
    } catch {
      setError("Failed to upload image");
    } finally {
      setLoading(false);
    }
  };

  // Get current folder's items or search results
  const currentItems = useMemo(() => {
    if (!allItems) return [];

    const searchLower = searchTerm.toLowerCase();

    // If searching, show all matching items regardless of current path
    if (searchTerm) {
      setIsSearching(true);
      return allItems.filter((item) => {
        const nameMatch = item.ref.name.toLowerCase().includes(searchLower);

        const pathMatch = item.parentPath.toLowerCase().includes(searchLower);

        return nameMatch || pathMatch;
      });
    }

    // If not searching, only show items in current path
    setIsSearching(false);
    return allItems.filter((item) => item.parentPath === currentPath);
  }, [allItems, currentPath, searchTerm]);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Path navigation */}
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <Button
          variant="ghost"
          size="sm"
          onClick={navigateToParent}
          disabled={currentPath === BASE_PATH}
        >
          {baseFolderName}
        </Button>
        {pathSegments.map((segment, index) => {
          if (segment === baseFolderName) return null;

          const path = pathSegments.slice(0, index + 1).join("/");

          return (
            <React.Fragment key={path}>
              <ChevronRight className="h-4 w-4" />
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCurrentPath(`${BASE_PATH}/${path}`)}
              >
                {segment}
              </Button>
            </React.Fragment>
          );
        })}
      </div>
      {/* Controls */}
      <div className="flex gap-4">
        <Input
          type="text"
          placeholder="Search images..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="flex-1"
        />
        <Input
          type="file"
          accept={ALLOWED_TYPES.join(",")}
          onChange={handleFileUpload}
          className="hidden"
          id="image-upload"
        />
        <Button variant="secondary" onClick={() => setShowNewFolderInput(true)}>
          <FolderPlus className="h-4 w-4 mr-2" />
          New Folder
        </Button>
        <Button asChild variant="secondary">
          <label htmlFor="image-upload">Upload</label>
        </Button>
      </div>
      {/* New folder input */}
      {showNewFolderInput && (
        <div className="flex gap-2">
          <Input
            type="text"
            placeholder="Folder name"
            value={newFolderName}
            onChange={(e) => setNewFolderName(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") createNewFolder();

              if (e.key === "Escape") {
                setNewFolderName("");
                setShowNewFolderInput(false);
              }
            }}
          />
          <Button onClick={createNewFolder}>Create</Button>
          <Button
            variant="ghost"
            onClick={() => {
              setNewFolderName("");
              setShowNewFolderInput(false);
            }}
          >
            Cancel
          </Button>
        </div>
      )}
      {error && <div className="text-red-500 text-sm">{error}</div>}
      {loading ? (
        <div className="text-center">Loading...</div>
      ) : (
        <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-4">
          {currentItems.map((item: StorageItem) => {
            if (item.type === "folder")
              return (
                <Card
                  key={item.ref.fullPath}
                  className="relative aspect-square cursor-pointer hover:ring-2 hover:ring-primary transition-all flex items-center justify-center"
                  onClick={() => handleFolderClick(item.ref)}
                >
                  <Folder className="h-12 w-12 text-muted-foreground" />
                  <div className="absolute bottom-2 text-xs text-center px-2 truncate w-full">
                    {item.ref.name}
                  </div>
                  {searchTerm && (
                    <div className="absolute top-2 text-xs text-muted-foreground truncate w-full px-2">
                      {item.parentPath.replace(BASE_PATH, "")}
                    </div>
                  )}
                </Card>
              );

            return (
              <Card
                key={item.ref.fullPath}
                className="relative aspect-square cursor-pointer hover:ring-2 hover:ring-primary transition-all"
                onClick={() => {
                  // Convert full path to relative path by removing BASE_PATH prefix
                  const relativePath = item.ref.fullPath
                    .replace(`${BASE_PATH}/`, "")
                    .replace(BASE_PATH, "");

                  onSelect(relativePath);
                }}
              >
                <Image
                  src={item.url || "/placeholder.svg"}
                  alt={item.ref.name}
                  fill
                  className="absolute inset-0 w-full h-full object-cover rounded-lg"
                />
                {searchTerm && (
                  <div className="absolute top-2 text-xs text-white bg-black/50 truncate w-full px-2 py-1">
                    {item.parentPath.replace(BASE_PATH, "")}
                  </div>
                )}
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
}
