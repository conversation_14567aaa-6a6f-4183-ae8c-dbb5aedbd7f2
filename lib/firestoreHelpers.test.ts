import { describe, it, expect, vi, beforeEach } from "vitest";
import { getPublished } from "./firestoreHelpers";
import { doc, getDoc } from "firebase/firestore";

// Mock Firebase Firestore
vi.mock("firebase/firestore", () => ({
  doc: vi.fn(),
  getDoc: vi.fn(),
}));

// Mock Firebase initialization
vi.mock("./firebase", () => ({
  db: {},
}));

describe("firestoreHelpers", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset window.location.search
    Object.defineProperty(window, "location", {
      value: { search: "" },
      writable: true,
    });
  });

  describe("getPublished", () => {
    it("returns null when no document exists", async () => {
      // Mock getDoc to return snapshots that don't exist
      (getDoc as any).mockResolvedValueOnce({
        exists: () => true,
        data: () => ({ current_version: "v1" }),
      });
      
      (getDoc as any).mockResolvedValueOnce({
        exists: () => false,
        data: () => ({}),
      });
      
      (getDoc as any).mockResolvedValueOnce({
        exists: () => false,
        data: () => ({}),
      });

      const result = await getPublished("live", "home");
      
      expect(result).toBeNull();
      expect(doc).toHaveBeenCalledTimes(3);
      expect(doc).toHaveBeenNthCalledWith(1, {}, "meta", "version");
      expect(doc).toHaveBeenNthCalledWith(2, {}, "versions", "v1", "live", "home");
      expect(doc).toHaveBeenNthCalledWith(3, {}, "live", "home");
    });

    it("returns data from versioned document when available", async () => {
      const versionedData = { title: "Versioned Home" };
      
      // Mock getDoc to return a meta snapshot with version and a versioned document
      (getDoc as any).mockResolvedValueOnce({
        exists: () => true,
        data: () => ({ current_version: "v1" }),
      });
      
      (getDoc as any).mockResolvedValueOnce({
        exists: () => true,
        data: () => versionedData,
      });

      const result = await getPublished("live", "home");
      
      expect(result).toEqual(versionedData);
      expect(doc).toHaveBeenCalledTimes(2);
      expect(doc).toHaveBeenNthCalledWith(1, {}, "meta", "version");
      expect(doc).toHaveBeenNthCalledWith(2, {}, "versions", "v1", "live", "home");
    });

    it("falls back to live document when versioned document doesn't exist", async () => {
      const liveData = { title: "Live Home" };
      
      // Mock getDoc to return a meta snapshot with version, no versioned document, and a live document
      (getDoc as any).mockResolvedValueOnce({
        exists: () => true,
        data: () => ({ current_version: "v1" }),
      });
      
      (getDoc as any).mockResolvedValueOnce({
        exists: () => false,
        data: () => ({}),
      });
      
      (getDoc as any).mockResolvedValueOnce({
        exists: () => true,
        data: () => liveData,
      });

      const result = await getPublished("live", "home");
      
      expect(result).toEqual(liveData);
      expect(doc).toHaveBeenCalledTimes(3);
      expect(doc).toHaveBeenNthCalledWith(1, {}, "meta", "version");
      expect(doc).toHaveBeenNthCalledWith(2, {}, "versions", "v1", "live", "home");
      expect(doc).toHaveBeenNthCalledWith(3, {}, "live", "home");
    });

    it("falls back to live document when meta doesn't exist", async () => {
      const liveData = { title: "Live Home" };
      
      // Mock getDoc to return no meta snapshot and a live document
      (getDoc as any).mockResolvedValueOnce({
        exists: () => false,
        data: () => ({}),
      });
      
      (getDoc as any).mockResolvedValueOnce({
        exists: () => true,
        data: () => liveData,
      });

      const result = await getPublished("live", "home");
      
      expect(result).toEqual(liveData);
      expect(doc).toHaveBeenCalledTimes(2);
      expect(doc).toHaveBeenNthCalledWith(1, {}, "meta", "version");
      expect(doc).toHaveBeenNthCalledWith(2, {}, "live", "home");
    });

    it("returns preview document when preview=true in URL", async () => {
      const previewData = { title: "Preview Home" };
      
      // Set window.location.search to include preview=true
      Object.defineProperty(window, "location", {
        value: { search: "?preview=true" },
        writable: true,
      });
      
      // Mock getDoc to return a preview document
      (getDoc as any).mockResolvedValueOnce({
        exists: () => true,
        data: () => previewData,
      });

      const result = await getPublished("live", "home");
      
      expect(result).toEqual(previewData);
      expect(doc).toHaveBeenCalledTimes(1);
      expect(doc).toHaveBeenCalledWith({}, "preview", "home");
    });

    it("falls back to normal flow when preview document doesn't exist", async () => {
      const liveData = { title: "Live Home" };
      
      // Set window.location.search to include preview=true
      Object.defineProperty(window, "location", {
        value: { search: "?preview=true" },
        writable: true,
      });
      
      // Mock getDoc to return no preview document, no meta, and a live document
      (getDoc as any).mockResolvedValueOnce({
        exists: () => false,
        data: () => ({}),
      });
      
      (getDoc as any).mockResolvedValueOnce({
        exists: () => false,
        data: () => ({}),
      });
      
      (getDoc as any).mockResolvedValueOnce({
        exists: () => true,
        data: () => liveData,
      });

      const result = await getPublished("live", "home");
      
      expect(result).toEqual(liveData);
      expect(doc).toHaveBeenCalledTimes(3);
      expect(doc).toHaveBeenNthCalledWith(1, {}, "preview", "home");
      expect(doc).toHaveBeenNthCalledWith(2, {}, "meta", "version");
      expect(doc).toHaveBeenNthCalledWith(3, {}, "live", "home");
    });
  });
});