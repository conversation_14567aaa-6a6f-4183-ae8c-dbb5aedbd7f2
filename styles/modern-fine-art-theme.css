/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&family=Inter:wght@300;400;500;600;700&family=Montserrat:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500&display=swap');

/* Base HTML Element Styling */
body {
    @apply font-sans text-modernArt-foreground bg-modernArt-background;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
    @apply font-serif text-modernArt-foreground;
    letter-spacing: -0.02em; /* Slightly tighter spacing for serif headings */
}

h1 {
    @apply text-4xl font-light mb-6; /* Example: Light weight for main titles */
}

h2 {
    @apply text-3xl font-normal mb-5;
}

h3 {
    @apply text-2xl font-normal mb-4;
}

h4 {
    @apply text-xl font-medium mb-3;
}

p {
    @apply font-sans text-base text-modernArt-mutedForeground leading-relaxed mb-4;
}

a {
    @apply text-modernArt-primaryAccent transition-colors duration-200 ease-in-out;
}

a:hover {
    @apply opacity-80; /* General opacity, works on any text color */
}

/* Adapted Helper Classes */

/* Image hover effects - kept from elegant-theme as it's a good generic effect */
.image-zoom-container {
    overflow: hidden;
}

.image-zoom {
    transition: transform 0.5s ease-in-out; /* Adjusted timing */
}

.image-zoom:hover {
    transform: scale(1.07); /* Slightly more pronounced zoom */
}

/* Modern Link Underline - subtle and on hover */
.modern-link-underline {
     @apply relative;
}

.modern-link-underline::after {
    content: '';
    @apply absolute bottom-0 left-0 w-0 h-px bg-modernArt-primaryAccent transition-all duration-300 ease-out;
}

.modern-link-underline:hover::after {
    @apply w-full;
}

/* Custom animation - kept from elegant-theme as it's a good generic effect */
 @keyframes subtle-fade {
   from {
     opacity: 0;
     transform: translateY(10px); /* Slightly less movement */
   }
   to {
     opacity: 1;
     transform: translateY(0);
   }
 }

 .animate-subtle-fade {
   animation: subtle-fade 0.7s ease-out forwards; /* Adjusted timing */
 }

/* Enhance shadcn/ui Card default interactivity */
.bg-card.border.shadow-sm { /* Targeting key structural classes of the card */
    @apply transition-all duration-200 ease-in-out; /* Faster transition */
}

.bg-card.border.shadow-sm:hover {
    @apply shadow-xl -translate-y-px; /* More prominent shadow, very slight lift */
}
/* Add more specific component styling or overrides below as needed during later steps */
