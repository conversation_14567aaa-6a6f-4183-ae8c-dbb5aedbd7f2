"use client";

import { useEffect, useState } from "react";
import { useAdminContent } from "@/hooks/use-admin-content";
import { useImageResolver } from "@/hooks/use-image-resolver";
import { useToast } from "@/hooks/use-toast";
import Link from "next/link";
import AdminFormLayout from "@/components/admin/AdminFormLayout";
import { FormField } from "@/components/ui/form-field";
import { EnhancedImageField } from "@/components/admin";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Trash2, Plus, Edit } from "lucide-react";
import { GalleryContent, Category } from "@/types/gallery";
import tryToCatch from "try-to-catch";

const defaultGalleryContent: GalleryContent = {
  categories: [],
};

export default function AdminGalleryPage() {
  const {
    content,
    setContent,
    loading,
    saving,
    isDirty,
    saveContent,
    resetContent,
  } = useAdminContent<GalleryContent>({
    previewCollection: "preview",
    previewDocId: "gallery",
    liveCollection: "live",
    liveDocId: "gallery",
    defaultValue: defaultGalleryContent,
  });

  const { toast, showToast } = useToast();
  const { resolveImages } = useImageResolver();

  // Track validation errors for each category
  const [categoryErrors, setCategoryErrors] = useState<
    Record<number, Record<string, string>>
  >({});

  // Resolve all image paths when content changes
  useEffect(() => {
    if (content?.categories) {
      const imagePaths = content.categories
        .map((category) => category.image)
        .filter(Boolean);

      resolveImages(imagePaths);
    }
  }, [content, resolveImages]);

  // Add a new category
  const addCategory = () => {
    setContent({
      ...content,
      categories: [
        ...content.categories,
        {
          title: "",
          slug: "",
          image: "",
        },
      ],
    });
  };

  // Update a category field
  const updateCategoryField = (
    index: number,
    field: keyof Category,
    value: string
  ) => {
    const updatedCategories = content.categories.slice();

    updatedCategories[index] = {
      ...updatedCategories[index],
      [field]: value,
    };

    // Auto-generate slug from title if slug field is being edited
    if (field === "title") {
      const slug = value
        .toLowerCase()
        .replace(/[^\da-z]+/g, "-")
        .replace(/^-|-$/g, "");

      updatedCategories[index].slug = slug;
      // Validate slug as well
      validateCategoryField(index, "slug", slug);
    }

    setContent({
      ...content,
      categories: updatedCategories,
    });

    // Validate the field
    validateCategoryField(index, field, value);
  };

  // Validate a category field
  const validateCategoryField = (
    index: number,
    field: keyof Category,
    value: string
  ) => {
    let isValid = true;
    let errorMessage = "";

    if (!value.trim().length) {
      isValid = false;
      errorMessage = `${
        field.charAt(0).toUpperCase() + field.slice(1)
      } is required`;
    }

    // Additional validation for slug
    if (field === "slug" && isValid) {
      // Check if slug is unique
      const slugExists = content.categories.some(
        (cat, i) => i !== index && cat.slug === value
      );

      if (slugExists) {
        isValid = false;
        errorMessage = "Slug must be unique";
      }
    }

    setCategoryErrors((prev) => ({
      ...prev,
      [index]: {
        ...prev[index],
        [field]: isValid ? "" : errorMessage,
      },
    }));

    return isValid;
  };

  // Remove a category
  const removeCategory = (index: number) => {
    const updatedCategories = content.categories.filter((_, i) => i !== index);

    setContent({
      ...content,
      categories: updatedCategories,
    });

    // Remove validation errors for this category
    setCategoryErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[index];
      return newErrors;
    });
  };

  const handleSave = async () => {
    // Validate all categories before saving
    let isValid = true;
    const newCategoryErrors: Record<number, Record<string, string>> = {};

    // Check for duplicate slugs
    const slugs = new Set<string>();
    const duplicateSlugs = new Set<string>();

    for (const cat of content.categories) {
      if (cat.slug && slugs.has(cat.slug)) duplicateSlugs.add(cat.slug);

      slugs.add(cat.slug);
    }

    for (const [index, category] of content.categories.entries()) {
      const categoryFieldErrors: Record<string, string> = {};

      // Validate required fields
      if (!category.title || !category.title.trim().length) {
        categoryFieldErrors.title = "Title is required";
        isValid = false;
      }

      if (!category.slug || !category.slug.trim().length) {
        categoryFieldErrors.slug = "Slug is required";
        isValid = false;
      } else if (duplicateSlugs.has(category.slug)) {
        categoryFieldErrors.slug = "Slug must be unique";
        isValid = false;
      }

      if (!category.image || !category.image.trim().length) {
        categoryFieldErrors.image = "Image is required";
        isValid = false;
      }

      if (Object.keys(categoryFieldErrors).length > 0)
        newCategoryErrors[index] = categoryFieldErrors;
    }

    setCategoryErrors(newCategoryErrors);

    if (!isValid) {
      showToast("Please fix the validation errors before saving", {
        variant: "error",
      });
      return;
    }

    await tryToCatch(saveContent);
  };

  return (
    <AdminFormLayout
      title="Gallery Categories"
      description="Manage gallery categories. Each category will have its own page with artworks."
      isLoading={loading}
      isSaving={saving}
      isDirty={isDirty}
      onSave={handleSave}
      onReset={resetContent}
      previewUrl="/gallery"
      previewDocId="gallery"
    >
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <p className="text-gray-600">
            Add, edit, or remove gallery categories. All fields are required.
          </p>
          <Button type="button" onClick={addCategory}>
            <Plus className="h-4 w-4 mr-1" /> Add Category
          </Button>
        </div>
        {Array.isArray(content.categories) &&
        content.categories.length === 0 ? (
          <div className="text-center py-12 border rounded-md bg-gray-50">
            <p className="text-gray-500 mb-4">No categories added yet</p>
            <Button type="button" onClick={addCategory}>
              <Plus className="h-4 w-4 mr-1" /> Add Your First Category
            </Button>
          </div>
        ) : Array.isArray(content.categories) &&
          content.categories.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {content.categories.map((category, index) => {
                return (
                  <div key={index} className="border rounded-md p-4 space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="font-medium">
                        {category.title || `Category ${index + 1}`}
                      </h3>
                      <div className="flex space-x-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          asChild
                          disabled={!category.slug}
                        >
                          <Link href={`/admin/gallery/${category.slug}`}>
                            <Edit className="h-4 w-4 mr-1" /> Edit Artworks
                          </Link>
                        </Button>
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() => removeCategory(index)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <FormField
                        label="Title"
                        required
                        error={categoryErrors[index]?.title}
                      >
                        <Input
                          value={category.title}
                          onChange={(e) =>
                            updateCategoryField(index, "title", e.target.value)
                          }
                          placeholder="Category title"
                          className={
                            categoryErrors[index]?.title ? "border-red-500" : ""
                          }
                        />
                      </FormField>
                      <FormField
                        label="Slug"
                        required
                        error={categoryErrors[index]?.slug}
                        helpText={`Used in URL: /gallery/${
                          category.slug || "category-slug"
                        }`}
                      >
                        <Input
                          value={category.slug}
                          onChange={(e) =>
                            updateCategoryField(index, "slug", e.target.value)
                          }
                          placeholder="category-slug"
                          className={
                            categoryErrors[index]?.slug ? "border-red-500" : ""
                          }
                        />
                      </FormField>
                      <FormField
                        label="Category Image"
                        required
                        error={categoryErrors[index]?.image}
                      >
                        <EnhancedImageField
                          label=""
                          imagePath={category.image}
                          onChange={(path) =>
                            updateCategoryField(index, "image", path)
                          }
                          aspectRatio="wide"
                          className={
                            categoryErrors[index]?.image
                              ? "border border-red-500 rounded-md"
                              : ""
                          }
                          required={true}
                        />
                      </FormField>
                    </div>
                  </div>
                );
              })}
            </div>
          </>
        ) : null}
      </div>
      {toast}
    </AdminFormLayout>
  );
}
