import { describe, it, expect, vi, beforeEach } from "vitest";
import { getStoragePath, getStorageRef, getPublicImageUrl } from "./image-paths";
import { getStorage, ref, getDownloadURL } from "firebase/storage";

// Mock Firebase storage functions
vi.mock("firebase/storage", () => ({
  getStorage: vi.fn(() => ({ app: {} })),
  ref: vi.fn((storage, path) => ({ storage, path })),
  getDownloadURL: vi.fn((ref) => Promise.resolve(`https://example.com/${ref.path}`)),
}));

describe("image-paths", () => {
  const originalEnv = process.env;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();
    
    // Mock environment variables
    vi.stubEnv("NEXT_PUBLIC_STORAGE_BASE_PATH", "bela-gallery");
  });

  afterEach(() => {
    // Restore environment variables
    process.env = originalEnv;
  });

  describe("getStoragePath", () => {
    it("removes leading slash from path", () => {
      const result = getStoragePath("/images/test.jpg");
      expect(result).toBe("bela-gallery/images/test.jpg");
    });

    it("works with paths without leading slash", () => {
      const result = getStoragePath("images/test.jpg");
      expect(result).toBe("bela-gallery/images/test.jpg");
    });

    it("uses custom base path from environment variable", () => {
      vi.stubEnv("NEXT_PUBLIC_STORAGE_BASE_PATH", "custom-path");
      const result = getStoragePath("/images/test.jpg");
      expect(result).toBe("custom-path/images/test.jpg");
    });

    it("falls back to default base path if env variable is not set", () => {
      vi.stubEnv("NEXT_PUBLIC_STORAGE_BASE_PATH", "");
      const result = getStoragePath("/images/test.jpg");
      expect(result).toBe("bela-gallery/images/test.jpg");
    });
  });

  describe("getStorageRef", () => {
    it("calls ref with correct path", () => {
      const result = getStorageRef("/images/test.jpg");
      
      expect(getStorage).toHaveBeenCalled();
      expect(ref).toHaveBeenCalledWith(
        expect.anything(),
        "bela-gallery/images/test.jpg"
      );
      expect(result).toEqual({
        storage: { app: {} },
        path: "bela-gallery/images/test.jpg",
      });
    });
  });

  describe("getPublicImageUrl", () => {
    it("returns download URL for the image", async () => {
      const url = await getPublicImageUrl("/images/test.jpg");
      
      expect(getStorage).toHaveBeenCalled();
      expect(ref).toHaveBeenCalledWith(
        expect.anything(),
        "bela-gallery/images/test.jpg"
      );
      expect(getDownloadURL).toHaveBeenCalled();
      expect(url).toBe("https://example.com/bela-gallery/images/test.jpg");
    });

    it("adds cache buster parameter when requested", async () => {
      // Mock Date.now to return a fixed value
      const originalDateNow = Date.now;
      Date.now = vi.fn(() => 1234567890);
      
      const url = await getPublicImageUrl("/images/test.jpg", true);
      
      expect(url).toBe("https://example.com/bela-gallery/images/test.jpg?t=1234567890");
      
      // Restore Date.now
      Date.now = originalDateNow;
    });

    it("normalizes paths with leading slash", async () => {
      await getPublicImageUrl("/images/test.jpg");
      
      expect(ref).toHaveBeenCalledWith(
        expect.anything(),
        "bela-gallery/images/test.jpg"
      );
    });

    it("works with paths without leading slash", async () => {
      await getPublicImageUrl("images/test.jpg");
      
      expect(ref).toHaveBeenCalledWith(
        expect.anything(),
        "bela-gallery/images/test.jpg"
      );
    });
  });
});