# Bela Gallery Firebase Functions

This directory contains the Firebase Cloud Functions for the Bela Gallery application.

## Testing with Firebase Emulator

The project is set up to use the Firebase Emulator Suite for local development and testing.

### Running Tests

To run tests with the Firebase Emulator:

```bash
# Run tests once
npm run test:emulator

# Run tests in watch mode
npm run test:emulator:watch

# Run tests with coverage report
npm run test:coverage
```

### Running the Emulator

To start the Firebase Emulator with data:

```bash
npm run serve
```

To start the Firebase Emulator with a clean state:

```bash
npm run serve:clean
```

### Test Structure

Tests are organized in the `__tests__` directory:

- `index.test.ts` - Tests for the main HTTP functions
- `publish.test.ts` - Tests for publish and rollback functions
- `backup.test.ts` - Tests for backup and version management functions

### Testing Approach

The tests use a combination of:

1. **Unit tests** - Testing individual functions in isolation
2. **Integration tests** - Testing functions with the Firebase emulator
3. **Mocking** - Using Jest mocks to isolate components

For the backup module, we use complete mocking to ensure tests are reliable and fast.

### Environment Setup

The tests use the following environment configuration:

- Firestore Emulator: `localhost:8080`
- Storage Emulator: `localhost:9199`
- Functions Emulator: `localhost:5001`

These are configured in the `__tests__/setup.js` file.