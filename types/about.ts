export interface Exhibition {
    year: string;
    details: string;
}

export interface ProfessionalExperience {
    years: string;
    role?: string;
    details?: string;
    organization?: string;
}

export interface AboutContent {
    statement: string[];
    education: {
        heading: string;
        details: string;
    }[];
    exhibitions: {
        solo: Exhibition[];
        group: Exhibition[];
        juried: Exhibition[];
        duo: Exhibition[];
    };
    awards: Exhibition[];
    collections: string[];
    profileImage: string;
    professionalExperience?: ProfessionalExperience[];
    research?: ProfessionalExperience[];
    publications?: Exhibition[];
    participation?: Exhibition[];
    otherDetails?: ProfessionalExperience[];
}
