import AdminArtworksEditor from "@/components/admin/AdminArtworksEditor";
import slugs from "@/app/gallery/[slug]/slugs.json";

export function generateStaticParams() {
  return slugs.map((slug: string) => ({
    slug,
  }));
}

// This is a server component that needs to handle params
export default async function AdminArtworksPage({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;
  return <AdminArtworksEditor slug={slug} />;
}
