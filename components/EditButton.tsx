'use client';

import React from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Pencil } from 'lucide-react';

interface EditButtonProps {
    path: string;
    label?: string;
    hidden?: boolean;
}

export default function EditButton({
    path, 
    label = 'Edit',
    hidden = false
}: EditButtonProps) {
    // Don't render the button if hidden is true
    if (hidden) {
        return null;
    }
    
    return (
        <Button
            variant="outline"
            size="sm"
            asChild
            className="flex items-center"
        >
            <Link href={path}>
                <Pencil className="mr-2 h-4 w-4"/>
                {label}
            </Link>
        </Button>
    );
}