"use client";

import { useEffect, useState } from "react";
import { useAdminContent } from "@/hooks/use-admin-content";
import { useImageResolver } from "@/hooks/use-image-resolver";
import { useToast } from "@/hooks/use-toast";
import { useFormValidation } from "@/hooks/use-form-validation";
import { HomeContent } from "@/types/home";
import AdminFormLayout from "@/components/admin/AdminFormLayout";
import { FormField } from "@/components/ui/form-field";
import { EnhancedImageField } from "@/components/admin";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Trash2, Plus, Eye } from "lucide-react";
import PreviewPanel from "@/components/admin/PreviewPanel";
import PreviewToggle from "@/components/admin/PreviewToggle";
import tryToCatch from "try-to-catch";

const defaultHomeContent: HomeContent = {
  heroTitle: "",
  heroText: "",
  ctaText: "",
  ctaLink: "",
  profileImage: "",
  aboutText: [""],
  artworks: [],
};

export default function AdminHomePage() {
  const {
    content,
    setContent,
    loading,
    saving,
    isDirty,
    saveContent,
    resetContent,
  } = useAdminContent<HomeContent>({
    previewCollection: "preview",
    previewDocId: "home",
    liveCollection: "live",
    liveDocId: "home",
    defaultValue: defaultHomeContent,
  });

  const { toast, showToast } = useToast();
  const { resolveImages } = useImageResolver();

  // Preview panel visibility state
  const [showPreview, setShowPreview] = useState(false);

  // Form validation
  const { errors, validateField, validateForm } =
    useFormValidation<HomeContent>({
      heroTitle: [
        {
          validate: (value) => Boolean(value && value.trim().length > 0),
          message: "Hero title is required",
        },
      ],
      ctaText: [
        {
          validate: (value) => Boolean(value && value.trim().length > 0),
          message: "CTA text is required",
        },
      ],
      ctaLink: [
        {
          validate: (value) => Boolean(value && value.trim().length > 0),
          message: "CTA link is required",
        },
        {
          validate: (value) => value.startsWith("/"),
          message: "CTA link must start with /",
        },
      ],
      profileImage: [
        {
          validate: (value) => Boolean(value && value.trim().length > 0),
          message: "Profile image is required",
        },
      ],
    });

  // Track validation errors for artworks
  const [artworkErrors, setArtworkErrors] = useState<
    Record<number, Record<string, string>>
  >({});

  // Resolve profile image path when content changes
  useEffect(() => {
    if (content?.profileImage) resolveImages([content.profileImage]);
  }, [content?.profileImage, resolveImages]);

  // Update a specific field in the content
  const updateField = (
    field: keyof HomeContent,
    value: HomeContent[keyof HomeContent]
  ) => {
    setContent({
      ...content,
      [field]: value,
    });

    // Validate the field if it has validation rules
    if (
      field === "heroTitle" ||
      field === "ctaText" ||
      field === "ctaLink" ||
      field === "profileImage"
    )
      validateField(field, value as string);
  };

  // Update a specific artwork field
  const updateArtworkField = (
    index: number,
    field: keyof NonNullable<HomeContent["artworks"]>[number],
    value: string
  ) => {
    const updatedArtworks = [...(content.artworks || [])];
    updatedArtworks[index] = {
      ...updatedArtworks[index],
      [field]: value,
    };
    updateField("artworks", updatedArtworks);

    // Validate artwork fields
    if (field === "title" || field === "image") {
      const isValid = value.trim().length > 0;
      setArtworkErrors((prev) => ({
        ...prev,
        [index]: {
          ...prev[index],
          [field]: isValid
            ? ""
            : `${field.charAt(0).toUpperCase() + field.slice(1)} is required`,
        },
      }));
    }
  };

  // Add a new artwork
  const addArtwork = () => {
    updateField("artworks", [
      ...(content.artworks || []),
      {
        title: "",
        image: "",
        description: "",
        year: "",
        size: "",
        galleryPath: "",
      },
    ]);
  };

  // Remove an artwork
  const removeArtwork = (index: number) => {
    const updatedArtworks = (content.artworks || []).filter(
      (_, i) => i !== index
    );
    updateField("artworks", updatedArtworks);
    // Remove validation errors for this artwork
    setArtworkErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[index];
      return newErrors;
    });
  };

  // Add a new about text paragraph
  const addAboutParagraph = () => {
    updateField("aboutText", [...content.aboutText, ""]);
  };

  // Update about text paragraph
  const updateAboutText = (index: number, value: string) => {
    const updatedAboutText = content.aboutText.slice();

    updatedAboutText[index] = value;
    updateField("aboutText", updatedAboutText);
  };

  // Remove about text paragraph
  const removeAboutParagraph = (index: number) => {
    const updatedAboutText = content.aboutText.filter((_, i) => i !== index);
    updateField("aboutText", updatedAboutText);
  };

  const handleSave = async () => {
    // Validate main form fields
    const isFormValid = validateForm(content);

    // Validate artworks
    let areArtworksValid = true;
    const newArtworkErrors: Record<number, Record<string, string>> = {};

    for (const [index, artwork] of (content.artworks || []).entries()) {
      const artworkFieldErrors: Record<string, string> = {};

      if (!artwork.title || !artwork.title.trim().length) {
        artworkFieldErrors.title = "Title is required";
        areArtworksValid = false;
      }

      if (!artwork.image || !artwork.image.trim().length) {
        artworkFieldErrors.image = "Image is required";
        areArtworksValid = false;
      }

      if (Object.keys(artworkFieldErrors).length > 0)
        newArtworkErrors[index] = artworkFieldErrors;
    }

    setArtworkErrors(newArtworkErrors);

    if (!isFormValid || !areArtworksValid) {
      showToast("Please fix the validation errors before saving", {
        variant: "error",
      });
      return;
    }

    await tryToCatch(saveContent);
  };

  return (
    <AdminFormLayout
      title="Edit Home Page"
      description="Manage the content displayed on the home page."
      isLoading={loading}
      isSaving={saving}
      isDirty={isDirty}
      onSave={handleSave}
      onReset={resetContent}
      previewUrl="/home"
      previewDocId="home"
    >
      <div className="flex justify-end mb-4">
        <PreviewToggle
          isVisible={showPreview}
          onToggle={() => setShowPreview(!showPreview)}
        />
      </div>

      <div
        className={`grid grid-cols-1 ${
          showPreview ? "md:grid-cols-3" : "md:grid-cols-1"
        } gap-6`}
      >
        <div className={showPreview ? "md:col-span-2" : "md:col-span-1"}>
          <div className="space-y-6">
            {/* Hero Section */}
            <div className="space-y-4 border rounded-md p-6">
              <h3 className="text-lg font-medium">Hero Section</h3>
              <FormField label="Hero Title" required error={errors.heroTitle}>
                <Input
                  value={content.heroTitle}
                  onChange={(e) => updateField("heroTitle", e.target.value)}
                  placeholder="Enter hero title"
                  className={errors.heroTitle ? "border-red-500" : ""}
                />
              </FormField>
              <FormField
                label="Hero Text"
                helpText="Brief description that appears in the hero section"
              >
                <Textarea
                  value={content.heroText}
                  onChange={(e) => updateField("heroText", e.target.value)}
                  placeholder="Enter hero text"
                  rows={3}
                />
              </FormField>
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  label="CTA Text"
                  required
                  error={errors.ctaText}
                  helpText="Call to action button text"
                >
                  <Input
                    value={content.ctaText}
                    onChange={(e) => updateField("ctaText", e.target.value)}
                    placeholder="Enter call to action text"
                    className={errors.ctaText ? "border-red-500" : ""}
                  />
                </FormField>
                <FormField
                  label="CTA Link"
                  required
                  error={errors.ctaLink}
                  helpText="Link destination (must start with /)"
                >
                  <Input
                    value={content.ctaLink}
                    onChange={(e) => updateField("ctaLink", e.target.value)}
                    placeholder="Enter call to action link"
                    className={errors.ctaLink ? "border-red-500" : ""}
                  />
                </FormField>
              </div>
            </div>

            {/* Profile Section */}
            <div className="space-y-4 border rounded-md p-6">
              <h3 className="text-lg font-medium">Profile</h3>
              <FormField
                label="Profile Image"
                required
                error={errors.profileImage}
              >
                <EnhancedImageField
                  label=""
                  imagePath={content.profileImage}
                  onChange={(path) => updateField("profileImage", path)}
                  aspectRatio="square"
                  height={200}
                  className={
                    errors.profileImage
                      ? "border border-red-500 rounded-md"
                      : ""
                  }
                  required={true}
                />
              </FormField>
            </div>

            {/* About Text Section */}
            <div className="space-y-4 border rounded-md p-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">About Text</h3>
                <Button
                  type="button"
                  size="sm"
                  variant="outline"
                  onClick={addAboutParagraph}
                >
                  <Plus className="h-4 w-4 mr-1" /> Add Paragraph
                </Button>
              </div>
              {content.aboutText.map((paragraph, index) => (
                <div key={index} className="flex gap-2">
                  <FormField
                    label={`Paragraph ${index + 1}`}
                    className="flex-1"
                  >
                    <Textarea
                      value={paragraph}
                      onChange={(e) => updateAboutText(index, e.target.value)}
                      placeholder={`Paragraph ${index + 1}`}
                      rows={3}
                    />
                  </FormField>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => removeAboutParagraph(index)}
                    disabled={content.aboutText.length <= 1}
                    className="mt-7"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>

            {/* Featured Artworks Section */}
            <div className="space-y-4 border rounded-md p-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Featured Artworks</h3>
                <Button
                  type="button"
                  size="sm"
                  variant="outline"
                  onClick={addArtwork}
                >
                  <Plus className="h-4 w-4 mr-1" /> Add Artwork
                </Button>
              </div>
              {(content.artworks || []).map((artwork, index) => (
                <div key={index} className="border rounded-md p-4 space-y-4">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">
                      {artwork.title || `Artwork ${index + 1}`}
                    </h4>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => removeArtwork(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      label="Title"
                      required
                      error={artworkErrors[index]?.title}
                    >
                      <Input
                        value={artwork.title}
                        onChange={(e) =>
                          updateArtworkField(index, "title", e.target.value)
                        }
                        placeholder="Artwork title"
                        className={
                          artworkErrors[index]?.title ? "border-red-500" : ""
                        }
                      />
                    </FormField>
                    <FormField
                      label="Gallery Path"
                      helpText="Optional link to gallery page"
                    >
                      <Input
                        value={artwork.galleryPath || ""}
                        onChange={(e) =>
                          updateArtworkField(
                            index,
                            "galleryPath",
                            e.target.value
                          )
                        }
                        placeholder="/gallery/category"
                      />
                    </FormField>
                    <FormField label="Year">
                      <Input
                        value={artwork.year}
                        onChange={(e) =>
                          updateArtworkField(index, "year", e.target.value)
                        }
                        placeholder="Year"
                      />
                    </FormField>
                    <FormField label="Size">
                      <Input
                        value={artwork.size}
                        onChange={(e) =>
                          updateArtworkField(index, "size", e.target.value)
                        }
                        placeholder="Size"
                      />
                    </FormField>
                    <FormField label="Description" className="md:col-span-2">
                      <Input
                        value={artwork.description}
                        onChange={(e) =>
                          updateArtworkField(
                            index,
                            "description",
                            e.target.value
                          )
                        }
                        placeholder="Description"
                      />
                    </FormField>
                    <FormField
                      label="Artwork Image"
                      required
                      error={artworkErrors[index]?.image}
                      className="md:col-span-2"
                    >
                      <EnhancedImageField
                        label=""
                        imagePath={artwork.image}
                        onChange={(path) =>
                          updateArtworkField(index, "image", path)
                        }
                        aspectRatio="wide"
                        className={
                          artworkErrors[index]?.image
                            ? "border border-red-500 rounded-md"
                            : ""
                        }
                        required={true}
                      />
                    </FormField>
                  </div>
                </div>
              ))}
              {(content.artworks || []).length === 0 && (
                <div className="text-center py-8 border rounded-md bg-gray-50">
                  <p className="text-gray-500">No artworks added yet</p>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={addArtwork}
                  >
                    <Plus className="h-4 w-4 mr-1" /> Add Artwork
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Live Preview Panel */}
        {showPreview && (
          <div className="md:col-span-1">
            <div className="sticky top-24">
              <div className="flex items-center mb-2">
                <Eye className="h-4 w-4 mr-1 text-elegant-accent" />
                <h3 className="text-sm font-medium text-elegant-text">
                  Live Preview
                </h3>
              </div>
              <PreviewPanel content={content} type="home" />
              <p className="text-xs text-elegant-text/60 mt-2 italic">
                This preview shows how your content will appear on the public
                site
              </p>
            </div>
          </div>
        )}
      </div>
      {toast}
    </AdminFormLayout>
  );
}
