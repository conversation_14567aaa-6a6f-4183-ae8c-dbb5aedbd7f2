{"hosting": {"public": "out", "cleanUrls": true, "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "redirects": [{"source": "/admin", "destination": "/admin/home", "type": 302}], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "emulators": {"functions": {"port": 5001}, "firestore": {"port": 8080, "options": {"max_heap_size_mb": 1024}}, "hosting": {"port": 5000}, "storage": {"port": 9199, "options": {"max_heap_size_mb": 1024}}, "ui": {"enabled": true}, "singleProjectMode": true}, "storage": {"rules": "storage.rules"}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}]}