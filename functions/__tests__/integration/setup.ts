// functions/__tests__/integration/setup.ts

import * as admin from 'firebase-admin';
import { readFileSync } from 'fs';
import { resolve } from 'path';

// Load emulator ports from main firebase.test.json (shared emulator instance)
const configPath = resolve(__dirname, '../../../firebase.test.json');
const config = JSON.parse(readFileSync(configPath, 'utf8'));

// Set environment variables for the Firebase emulator (using ports from main firebase.test.json)
process.env.FIRESTORE_EMULATOR_HOST = `localhost:${config.emulators.firestore.port}`;
process.env.FIREBASE_STORAGE_EMULATOR_HOST = `localhost:${config.emulators.storage.port}`;
process.env.FIREBASE_AUTH_EMULATOR_HOST = 'localhost:9192'; // Auth emulator not defined in config, using default
process.env.GCLOUD_PROJECT = 'belagallery-test';
process.env.GOOGLE_CLOUD_PROJECT = 'belagallery-test';

// For integration tests, we want to use the real Firebase Admin SDK
// but connected to the emulator, so we don't mock it like in unit tests

// Only silence the logger to reduce noise during tests
import { logger } from 'firebase-functions';
logger.log = jest.fn();
logger.info = jest.fn();
logger.debug = jest.fn();
logger.warn = jest.fn();
logger.error = jest.fn();
logger.write = jest.fn();

// Initialize Firebase Admin for cleanup operations
// Initialize the app if it hasn't been initialized yet
if (!admin.apps.length) {
  admin.initializeApp({
    projectId: 'belagallery-test'
  });
}

const db = admin.firestore();

// Global cleanup function to clear all collections
declare global {
  // eslint-disable-next-line no-var
  var clearFirestoreData: () => Promise<void>;
}

global.clearFirestoreData = async (): Promise<void> => {
  const collections = ['content', 'preview', 'backup_meta', 'audit_log', 'versions', 'backups', 'live', 'audit'];
  
  for (const collectionName of collections) {
    const snapshot = await db.collection(collectionName).get();
    const batch = db.batch();
    
    snapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });
    
    if (snapshot.docs.length > 0) {
      await batch.commit();
    }
  }
};

// Clear data before each test to ensure isolation
beforeEach(async () => {
  await global.clearFirestoreData();
});

// Clear data after all tests complete
afterAll(async () => {
  await global.clearFirestoreData();
});