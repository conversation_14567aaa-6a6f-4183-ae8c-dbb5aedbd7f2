// functions/__tests__/integration/backup.test.ts
import * as admin from 'firebase-admin';
import {
  createVersionSnapshot,
  getVersionContent,
  listVersions,
  deleteVersion,
} from '../../src/backup';

// Import setup to ensure emulator connection
import './setup';

describe('Backup Functions Integration Tests', () => {
  let db: admin.firestore.Firestore;

  beforeAll(async () => {
    // Initialize Firebase Admin with emulator
    if (!admin.apps.length) {
      admin.initializeApp({
        projectId: 'belagallery-test',
      });
    }
    db = admin.firestore();
  });

  beforeEach(async () => {
    // Clear all collections before each test
    await clearFirestore();
  });

  afterAll(async () => {
    // Clean up
    await clearFirestore();
  });

  async function clearFirestore() {
    const collections = ['live', 'preview', 'backup_meta', 'backups', 'audit'];
    for (const collectionName of collections) {
      const snapshot = await db.collection(collectionName).get();
      const batch = db.batch();
      snapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });
      await batch.commit();
    }
  }

  describe('createVersionSnapshot Integration', () => {
    it('should create a version snapshot with live content', async () => {
      // Setup - Add some live content
      const liveData = {
        home: { title: 'Home Page', content: 'Welcome to our gallery' }
      };

      // Add live documents
      await db.collection('live').doc('home').set(liveData.home);

      // Wait for document to be written
      await new Promise(resolve => setTimeout(resolve, 100));

      const versionId = 'test-version-123';
      const snapshotBatch = db.batch();

      // Execute
      await createVersionSnapshot(snapshotBatch, versionId);
      await snapshotBatch.commit();

      // Wait for batch to complete
      await new Promise(resolve => setTimeout(resolve, 200));

      // Verify version was created
      const versionDoc = await db.collection('backup_meta').doc(versionId).get();
      expect(versionDoc.exists).toBe(true);

      const versionData = versionDoc.data();
      expect(versionData).toBeDefined();
      expect(versionData!.timestamp).toBeDefined();
      expect(versionData!.pages).toBeDefined();

      // Verify the pages array contains the home page
      expect(versionData!.pages).toContain('home');
      
      // Verify backup document was created for home
      const homeBackupDoc = await db.collection('backups').doc(`home_${versionId}`).get();
      expect(homeBackupDoc.exists).toBe(true);
      expect(homeBackupDoc.data()).toMatchObject(expect.objectContaining(liveData.home));
    });

    it('should create version with only existing live pages', async () => {
      // Setup - Add only some live content
      const liveData = {
        home: { title: 'Home Page', content: 'Welcome to our gallery' }
      };

      await db.collection('live').doc('home').set(liveData.home);

      const versionId = 'test-version-456';
      const batch2 = db.batch();

      // Execute
      await createVersionSnapshot(batch2, versionId);
      await batch2.commit();

      // Verify version was created
      const versionDoc = await db.collection('backup_meta').doc(versionId).get();
      expect(versionDoc.exists).toBe(true);

      const versionData = versionDoc.data();
      expect(versionData!.pages).toContain('home');
      expect(versionData!.pages.length).toBeGreaterThan(0);
      
      // Verify backup document was created for home
      const homeBackup = await db.collection('backups').doc(`home_${versionId}`).get();
      expect(homeBackup.exists).toBe(true);
      expect(homeBackup.data()).toMatchObject(expect.objectContaining(liveData.home));
    });

    it('should handle empty live collection', async () => {
      const versionId = 'test-version-empty';
      const batch3 = db.batch();

      // Execute
      await createVersionSnapshot(batch3, versionId);
      await batch3.commit();

      // Verify version was created with empty pages
      const versionDoc = await db.collection('backup_meta').doc(versionId).get();
      expect(versionDoc.exists).toBe(true);

      const versionData = versionDoc.data();
      expect(versionData!.pages).toEqual([]);
    });
  });

  describe('getVersionContent Integration', () => {
    it('should retrieve version content', async () => {
      // Setup - Create a version with simpler structure
      const versionId = 'test-version-get';
      const versionData = {
        timestamp: new Date(),
        createdAt: new Date(),
        pages: ['home']
      };

      // Create version metadata first
      await db.collection('backup_meta').doc(versionId).set(versionData);
      
      // Create backup document
      await db.collection('backups').doc(`home_${versionId}`).set({
        title: 'Version Home',
        content: 'Version content',
        _meta: { page: 'home', versionId, timestamp: new Date() }
      });

      // Wait for documents to be written and ensure they exist
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // Verify the documents exist before calling getVersionContent
      const metaDoc = await db.collection('backup_meta').doc(versionId).get();
      expect(metaDoc.exists).toBe(true);
      
      const backupDoc = await db.collection('backups').doc(`home_${versionId}`).get();
      expect(backupDoc.exists).toBe(true);

      // Execute
      const result = await getVersionContent(versionId);

      // Verify
      expect(result).toBeDefined();
      expect(result.metadata.timestamp).toBeDefined();
      expect(result.pages.home).toBeDefined();
      
      // Use type assertion to avoid eslint errors
      const homeData = result.pages.home as { title: string; content: string };
      expect(homeData.title).toBe('Version Home');
    });

    it('should throw error for non-existent version', async () => {
      // Execute & Verify
      await expect(getVersionContent('non-existent-version')).rejects.toThrow('Version non-existent-version not found');
    });
  });

  describe('listVersions Integration', () => {
    it('should list all versions ordered by timestamp', async () => {
      // Setup - Create multiple versions
      const versions = [
        {
          id: 'version-1',
          data: {
            timestamp: new Date('2023-01-01'),
            createdAt: new Date('2023-01-01'),
            pages: []
          }
        },
        {
          id: 'version-2',
          data: {
            timestamp: new Date('2023-01-02'),
            createdAt: new Date('2023-01-02'),
            pages: []
          }
        },
        {
          id: 'version-3',
          data: {
            timestamp: new Date('2023-01-03'),
            createdAt: new Date('2023-01-03'),
            pages: []
          }
        }
      ];

      // Add versions to Firestore
      const batchList = db.batch();
      versions.forEach(version => {
        const docRef = db.collection('backup_meta').doc(version.id);
        batchList.set(docRef, version.data);
      });
      await batchList.commit();

      // Execute
      const result = await listVersions();

      // Verify
      expect(result).toHaveLength(3);
      
      // Should be ordered by timestamp descending (newest first)
      expect(result[0].id).toBe('version-3');
      expect(result[1].id).toBe('version-2');
      expect(result[2].id).toBe('version-1');

      // Verify structure
      result.forEach((version: { id: string; timestamp?: string; createdAt?: string; pages?: string[] }) => {
        expect(version.id).toBeDefined();
        expect(version.timestamp || version.createdAt).toBeDefined();
        if (version.createdAt) expect(version.createdAt).toBeDefined();
        if (version.pages) expect(version.pages).toBeDefined();
      });
    });

    it('should return empty array when no versions exist', async () => {
      // Execute
      const result = await listVersions();

      // Verify
      expect(result).toEqual([]);
    });
  });

  describe('deleteVersion Integration', () => {
    it('should delete a version', async () => {
      // Setup - Create a version
      const versionId = 'version-to-delete';
      const versionData = {
        timestamp: new Date(),
        createdAt: new Date(),
        pages: ['home']
      };

      await db.collection('backup_meta').doc(versionId).set(versionData);
      
      // Also create a backup document
      await db.collection('backups').doc(`home_${versionId}`).set({
        title: 'Home',
        content: 'Content',
        _meta: {
          page: 'home',
          versionId,
          timestamp: new Date()
        }
      });

      // Verify version exists
      let versionDoc = await db.collection('backup_meta').doc(versionId).get();
      expect(versionDoc.exists).toBe(true);

      // Execute
      await deleteVersion(versionId);

      // Verify version was deleted
      versionDoc = await db.collection('backup_meta').doc(versionId).get();
      expect(versionDoc.exists).toBe(false);
      
      // Verify backup document was also deleted
      const backupDoc = await db.collection('backups').doc(`home_${versionId}`).get();
      expect(backupDoc.exists).toBe(false);
    });

    it('should handle deletion of non-existent version', async () => {
      // Execute - should throw error for non-existent version
      await expect(deleteVersion('non-existent-version')).rejects.toThrow('Version non-existent-version not found');
    });
  });
});