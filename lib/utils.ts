import {ClassValue, clsx} from 'clsx';
import {twMerge} from 'tailwind-merge';

/**
 * Combines multiple class names using clsx and tailwind-merge
 * This allows for conditional classes and proper merging of Tailwind classes
 */
export const cn = (...inputs: ClassValue[]) => twMerge(clsx(inputs));

/**
 * Formats a date string into a more readable format
 */
export function formatDate(dateString: string): string {
    if (!dateString)
        return '';
    
    // Try to parse the date string
    // Ensure we're parsing the date correctly by explicitly handling the timezone
    // Format: YYYY-MM-DD
    const [year, month, day] = dateString.split('-').map(Number);
    const date = new Date(year, month - 1, day);
    
    if (isNaN(date.getTime()))
        // If parsing fails, return the original string
        return dateString;
    
    // Format the date
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
    }).format(date);
}

/**
 * Truncates text to a specified length and adds ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
    if (!text || text.length <= maxLength)
        return text;
    
    return text.slice(0, maxLength) + '...';
}

/**
 * Generates a slug from a string
 */
export function slugify(text: string): string {
    return text
        .toLowerCase()
        .replace(/[^\da-z]+/g, '-')
        .replace(/^-|-$/g, '');
}

/**
 * Debounces a function call
 */
export function debounce<T extends (...args: unknown[]) => unknown>(func: T, wait: number): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout | null = null;
    
    return (...args: Parameters<T>) => {
        if (timeout)
            clearTimeout(timeout);
        
        timeout = setTimeout(() => func(...args), wait);
    };
}
