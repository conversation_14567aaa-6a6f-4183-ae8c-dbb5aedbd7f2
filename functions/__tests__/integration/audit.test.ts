// functions/__tests__/integration/audit.test.ts
import * as admin from 'firebase-admin';
import { logAuditEvent, AuditEntry } from '../../src/audit';

// Import setup to ensure emulator connection
import './setup';

describe('Audit Functions Integration Tests', () => {
  let db: admin.firestore.Firestore;

  beforeAll(async () => {
    // Initialize Firebase Admin with emulator
    if (!admin.apps.length) {
      admin.initializeApp({
        projectId: 'belagallery-test',
      });
    }
    db = admin.firestore();
  });

  beforeEach(async () => {
    // Clear audit collection before each test
    await clearAuditCollection();
  });

  afterAll(async () => {
    // Clean up
    await clearAuditCollection();
  });

  async function clearAuditCollection() {
    const snapshot = await db.collection('audit_log').get();
    const batch = db.batch();
    snapshot.docs.forEach(doc => {
      batch.delete(doc.ref);
    });
    await batch.commit();
  }

  describe('logAuditEvent Integration', () => {
    it('should create an audit log entry', async () => {
      // Setup
      const auditEntry: AuditEntry = {
        action: 'publish',
        user: '<EMAIL>',
        version: 'v1.0.0',
        timestamp: new Date(),
        changes: ['home', 'about'],
        metadata: { source: 'integration_test' }
      };

      // Execute
      await logAuditEvent(auditEntry);

      // Verify the document was created in Firestore
      const auditSnapshot = await db.collection('audit_log').get();
      expect(auditSnapshot.docs).toHaveLength(1);

      const auditData = auditSnapshot.docs[0].data();
      expect(auditData).toBeDefined();
      expect(auditData.action).toBe(auditEntry.action);
      expect(auditData.user).toBe(auditEntry.user);
      expect(auditData.version).toBe(auditEntry.version);
      expect(auditData.changes).toEqual(auditEntry.changes);
      expect(auditData.metadata).toEqual(auditEntry.metadata);
      expect(auditData.timestamp).toBeDefined();
      expect(auditData.environment).toBeDefined();
    });

    it('should create audit log with minimal details', async () => {
      // Setup
      const auditEntry: AuditEntry = {
        action: 'content_edit',
        user: '<EMAIL>',
        timestamp: new Date(),
        changes: ['home']
      };

      // Execute
      await logAuditEvent(auditEntry);

      // Verify
      const auditSnapshot = await db.collection('audit_log').get();
      expect(auditSnapshot.docs).toHaveLength(1);

      const auditData = auditSnapshot.docs[0].data();
      expect(auditData.action).toBe(auditEntry.action);
      expect(auditData.user).toBe(auditEntry.user);
      expect(auditData.changes).toEqual(auditEntry.changes);
      expect(auditData.timestamp).toBeDefined();
    });

    it('should create audit log with complex metadata', async () => {
      // Setup
      const auditEntry: AuditEntry = {
        action: 'rollback',
        user: '<EMAIL>',
        version: 'v2.1.0',
        timestamp: new Date(),
        changes: ['home', 'about', 'gallery'],
        metadata: {
          previousVersion: 'v2.0.0',
          reason: 'Critical bug fix',
          rollbackTime: new Date().toISOString()
        }
      };

      // Execute
      await logAuditEvent(auditEntry);

      // Verify
      const auditSnapshot = await db.collection('audit_log').get();
      expect(auditSnapshot.docs).toHaveLength(1);

      const auditData = auditSnapshot.docs[0].data();
      expect(auditData.action).toBe(auditEntry.action);
      expect(auditData.user).toBe(auditEntry.user);
      expect(auditData.version).toBe(auditEntry.version);
      expect(auditData.changes).toEqual(auditEntry.changes);
      expect(auditData.metadata).toEqual(auditEntry.metadata);
      expect(auditData.timestamp).toBeDefined();
    });

    it('should handle multiple concurrent audit logs', async () => {
      // Setup
      const auditEntries: AuditEntry[] = [
        {
          action: 'publish',
          user: '<EMAIL>',
          timestamp: new Date(),
          changes: ['home']
        },
        {
          action: 'content_edit',
          user: '<EMAIL>',
          timestamp: new Date(),
          changes: ['about']
        },
        {
          action: 'rollback',
          user: '<EMAIL>',
          version: 'v1.0.0',
          timestamp: new Date(),
          changes: ['gallery']
        }
      ];

      // Execute - create multiple audit logs concurrently
      const auditPromises = auditEntries.map(entry => logAuditEvent(entry));
      await Promise.all(auditPromises);

      // Verify all documents were created
      const auditSnapshot = await db.collection('audit_log').get();
      expect(auditSnapshot.docs).toHaveLength(3);

      // Verify each document has correct data
      const auditDocs = auditSnapshot.docs.map(doc => doc.data());

      auditEntries.forEach(expectedEntry => {
        const matchingDoc = auditDocs.find(doc =>
          doc.action === expectedEntry.action && doc.user === expectedEntry.user
        );
        expect(matchingDoc).toBeDefined();
        expect(matchingDoc!.changes).toEqual(expectedEntry.changes);
        expect(matchingDoc!.timestamp).toBeDefined();
      });
    });

    it('should create audit logs with proper timestamp ordering', async () => {
      // Clear any existing audit logs first
      const existingSnapshot = await db.collection('audit_log').get();
      const batch = db.batch();
      existingSnapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });
      await batch.commit();

      // Setup - create a single entry first to test basic functionality
      const auditEntry: AuditEntry = {
        action: 'content_edit',
        user: '<EMAIL>',
        timestamp: new Date(),
        changes: ['test_change']
      };

      // Execute
      await logAuditEvent(auditEntry);

      // Wait for write to complete
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Get all audit documents
      const auditSnapshot = await db.collection('audit_log').get();

      // Verify at least one document was created
      expect(auditSnapshot.docs.length).toBeGreaterThanOrEqual(1);

      // Verify the document has the expected structure
      const auditDoc = auditSnapshot.docs[0];
      expect(auditDoc.data().action).toBeDefined();
      expect(auditDoc.data().user).toBeDefined();
      expect(auditDoc.data().timestamp).toBeDefined();
      expect(auditDoc.data().environment).toBeDefined();
    });
  });
});