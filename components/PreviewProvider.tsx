"use client";

import {EyeOff} from 'lucide-react';
import {Button} from '@/components/ui/button';
import {
    createContext,
    useState,
    useEffect,
    ReactNode,
} from 'react';
import AdminPreviewButton from './AdminPreviewButton';

export const PreviewContext = createContext(false);

export function PreviewProvider({children}: {
    children: ReactNode;
}) {
    const isStaticBuild = process.env.NEXT_PUBLIC_STATIC_BUILD === 'true';
    const [isPreviewMode, setIsPreviewMode] = useState(false);
    const [mounted, setMounted] = useState(false);
    
    // Check if we're in preview mode on component mount
    useEffect(() => {
        if (isStaticBuild) return;
        
        setMounted(true);
        
        if (typeof window !== 'undefined') {
            const isPreview = new URLSearchParams(window.location.search).get('preview') === 'true';
            setIsPreviewMode(isPreview);
        }
    }, [isStaticBuild]);
    
    // In static build mode, return just the children without preview functionality
    if (isStaticBuild) {
        return <>{children}</>;
    }

    // Exit preview mode
    const exitPreviewMode = () => {
        if (typeof window !== 'undefined') {
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.delete('preview');
            window.location.href = currentUrl.toString();
        }
    };
    
    // Don't render preview controls during SSR
    if (!mounted)
        return (
            <>{children}</>
        );
    
    return (
        <PreviewContext.Provider value={isPreviewMode}>
            {children}
            {/* Preview Banner */}
            {isPreviewMode && (
<div className="fixed top-0 left-0 right-0 z-50 bg-amber-500 text-white py-2 px-4 flex items-center justify-between">
                <div>
                    <span className="font-medium">Preview Mode</span>
                    <span className="text-sm ml-2 opacity-80">
              Viewing draft changes
            </span>
                </div>
                <Button
                    variant="outline"
                    size="sm"
                    onClick={exitPreviewMode}
                    className="bg-transparent border-white text-white hover:bg-amber-600 hover:text-white"
                >
                <EyeOff className="h-4 w-4 mr-1"/>
            Exit Preview
          </Button>
            </div>
        )}
            {/* The Preview Toggle Button is now in a separate component that handles its own auth check */}
            <AdminPreviewButton/>
        </PreviewContext.Provider>
    );
}
