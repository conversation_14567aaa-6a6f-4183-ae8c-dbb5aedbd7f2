import {
    initializeApp,
    getApps,
    getApp,
    type FirebaseApp,
} from 'firebase/app';
import {
    getFirestore,
    enableNetwork,
    connectFirestoreEmulator,
    type Firestore,
} from 'firebase/firestore';
import {getAuth, type Auth} from 'firebase/auth';
import {
    getStorage,
    connectStorageEmulator,
    type FirebaseStorage,
} from 'firebase/storage';
import {
    getFunctions,
    connectFunctionsEmulator,
    type Functions,
} from 'firebase/functions';

// Only initialize Firebase if not in static build mode
const isStaticBuild = process.env.NEXT_PUBLIC_STATIC_BUILD === 'true';

let app: FirebaseApp | null = null;
let db: Firestore | null = null;
let auth: Auth | null = null;
let storage: FirebaseStorage | null = null;
let functions: Functions | null = null;

// Track if emulators have been connected to prevent multiple connections
let emulatorsConnected = false;

if (!isStaticBuild) {
    const config = {
        apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY!,
        authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN!,
        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID!,
        storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET!,
        messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID!,
        appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID!,
    };

    app = !getApps().length ? initializeApp(config) : getApp();
    db = getFirestore(app);
    auth = getAuth(app);
    storage = getStorage(app);
    functions = getFunctions(app);
}

export { db, auth, storage, functions };

// Connect to emulators in local development - only once
if (typeof window !== 'undefined' && !isStaticBuild && !emulatorsConnected) {
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        if (db && storage && functions) {
            try {
                connectFirestoreEmulator(db, 'localhost', 8080);
                connectStorageEmulator(storage, 'localhost', 9199);
                connectFunctionsEmulator(functions, 'localhost', 5001);
                enableNetwork(db);
                emulatorsConnected = true;
            } catch (error) {
                // Emulators might already be connected, which is fine
                console.log('Firebase emulators already connected or connection failed:', error);
                emulatorsConnected = true; // Prevent further attempts
            }
        }
    }
}
