'use client';

import {useState, useEffect} from 'react';
import Image from 'next/image';
import {Button} from '@/components/ui/button';
import {useImageResolver} from '@/hooks/use-image-resolver';
import {Artwork} from '@/types/artworks';
import {
    Edit,
    Trash2,
    Upload,
    PlusCircle,
} from 'lucide-react';
import {EnhancedImageSelectorDialog} from './EnhancedImageSelectorDialog';
import {AddNewArtworkDialog} from './AddNewArtworkDialog';

interface EnhancedImageFieldProps {
    label: string;
    imagePath: string;
    onChange: (path: string) => void;
    className?: string;
    aspectRatio?: 'square' | 'video' | 'wide';
    height?: number;
    required?: boolean;
    defaultFolder?: string;
}

/**
 * An enhanced image field component with improved UX for selecting and displaying images
 */
export function EnhancedImageField({label, imagePath, onChange, className = '', aspectRatio = 'square', height = 200, required = false, defaultFolder = 'images'}: EnhancedImageFieldProps) {
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [isAddNewDialogOpen, setIsAddNewDialogOpen] = useState(false);
    const {resolvedUrls, resolveImages} = useImageResolver();
    const [isHovered, setIsHovered] = useState(false);
    
    useEffect(() => {
        if (imagePath)
            resolveImages([imagePath]);
    }, [imagePath, resolveImages]);
    
    const aspectRatioClass = {
        square: 'aspect-square',
        video: 'aspect-video',
        wide: 'aspect-[16/9]',
    }[aspectRatio];
    
    // Handle newly created artwork
    const handleArtworkCreated = (artwork: Artwork) => {
        if (artwork?.image)
            onChange(artwork.image);
    };
    
    return (
        <div className={`space-y-2 ${className}`}>
            {label && (
<label className="block text-sm font-medium text-gray-700">
                {label}
                {required && <span className="text-red-500 ml-1">*</span>}
            </label>
        )}
            <div
                className={`border-2 border-dashed rounded-md overflow-hidden ${imagePath ? 'border-transparent' : 'border-gray-300'} transition-all hover:border-primary`}
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
                onClick={() => !imagePath && setIsDialogOpen(true)}
                style={{
                    cursor: imagePath ? 'default' : 'pointer',
                }}
            >
            {imagePath && resolvedUrls[imagePath] ? (
            <div
                className={`relative ${aspectRatioClass} w-full`}
                style={{
                    height,
                }}
            >
                <Image
                    src={resolvedUrls[imagePath]}
                    alt={label || 'Selected image'}
                    fill
                    className="object-contain"
                />
                {/* Overlay controls on hover */}
                {isHovered && (
<div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    <div className="flex gap-2">
                        <Button
                            size="sm"
                            variant="secondary"
                            onClick={(e) => {
                                e.stopPropagation();
                                setIsDialogOpen(true);
                            }}
                        >
                        <Edit className="h-4 w-4 mr-1"/>
                                        Change
                                    </Button>
                        <Button
                            size="sm"
                            variant="destructive"
                            onClick={(e) => {
                                e.stopPropagation();
                                onChange('');
                            }}
                        >
                        <Trash2 className="h-4 w-4 mr-1"/>
                                        Remove
                                    </Button>
                        <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                                e.stopPropagation();
                                setIsAddNewDialogOpen(true);
                            }}
                        >
                        <PlusCircle className="h-4 w-4 mr-1"/>
                                        Add New
                                    </Button>
                    </div>
                </div>
            )}
            </div>
            ) : (
            <div
                className={`flex flex-col items-center justify-center bg-gray-50 ${aspectRatioClass} hover:bg-gray-100 transition-colors`}
                style={{
                    height,
                }}
            >
                <div className="flex flex-col items-center">
                    <Upload className="h-8 w-8 text-gray-400 mb-2"/>
                    <span className="text-sm text-gray-500 mb-1">
                        {required ? 'Click to select an image' : 'Click to add an image'}
                    </span>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                            e.stopPropagation();
                            setIsAddNewDialogOpen(true);
                        }}
                        className="mt-2"
                    >
                    <PlusCircle className="h-4 w-4 mr-1"/>
                                Add New Artwork
                            </Button>
                </div>
            </div>
            )}
        </div>
            {/* Browse existing images dialog */}
            <EnhancedImageSelectorDialog
                open={isDialogOpen}
                onOpenChange={setIsDialogOpen}
                onSelect={(path) => {
                    onChange(path);
                    setIsDialogOpen(false);
                }}
                currentImage={imagePath}
            />
            {/* Add new artwork dialog */}
            <AddNewArtworkDialog
                open={isAddNewDialogOpen}
                onOpenChange={setIsAddNewDialogOpen}
                onArtworkCreated={handleArtworkCreated}
                defaultFolder={defaultFolder}
            />
        </div>
    );
}
