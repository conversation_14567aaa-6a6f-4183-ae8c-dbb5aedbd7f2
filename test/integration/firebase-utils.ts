import { initializeApp } from 'firebase/app';
import { getFirestore, connectFirestoreEmulator, doc, setDoc, deleteDoc, collection, getDocs } from 'firebase/firestore';
import { getStorage, connectStorageEmulator } from 'firebase/storage';

// Firebase configuration for emulator
const firebaseConfig = {
  projectId: 'belagallery-9e01b',
  apiKey: 'fake-api-key',
  authDomain: 'localhost',
};

// Initialize Firebase with emulator connections
export function initializeFirebaseEmulator() {
  const app = initializeApp(firebaseConfig);
  const db = getFirestore(app);
  const storage = getStorage(app);
  
  // Connect to Firestore emulator
  connectFirestoreEmulator(db, 'localhost', 8080);
  
  // Connect to Storage emulator
  connectStorageEmulator(storage, 'localhost', 9199);
  
  return { app, db, storage };
}

// Seed the Firestore emulator with test data
export async function seedFirestore(db: any, collection: string, docId: string, data: any) {
  try {
    await setDoc(doc(db, collection, docId), data);
    console.log(`Seeded ${collection}/${docId} with test data`);
  } catch (error) {
    console.error(`Error seeding ${collection}/${docId}:`, error);
    throw error;
  }
}

// Clear all data from a collection
export async function clearCollection(db: any, collectionPath: string) {
  const collectionRef = collection(db, collectionPath);
  const snapshot = await getDocs(collectionRef);
  
  const deletePromises = snapshot.docs.map(doc => deleteDoc(doc.ref));
  await Promise.all(deletePromises);
  
  console.log(`Cleared collection: ${collectionPath}`);
}