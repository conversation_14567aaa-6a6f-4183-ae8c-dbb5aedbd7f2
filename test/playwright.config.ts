import { defineConfig, devices } from "@playwright/test";
import path from "path";

export default defineConfig({
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: process.env.CI ? "dot" : [["html", { outputFolder: path.resolve(__dirname, "output/playwright-report") }]],
  outputDir: path.resolve(__dirname, "output/test-results"),
  use: {
    baseURL: "http://localhost:3000",
    trace: "on-first-retry",
    screenshot: "only-on-failure",
    video: "retain-on-failure",
    launchOptions: {
      args: ["--enable-precise-memory-info"],
    },
  },
  projects: [
    // E2E test projects
    {
      name: "e2e",
      testDir: path.resolve(__dirname, "e2e"),
      outputDir: path.resolve(__dirname, "output/test-results"),
      use: {
        ...devices["Desktop Chrome"],
        // Use coverage setup
        testIdAttribute: "data-testid",
      },
      testMatch: ["**/e2e/**/*.spec.ts"],
    },
    // Integration tests with Firebase emulator
    {
      name: "integration",
      testDir: path.resolve(__dirname, "integration"),
      outputDir: path.resolve(__dirname, "output/test-results"),
      use: {
        ...devices["Desktop Chrome"],
        // Increase timeout for all tests
        actionTimeout: 30000,
        navigationTimeout: 30000,
        testIdAttribute: "data-testid",
      },
      timeout: 90000, // 90 seconds timeout for integration tests
      testMatch: ["**/integration/**/*.spec.ts"],
      // Emulator setup/teardown is now handled by firebase emulators:exec
    },
  ],
  webServer: {
    command: "npm run dev",
    url: "http://localhost:3000",
    reuseExistingServer: true, // Always reuse existing server to avoid builds
    timeout: 120000, // 2 minutes timeout for server startup
  },
});
