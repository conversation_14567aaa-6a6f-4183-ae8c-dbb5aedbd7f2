import { NextRequest, NextResponse } from 'next/server';
import { adminDb } from '@/lib/firebase-admin';
import { sendEmail, createWelcomeEmailHtml } from '@/lib/email';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ token: string }> }
) {
  try {
    const { token } = await params;

    if (!token) {
      return NextResponse.json({ error: 'Confirmation token is required' }, { status: 400 });
    }

    const subscribersRef = adminDb.collection('subscribers');
    const snapshot = await subscribersRef.where('confirmationToken', '==', token).limit(1).get();

    if (snapshot.empty) {
      return NextResponse.json({ error: 'Invalid or expired token' }, { status: 404 });
    }

    const subscriberDoc = snapshot.docs[0];
    const subscriberData = subscriberDoc.data();

    if (subscriberData.isConfirmed) {
      // Already confirmed, redirect to a page indicating this or to the main page
      // For now, let's redirect to a generic success page or login if applicable
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
      return NextResponse.redirect(`${baseUrl}/confirm/already-confirmed`); // You'll need to create this page
    }

    await subscriberDoc.ref.update({
      isConfirmed: true,
      confirmedAt: new Date(), // Optional: track confirmation time
    });

    // Send welcome email
    try {
      const emailName = subscriberData.email.split('@')[0]; // Simple name extraction
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
      const unsubscribeUrl = `${baseUrl}/api/unsubscribe/${token}`;
      const welcomeEmailHtml = createWelcomeEmailHtml(emailName, unsubscribeUrl);
      await sendEmail({
        to: subscriberData.email,
        subject: 'Welcome to Bela Gallery Newsletter! 🎨',
        html: welcomeEmailHtml,
      });
      console.log(`Welcome email sent to ${subscriberData.email}`);
    } catch (emailError) {
      console.error(`Failed to send welcome email to ${subscriberData.email}:`, emailError);
      // Don't fail the confirmation if welcome email fails - the subscription is still confirmed
    }

    // Redirect to a success page
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    // The success page will be created in a later step.
    return NextResponse.redirect(`${baseUrl}/confirm/success`);

  } catch (error) {
    console.error('Confirmation error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';
    // Redirect to an error page or show a generic error
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    return NextResponse.redirect(`${baseUrl}/confirm/error?message=${encodeURIComponent(errorMessage)}`); // You'll need to create this page
  }
}
