"use client";

import React from 'react';
import {Button} from '@/components/ui/button';
import {cn} from '@/lib/utils';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import {ImageSelector} from './image-selector';

export interface ImageSelectorDialogProps {
    onSelect: (url: string) => void;
    trigger?: React.ReactNode;
    className?: string;
}

export function ImageSelectorDialog({onSelect, trigger, className}: ImageSelectorDialogProps) {
    const [open, setOpen] = React.useState(false);
    
    const handleSelect = (url: string) => {
        onSelect(url);
        setOpen(false);
    };
    
    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                {trigger || <Button variant="outline">Select Image</Button>}
            </DialogTrigger>
            <DialogContent className={cn('max-w-3xl', className)}>
                <DialogHeader>
                    <DialogTitle>Select Image</DialogTitle>
                </DialogHeader>
                <ImageSelector onSelect={handleSelect}/>
            </DialogContent>
        </Dialog>
    );
}
