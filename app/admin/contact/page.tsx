"use client";

import {useAdminContent} from '@/hooks/use-admin-content';
import {useToast} from '@/hooks/use-toast';
import {useFormValidation} from '@/hooks/use-form-validation';
import {ContactContent} from '@/types/contact';
import AdminFormLayout from '@/components/admin/AdminFormLayout';
import {FormField} from '@/components/ui/form-field';
import {Input} from '@/components/ui/input';
import {Textarea} from '@/components/ui/textarea';
import {Eye} from 'lucide-react';
import PreviewPanel from '@/components/admin/PreviewPanel';
import PreviewToggle from '@/components/admin/PreviewToggle';
import {useState} from 'react';
import tryToCatch from 'try-to-catch';

const defaultContactContent: ContactContent = {
    header: '',
    sectionTitle: '',
    description: '',
};

export default function AdminContactPage() {
    const {
        content,
        setContent,
        loading,
        saving,
        isDirty,
        saveContent,
        resetContent,
    } = useAdminContent<ContactContent>({
        previewCollection: 'preview',
        previewDocId: 'contact',
        liveCollection: 'live',
        liveDocId: 'contact',
        defaultValue: defaultContactContent,
    });
    
    const {toast, showToast} = useToast();
    
    // Preview panel visibility state
    const [showPreview, setShowPreview] = useState(false);
    
    // Form validation
    const {
        errors,
        validateField,
        validateForm,
    } = useFormValidation<ContactContent>({
        header: [{
            validate: (value) => value && value.trim().length > 0,
            message: 'Page title is required',
        }],
        sectionTitle: [{
            validate: (value) => value && value.trim().length > 0,
            message: 'Section title is required',
        }],
    });
    
    // Update a specific field in the content
    const updateField = (field: keyof ContactContent, value: string) => {
        setContent({
            ...content,
            [field]: value,
        });

        // Validate the field if it has validation rules
        if (field === 'header' || field === 'sectionTitle')
            validateField(field, value);
    };
    
    const handleSave = async () => {
        // Validate all fields before saving
        if (!validateForm(content)) {
            showToast('Please fix the validation errors before saving', {
                variant: 'error',
            });
            return;
        }
        
        await tryToCatch(saveContent);
    };
    
    
    return (
      <AdminFormLayout
        title="Edit Contact Page"
        description="Manage the content displayed on the contact page."
        isLoading={loading}
        isSaving={saving}
        isDirty={isDirty}
        onSave={handleSave}
        onReset={resetContent}
        previewUrl="/contact"
        previewDocId="contact"
      >
        <div className="flex justify-end mb-4">
          <PreviewToggle isVisible={showPreview} onToggle={() => setShowPreview(!showPreview)} />
        </div>
        
        <div className={`grid grid-cols-1 ${showPreview ? 'md:grid-cols-3' : 'md:grid-cols-1'} gap-6`}>
          <div className={showPreview ? 'md:col-span-2' : 'md:col-span-1'}>
            <div className="space-y-6 border rounded-md p-6">
              <h3 className="text-lg font-medium">Contact Page Content</h3>
              <FormField label="Page Title" required error={errors.header}>
                <Input
                  value={content.header}
                  onChange={(e) => updateField("header", e.target.value)}
                  placeholder="Enter page title"
                  className={errors.header ? "border-red-500" : ""}
                />
              </FormField>
              <FormField
                label="Section Title"
                required
                error={errors.sectionTitle}
              >
                <Input
                  value={content.sectionTitle}
                  onChange={(e) => updateField("sectionTitle", e.target.value)}
                  placeholder="Enter section title"
                  className={errors.sectionTitle ? "border-red-500" : ""}
                />
              </FormField>
              <FormField
                label="Description"
                helpText="Provide a detailed description for the contact page"
              >
                <Textarea
                  value={content.description}
                  onChange={(e) => updateField("description", e.target.value)}
                  placeholder="Enter description"
                  rows={4}
                />
              </FormField>
            </div>
            
            <div className="mt-6 p-4 bg-elegant-light/30 rounded-md border border-elegant/20">
              <h3 className="text-sm font-medium mb-2">Contact Form</h3>
              <p className="text-sm text-elegant-text/70">
                The contact form is automatically included on the page. Visitors will be able to send messages directly through the form.
              </p>
              <div className="mt-4 p-4 border border-dashed border-elegant/30 rounded-md">
                <div className="grid grid-cols-2 gap-2">
                  <div className="h-8 bg-elegant/10 rounded"></div>
                  <div className="h-8 bg-elegant/10 rounded"></div>
                  <div className="h-8 bg-elegant/10 rounded col-span-2"></div>
                  <div className="h-20 bg-elegant/10 rounded col-span-2"></div>
                  <div className="h-10 bg-elegant/20 rounded col-span-2 w-1/3 ml-auto"></div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Live Preview Panel */}
          {showPreview && (
            <div className="md:col-span-1">
              <div className="sticky top-24">
                <div className="flex items-center mb-2">
                  <Eye className="h-4 w-4 mr-1 text-elegant-accent" />
                  <h3 className="text-sm font-medium text-elegant-text">Live Preview</h3>
                </div>
                <PreviewPanel content={content} type="contact" />
                <p className="text-xs text-elegant-text/60 mt-2 italic">
                  This preview shows how your content will appear on the public site
                </p>
              </div>
            </div>
          )}
        </div>
        {toast}
      </AdminFormLayout>
    );
}