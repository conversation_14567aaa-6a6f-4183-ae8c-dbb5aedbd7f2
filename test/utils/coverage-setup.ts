import { test as base } from "@playwright/test";
import { mkdir, writeFile } from "fs/promises";
import path from "path";
import v8toIstanbul from "v8-to-istanbul";

interface V8CoverageEntry {
  url: string;
  functions: Array<{
    functionName: string;
    ranges: Array<{
      startOffset: number;
      endOffset: number;
      count: number;
    }>;
  }>;
}

declare global {
  interface Window {
    coverage: Set<V8CoverageEntry>;
    collectCoverage: (coverage: V8CoverageEntry) => Promise<void>;
  }
}

// Add coverage collection to the test instance
export const test = base.extend({
  page: async ({ page }, runTest) => {
    console.log("Starting JS coverage collection...");

    // Create coverage directory before starting
    const coverageDir = path.join(process.cwd(), "test", "output", "coverage", "playwright");
    await mkdir(coverageDir, { recursive: true });

    // Start coverage before each test
    await page.coverage.startJSCoverage({
      reportAnonymousScripts: true,
      resetOnNavigation: false,
    });

    try {
      // Run the test
      await runTest(page);
    } finally {
      // Get coverage after test
      const coverage = await page.coverage.stopJSCoverage();
      console.log(`Total coverage entries: ${coverage.length}`);

      // Convert V8 coverage to Istanbul format
      const istanbulCoverage = [];
      for (const entry of coverage) {
        // Skip entries without a URL
        if (!entry.url) {
          continue;
        }

        // Handle webpack URLs from Next.js
        if (entry.url.includes("webpack-internal:///")) {
          const match = entry.url.match(
            /webpack-internal:\/\/\/\(app-pages-browser\)\/\.\/(.+?)(?:\?|$)/
          );
          if (match) {
            entry.url = `file://${process.cwd()}/${match[1]}`;
          }
        }

        // Skip test files and dependencies
        if (
          entry.url.includes("node_modules") ||
          entry.url.includes("test") ||
          entry.url.includes("coverage") ||
          entry.url.includes("/_next/") ||
          (entry.url.includes("webpack-internal") &&
            !entry.url.startsWith("file://")) ||
          entry.url.startsWith("http://") ||
          entry.url.startsWith("https://")
        ) {
          continue;
        }

        const filePath = entry.url.replace("file://", "");

        try {
          const converter = v8toIstanbul(filePath);
          await converter.load();
          converter.applyCoverage(entry.functions);
          const converted = converter.toIstanbul();
          istanbulCoverage.push(converted);
        } catch (error) {
          console.log(`Skipping coverage for ${entry.url}: ${error instanceof Error ? error.message : String(error)}`);
        }
      }

      // Only write coverage if we have data
      if (istanbulCoverage.length > 0) {
        const outputPath = path.join(
          coverageDir,
          `coverage-${Date.now()}.json`
        );
        // Convert array of coverage objects into a single object with file paths as keys
        const coverageMap = istanbulCoverage.reduce((acc, coverage) => {
          // Each coverage object should have a key that is the file path
          Object.keys(coverage).forEach((filePath) => {
            acc[filePath] = coverage[filePath];
          });
          return acc;
        }, {});

        await writeFile(outputPath, JSON.stringify(coverageMap, null, 2));
        console.log(`Coverage data written to: ${outputPath}`);
      } else {
        console.log("No coverage data collected");
      }
    }
  },
});

export { expect } from "@playwright/test";