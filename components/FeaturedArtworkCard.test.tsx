import React from "react";
import { describe, it, expect, vi } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import { FeaturedArtworkCard } from "./FeaturedArtworkCard";

// Mock Next.js Image component
vi.mock("next/image", () => ({
  default: ({ src, alt, className }: any) => {
    return (
      // eslint-disable-next-line @next/next/no-img-element
      <img src={src} alt={alt} className={className} data-testid="next-image" />
    );
  },
}));

describe("FeaturedArtworkCard", () => {
  const mockProps = {
    image: "/test-image.jpg",
    title: "Test Artwork",
    description: "This is a test artwork description",
    year: "2025",
    size: "24 x 36 inches",
  };

  it("renders with the correct image and content", () => {
    render(<FeaturedArtworkCard {...mockProps} />);
    
    // Check image
    const image = screen.getByTestId("next-image");
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute("src", "/test-image.jpg");
    expect(image).toHaveAttribute("alt", "Test Artwork");
    
    // Check content
    expect(screen.getAllByText("Test Artwork")).toHaveLength(2); // Title appears twice
    expect(screen.getByText("This is a test artwork description")).toBeInTheDocument();
    expect(screen.getByText("2025")).toBeInTheDocument();
    expect(screen.getByText("24 x 36 inches")).toBeInTheDocument();
  });

  it("uses placeholder image when image prop is empty", () => {
    render(<FeaturedArtworkCard {...mockProps} image="" />);
    
    const image = screen.getByTestId("next-image");
    expect(image).toHaveAttribute("src", "/placeholder.svg");
  });

  it("calls onClick handler when clicked", () => {
    const handleClick = vi.fn();
    render(<FeaturedArtworkCard {...mockProps} onClick={handleClick} />);
    
    // Get the main container directly
    const card = screen.getByTestId("next-image").closest(".group");
    fireEvent.click(card!);
    
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it("has the correct CSS classes for styling", () => {
    render(<FeaturedArtworkCard {...mockProps} />);
    
    // Check main container - get it directly by class
    const container = screen.getByTestId("next-image").closest(".group");
    expect(container).toHaveClass("group");
    expect(container).toHaveClass("cursor-pointer");
    
    // Check image container
    const imageContainer = screen.getByTestId("next-image").closest("div");
    expect(imageContainer).toHaveClass("image-zoom-container");
    
    // Check image
    const image = screen.getByTestId("next-image");
    expect(image).toHaveClass("image-zoom");
    
    // Check overlay
    const overlay = screen.getByText("This is a test artwork description").closest("div");
    expect(overlay).toHaveClass("absolute");
    expect(overlay).toHaveClass("opacity-0");
    expect(overlay).toHaveClass("group-hover:opacity-100");
  });
});