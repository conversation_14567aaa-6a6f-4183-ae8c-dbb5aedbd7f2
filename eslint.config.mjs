import path from 'node:path'
import { fileURLToPath } from 'node:url'
import { defineConfig } from 'eslint/config'
import unusedImports from 'eslint-plugin-unused-imports'
import js from '@eslint/js'
import { FlatCompat } from '@eslint/eslintrc'
import tseslint from 'typescript-eslint'
import typescriptEslintPlugin from '@typescript-eslint/eslint-plugin'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const compat = new FlatCompat({
    baseDirectory: __dirname,
    recommendedConfig: js.configs.recommended,
    allConfig: js.configs.all,
})

export default defineConfig([
    // Basic config for this config file
    {
        files: ['eslint.config.mjs'],
        extends: [js.configs.recommended],
        rules: {
            'no-console': 'off',
        },
    },
    // Next.js and TypeScript config
    ...compat.extends('next/core-web-vitals', 'next/typescript'),
    // Default config for TypeScript files
    {
        files: ['**/*.ts', '**/*.tsx'],
        languageOptions: {
            parser: tseslint.parser,
            parserOptions: {
                project: './tsconfig.json',
            },
        },
        plugins: {
            'unused-imports': unusedImports,
            '@typescript-eslint': typescriptEslintPlugin,
        },
        rules: {
            '@typescript-eslint/no-explicit-any': 'error',
            'unused-imports/no-unused-imports': 'error',
            'unused-imports/no-unused-vars': ['warn', {
                vars: 'all',
                varsIgnorePattern: '^_',
                args: 'after-used',
                argsIgnorePattern: '^_',
            }],
        },
    },
    // Config for test files - allow any types for mocking
    {
        files: ['**/*.test.ts', '**/*.test.tsx', '**/*.spec.ts', '**/*.spec.tsx'],
        rules: {
            '@typescript-eslint/no-explicit-any': 'off', // Allow any in test files
            '@next/next/no-img-element': 'warn', // Downgrade to warning for test files
        },
    },
    // Config for Node.js scripts in scripts directory
    {
        files: ['scripts/**/*.js'],
        extends: [js.configs.recommended],
        env: {
            node: true,
        },
        rules: {
            'no-console': 'off',
        },
    },
])
