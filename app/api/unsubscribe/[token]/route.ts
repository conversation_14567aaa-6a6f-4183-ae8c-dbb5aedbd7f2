import { NextRequest, NextResponse } from 'next/server';
import { adminDb } from '@/lib/firebase-admin';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ token: string }> }
) {
  try {
    const { token } = await params;

    if (!token) {
      return NextResponse.json({ error: 'Unsubscribe token is required' }, { status: 400 });
    }

    const subscribersRef = adminDb.collection('subscribers');
    // Using the confirmationToken as the unsubscribe token for simplicity
    const snapshot = await subscribersRef.where('confirmationToken', '==', token).limit(1).get();

    if (snapshot.empty) {
      // If no token found, it might be an invalid attempt or already unsubscribed and token invalidated.
      // Redirect to a page indicating the token is invalid or the action cannot be completed.
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
      return NextResponse.redirect(`${baseUrl}/unsubscribe/invalid-token`); // You'll need to create this page
    }

    const subscriberDoc = snapshot.docs[0];
    const subscriberData = subscriberDoc.data();

    // Check if already unsubscribed based on 'unsubscribedAt' field
    if (subscriberData.unsubscribedAt) {
        const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
        return NextResponse.redirect(`${baseUrl}/unsubscribe/already-unsubscribed`); // You'll need to create this page
    }

    await subscriberDoc.ref.update({
      isConfirmed: false, // Mark as not confirmed
      unsubscribedAt: new Date(),
      // Optionally, clear the confirmationToken or rotate it if it's sensitive,
      // but for now, keeping it allows them to resubscribe and reconfirm with the same link if desired,
      // though a new subscription flow would generate a new token.
      // For simplicity, we'll leave the token as is.
    });

    // Redirect to an unsubscription success page
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    // The success page will be created in a later step.
    return NextResponse.redirect(`${baseUrl}/unsubscribe/success`);

  } catch (error) {
    console.error('Unsubscribe error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';
    // Redirect to an error page
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    return NextResponse.redirect(`${baseUrl}/unsubscribe/error?message=${encodeURIComponent(errorMessage)}`); // You'll need to create this page
  }
}
