# Local GitHub Actions Testing

This directory contains tools for running GitHub Actions workflows locally using [act](https://github.com/nektos/act).

## Setup

### Installing `act` Locally

- **macOS:**  
  `brew install act`

- **Linux:**  
  `curl https://raw.githubusercontent.com/nektos/act/master/install.sh | sudo bash`

- **Windows:**  
  - Download from [GitHub Releases](https://github.com/nektos/act/releases)
  - Or use Chocolatey: `choco install act-cli`
  - Or use Scoop: `scoop install act`

## Running Tests

Use the `run-tests.sh` script to run tests:

```bash
cd action
chmod +x run-tests.sh
./run-tests.sh [test_type]
```

Where `[test_type]` can be:

- `all` (default)
- `unit`
- `integration`
- `e2e`

For example:

```bash
./run-tests.sh integration
```

## Files in this directory

- `run-tests.sh`: Script to run tests with the specified test type
- `test-event.json`: Contains the input parameters for the workflow (generated by run-tests.sh)
- `.github/workflows/test.yml`: Copy of the workflow file

## Notes

We're using the standard `nektos/act-environments-ubuntu:18.04` image which includes:

- Node.js
- Java
- Common build tools

The workflow will install additional dependencies as needed:

- lsof
- npm packages
- Playwright

## Optional: use Caching like in github between runs of act
### Clone and run the cache server:


- git clone https://github.com/NoahHsu/github-act-cache-server.git
- cd github-act-cache-server
- export ACT_CACHE_AUTH_KEY=foo
- docker compose up --build

### Create Env File
- ACTIONS_CACHE_URL=http://localhost:8080/
- ACTIONS_RUNTIME_URL=http://localhost:8080/
- ACTIONS_RUNTIME_TOKEN=foo

### modify script to include env file
- act --env-file .env

### Cache location
The cache server stores cache files in its own Docker volume or bind mount, as configured in its docker-compose.yml. By default, this is not in your project directory, but inside the cache server’s storage (look for a data or similar directory in the cache server repo or Docker volumes)