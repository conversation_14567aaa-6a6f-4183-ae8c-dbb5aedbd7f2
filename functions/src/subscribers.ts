import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import { v4 as uuidv4 } from "uuid";
import { sendEmail, createConfirmationEmailHtml, createWelcomeEmailHtml } from "./email";

if (!admin.apps.length) admin.initializeApp();

const db = admin.firestore();

// Subscribe function
export const subscribeFunction = functions.https.onRequest(async (req, res) => {
  // Enable CORS
  res.set("Access-Control-Allow-Origin", "*");
  res.set("Access-Control-Allow-Methods", "POST, OPTIONS");
  res.set("Access-Control-Allow-Headers", "Content-Type");

  if (req.method === "OPTIONS") {
    res.status(204).send("");
    return;
  }

  if (req.method !== "POST") {
    res.status(405).send("Method Not Allowed");
    return;
  }

  try {
    const { name, email } = req.body;

    if (!name || typeof name !== 'string') {
      res.status(400).json({ error: 'Name is required' });
      return;
    }

    if (!email || typeof email !== 'string') {
      res.status(400).json({ error: 'Email is required' });
      return;
    }

    // Validate email format (basic)
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      res.status(400).json({ error: 'Invalid email format' });
      return;
    }

    const subscribersRef = db.collection('subscribers');
    const existingSubscriber = await subscribersRef.where('email', '==', email).limit(1).get();

    if (!existingSubscriber.empty) {
      // Check if already confirmed; if not, maybe resend confirmation?
      const subData = existingSubscriber.docs[0].data();
      if (subData.isConfirmed) {
        res.status(200).json({ message: 'Email already subscribed and confirmed' });
        return;
      } else {
        res.status(200).json({ message: 'Email already on the list, pending confirmation' });
        return;
      }
    }

    const confirmationToken = uuidv4();
    const subscribedAt = new Date();

    // Construct the confirmation URL
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const confirmationUrl = `${baseUrl}/confirm/${confirmationToken}`;

    await subscribersRef.add({
      name: name.trim(),
      email,
      isConfirmed: false,
      confirmationToken,
      subscribedAt,
      confirmationEmailSentAt: new Date(),
    });

    // Send confirmation email
    try {
      const emailHtml = createConfirmationEmailHtml(name.trim(), confirmationUrl);
      await sendEmail({
        to: email,
        subject: 'Confirm Your Subscription - Bela Gallery',
        html: emailHtml,
      });
      functions.logger.info(`Confirmation email sent to ${email}`);
    } catch (emailError) {
      functions.logger.error(`Failed to send confirmation email to ${email}:`, emailError);
    }

    res.status(201).json({ message: 'Subscription successful! Please check your email to confirm.' });

  } catch (error) {
    functions.logger.error('Subscription error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';
    res.status(500).json({ error: errorMessage });
  }
});

// Confirm subscription function
export const confirmSubscriptionFunction = functions.https.onRequest(async (req, res) => {
  // Enable CORS
  res.set("Access-Control-Allow-Origin", "*");
  res.set("Access-Control-Allow-Methods", "GET, OPTIONS");
  res.set("Access-Control-Allow-Headers", "Content-Type");

  if (req.method === "OPTIONS") {
    res.status(204).send("");
    return;
  }

  if (req.method !== "GET") {
    res.status(405).send("Method Not Allowed");
    return;
  }

  try {
    // Extract token from URL path
    const pathParts = req.path.split('/');
    const token = pathParts[pathParts.length - 1];

    if (!token) {
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
      res.redirect(`${baseUrl}/confirm/error?message=${encodeURIComponent('Confirmation token is required')}`);
      return;
    }

    const subscribersRef = db.collection('subscribers');
    const snapshot = await subscribersRef.where('confirmationToken', '==', token).limit(1).get();

    if (snapshot.empty) {
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
      res.redirect(`${baseUrl}/confirm/error?message=${encodeURIComponent('Invalid or expired token')}`);
      return;
    }

    const subscriberDoc = snapshot.docs[0];
    const subscriberData = subscriberDoc.data();

    if (subscriberData.isConfirmed) {
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
      res.redirect(`${baseUrl}/confirm/already-confirmed`);
      return;
    }

    await subscriberDoc.ref.update({
      isConfirmed: true,
      confirmedAt: new Date(),
    });

    // Send welcome email
    try {
      const emailName = subscriberData.name || subscriberData.email.split('@')[0];
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
      const unsubscribeUrl = `${baseUrl}/unsubscribe/${token}`;
      const welcomeEmailHtml = createWelcomeEmailHtml(emailName, unsubscribeUrl);
      await sendEmail({
        to: subscriberData.email,
        subject: 'Welcome to Bela Gallery Newsletter! 🎨',
        html: welcomeEmailHtml,
      });
      functions.logger.info(`Welcome email sent to ${subscriberData.email}`);
    } catch (emailError) {
      functions.logger.error(`Failed to send welcome email to ${subscriberData.email}:`, emailError);
    }

    // Redirect to success page
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    res.redirect(`${baseUrl}/confirm/success`);

  } catch (error) {
    functions.logger.error('Confirmation error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    res.redirect(`${baseUrl}/confirm/error?message=${encodeURIComponent(errorMessage)}`);
  }
});

// Unsubscribe function
export const unsubscribeFunction = functions.https.onRequest(async (req, res) => {
  // Enable CORS
  res.set("Access-Control-Allow-Origin", "*");
  res.set("Access-Control-Allow-Methods", "GET, OPTIONS");
  res.set("Access-Control-Allow-Headers", "Content-Type");

  if (req.method === "OPTIONS") {
    res.status(204).send("");
    return;
  }

  if (req.method !== "GET") {
    res.status(405).send("Method Not Allowed");
    return;
  }

  try {
    // Extract token from URL path
    const pathParts = req.path.split('/');
    const token = pathParts[pathParts.length - 1];

    if (!token) {
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
      res.redirect(`${baseUrl}/unsubscribe/invalid-token`);
      return;
    }

    const subscribersRef = db.collection('subscribers');
    const snapshot = await subscribersRef.where('confirmationToken', '==', token).limit(1).get();

    if (snapshot.empty) {
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
      res.redirect(`${baseUrl}/unsubscribe/invalid-token`);
      return;
    }

    const subscriberDoc = snapshot.docs[0];
    const subscriberData = subscriberDoc.data();

    // Check if already unsubscribed
    if (subscriberData.unsubscribedAt) {
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
      res.redirect(`${baseUrl}/unsubscribe/already-unsubscribed`);
      return;
    }

    await subscriberDoc.ref.update({
      isConfirmed: false,
      unsubscribedAt: new Date(),
    });

    // Redirect to success page
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    res.redirect(`${baseUrl}/unsubscribe/success`);

  } catch (error) {
    functions.logger.error('Unsubscribe error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    res.redirect(`${baseUrl}/unsubscribe/error?message=${encodeURIComponent(errorMessage)}`);
  }
});