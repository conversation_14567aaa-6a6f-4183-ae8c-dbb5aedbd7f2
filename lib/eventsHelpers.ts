import {Event} from '@/lib/types';

// Define event status type locally - not part of the Event type
export type EventStatus = 'ongoing' | 'upcoming' | 'past';

// Define a type for events with calculated status
export type EventWithStatus = Event & {
  _status: EventStatus;
};

/**
 * Categorizes events as ongoing, upcoming, or past based on their dates
 */
export function categorizeEvents(events: Event[]): EventWithStatus[] {
  const now = new Date(); // Use actual current date
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth();
  const currentDay = now.getDate();
  
  return events.map(event => {
    let status: EventStatus = 'past'; // Default status
    
    try {
      const dateStr = event.date;
      
      // Check if it's a date range (contains a hyphen)
      if (dateStr && dateStr.includes('-')) {
        // For date ranges like "May 01-30, 2025"
        const parts = dateStr.split(',');
        if (parts.length === 2) {
          const year = parseInt(parts[1].trim());
          
          // First check the year
          if (year < currentYear) {
            return { ...event, _status: 'past' };
          } else if (year > currentYear) {
            return { ...event, _status: 'upcoming' };
          }
          
          // If we're in the same year, check month and day
          const monthDayParts = parts[0].trim().split(' ');
          
          if (monthDayParts.length === 2) {
            const monthName = monthDayParts[0];
            const dayRange = monthDayParts[1].split('-');
            
            if (dayRange.length === 2) {
              // Convert month name to number (0-11)
              const months: Record<string, number> = {
                'january': 0, 'jan': 0,
                'february': 1, 'feb': 1,
                'march': 2, 'mar': 2,
                'april': 3, 'apr': 3,
                'may': 4,
                'june': 5, 'jun': 5,
                'july': 6, 'jul': 6,
                'august': 7, 'aug': 7,
                'september': 8, 'sep': 8, 'sept': 8,
                'october': 9, 'oct': 9,
                'november': 10, 'nov': 10,
                'december': 11, 'dec': 11
              };
              
              const monthIndex = months[monthName.toLowerCase()];
              
              if (monthIndex !== undefined) {
                // Compare month first
                if (monthIndex < currentMonth) {
                  return { ...event, _status: 'past' };
                } else if (monthIndex > currentMonth) {
                  return { ...event, _status: 'upcoming' };
                }
                
                // Same month, check days
                const startDay = parseInt(dayRange[0]);
                const endDay = parseInt(dayRange[1]);
                
                if (!isNaN(startDay) && !isNaN(endDay)) {
                  if (startDay > endDay) { // Handle invalid range first
                    status = 'past';
                  } else {
                    // Handle case where days might be in wrong order (now only for valid ranges)
                    const actualStartDay = Math.min(startDay, endDay);
                    const actualEndDay = Math.max(startDay, endDay);

                    if (currentDay >= actualStartDay && currentDay <= actualEndDay) {
                      status = 'ongoing';
                    } else if (currentDay < actualStartDay) {
                      status = 'upcoming';
                    } else {
                      status = 'past';
                    }
                  }
                }
              }
            }
          }
        }
      } else {
        // Fallback to simple date comparison for non-range dates
        const eventDate = new Date(dateStr || '');
        if (!isNaN(eventDate.getTime())) {
          if (eventDate > now) {
            status = 'upcoming';
          } else if (
            eventDate.getDate() === now.getDate() &&
            eventDate.getMonth() === now.getMonth() &&
            eventDate.getFullYear() === now.getFullYear()
          ) {
            status = 'ongoing';
          } else {
            status = 'past';
          }
        }
      }
    } catch (error) {
      console.error('Error parsing event date:', error);  
    }
    
    // Return event with _status property
    return { ...event, _status: status };
  });
}

export function mapEventsToSlideshowImages(events: Event[]) {
    return events.flatMap((event) => {
        const yearRegex = /(\d{4})/;
        const match = event.date.match(yearRegex);
        // The first element of the match array (match[0]) is the full matched string.
        // If the regex was /someprefix-(\d{4})/, then match[1] would be the captured group.
        // For just /(\d{4})/, match[0] is the 4-digit year.
        const year = match ? match[0] : ""; // Fallback to empty string if no 4-digit year found

        const images = [{
            title: event.title,
            image: event.images.main,
            description: `${event.date} at ${event.location}`,
            year: year,
            size: '',
        }];
        
        if (event.images.details)
            images.push({
                title: `${event.title} - Details`,
                image: event.images.details,
                description: `${event.date} at ${event.location}`,
                year: year, // Use the same extracted year
                size: '',
            });
        
        return images;
    });
}

/**
 * Get ongoing events from a list of categorized events
 */
export function getOngoingEvents(events: Event[]): EventWithStatus[] {
  const categorizedEvents = categorizeEvents(events);
  return categorizedEvents.filter(event => event._status === 'ongoing');
}

/**
 * Get upcoming events from a list of categorized events
 */
export function getUpcomingEvents(events: Event[]): EventWithStatus[] {
  const categorizedEvents = categorizeEvents(events);
  return categorizedEvents.filter(event => event._status === 'upcoming');
}

/**
 * Get past events from a list of categorized events
 */
export function getPastEvents(events: Event[]): EventWithStatus[] {
  const categorizedEvents = categorizeEvents(events);
  return categorizedEvents.filter(event => event._status === 'past');
}

/**
 * Get ongoing and upcoming events (relevant for newsletters)
 */
export function getRelevantEvents(events: Event[]): EventWithStatus[] {
  const categorizedEvents = categorizeEvents(events);
  return categorizedEvents.filter(event =>
    event._status === 'ongoing' || event._status === 'upcoming'
  );
}