import { NextPage } from 'next';
import Link from 'next/link';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { UserMinus } from 'lucide-react'; // Icon for unsubscription

const UnsubscribeSuccessPage: NextPage = () => {
  return (
    <div className="container mx-auto px-4 py-12 flex justify-center items-center min-h-[calc(100vh-200px)]">
      <Card className="w-full max-w-md text-center">
        <CardHeader className="items-center">
          <UserMinus className="w-12 h-12 text-orange-500 mb-2" />
          <CardTitle>Unsubscribed</CardTitle>
          <CardDescription>You have been successfully unsubscribed.</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-6">
            You will no longer receive email updates from us. We&apos;re sorry to see you go!
          </p>
          <p className="text-sm text-gray-500 mb-4">
            If you wish to resubscribe in the future, you can do so from our website.
          </p>
          <Button asChild>
            <Link href="/">Return to Homepage</Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default UnsubscribeSuccessPage;
