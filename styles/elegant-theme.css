/* Elegant Typography */
@import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@300;400;500;600&family=Montserrat:wght@300;400;500&family=Dancing+Script&display=swap');

/* Custom Animation */
@keyframes subtle-fade {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-subtle-fade {
  animation: subtle-fade 0.8s ease-out forwards;
}

/* Elegant hover effects */
.hover-underline {
  position: relative;
}

.hover-underline::after {
  content: '';
  position: absolute;
  width: 0;
  height: 1px;
  bottom: -2px;
  left: 0;
  background-color: currentColor;
  transition: width 0.3s ease;
}

.hover-underline:hover::after {
  width: 100%;
}

/* Image hover effects */
.image-zoom-container {
  overflow: hidden;
}

.image-zoom {
  transition: transform 0.7s ease;
}

.image-zoom:hover {
  transform: scale(1.05);
}

/* Elegant card styles */
.elegant-card {
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.elegant-card:hover {
  border-color: rgba(0, 0, 0, 0.1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

/* Decorative elements */
.decorative-line {
  position: relative;
}

.decorative-line::before {
  content: '';
  position: absolute;
  width: 40px;
  height: 1px;
  background-color: currentColor;
  top: 50%;
  left: -60px;
}

.decorative-line::after {
  content: '';
  position: absolute;
  width: 40px;
  height: 1px;
  background-color: currentColor;
  top: 50%;
  right: -60px;
}

:root {
  --font-dancing-script: 'Dancing Script';
}