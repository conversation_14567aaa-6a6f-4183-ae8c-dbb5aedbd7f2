import { logError } from "@/lib/errorLogger";
import { doc, getDoc, setDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";

// Cache for content to avoid multiple fetches during development
const contentCache = new Map<string, {data: unknown, timestamp: number}>();
const CACHE_TTL = 10000; // 10 seconds cache TTL for development

export async function fetchContent<T>(
  collection: string,
  docId: string,
  options?: {
    fallbackCollection?: string;
    fallbackDocId?: string;
    defaultValue?: T;
    transformData?: (data: unknown) => T;
  }
): Promise<T> {
  try {
    const cacheKey = `${collection}:${docId}`;
    const now = Date.now();
    
    // Check cache first
    const cached = contentCache.get(cacheKey);
    if (cached && (now - cached.timestamp < CACHE_TTL)) {
      return options?.transformData 
        ? options.transformData(cached.data) 
        : cached.data as T;
    }
    
    // Try primary source
    const isStaticBuild = process.env.NEXT_PUBLIC_STATIC_BUILD === 'true';
    
    if (isStaticBuild) {
      // JSON file mode - read from /data/docId.json
      try {
        const response = await fetch(`/data/${docId}.json`);
        if (response.ok) {
          const data = await response.json() as T;
          contentCache.set(cacheKey, {data, timestamp: now});
          return options?.transformData
            ? options.transformData(data)
            : data;
        }
      } catch (jsonError) {
        logError(jsonError instanceof Error ? jsonError : new Error(String(jsonError)),
          "contentRepository.fetchContent.json", {
            context: { collection, documentId: docId }
          });
      }
      // For static mode, skip fallback and go straight to default
      return options?.defaultValue as T;
    } else {
      // Firestore mode - existing logic
      const docRef = doc(db, collection, docId);
      const snapshot = await getDoc(docRef);
      if (snapshot.exists()) {
        const data = snapshot.data() as T;
        contentCache.set(cacheKey, {data, timestamp: now});
        return options?.transformData
          ? options.transformData(data)
          : data;
      }
    }
    
    // Try fallback collection if provided
    if (options?.fallbackCollection) {
      const fallbackCollection = options.fallbackCollection;
      const fallbackDocId = options.fallbackDocId || docId;
      
      const fallbackDocRef = doc(db, fallbackCollection, fallbackDocId);
      const fallbackSnapshot = await getDoc(fallbackDocRef);
      if (fallbackSnapshot.exists()) {
        const fallbackData = fallbackSnapshot.data() as T;
        contentCache.set(cacheKey, {data: fallbackData, timestamp: now});
        return options?.transformData 
          ? options.transformData(fallbackData) 
          : fallbackData;
      }
    }
    
    // Return default if nothing found
    return options?.defaultValue as T;
  } catch (error) {
    logError(error instanceof Error ? error : new Error(String(error)), 
      "contentRepository.fetchContent", {
        context: { collection, documentId: docId }
      });
    throw error;
  }
}

// Save content - only supported for Firestore
export async function saveContent<T>(
  collection: string,
  docId: string,
  data: T
): Promise<void> {
  try {
    // Only Firestore supports saving
    const docRef = doc(db, collection, docId);
    await getDoc(docRef); // Validate connection
    await setDoc(docRef, data as Record<string, unknown>);
  } catch (error) {
    logError(error instanceof Error ? error : new Error(String(error)), 
      "contentRepository.saveContent", {
        context: { collection, documentId: docId }
      });
    throw error;
  }
}
