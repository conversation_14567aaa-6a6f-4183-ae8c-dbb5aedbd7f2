export interface HomeContent {
    heroTitle: string;
    heroText: string;
    ctaText: string;
    ctaLink: string;
    profileImage: string;
    aboutText: string[];
    artworks?: {
        title: string;
        image: string;
        description: string;
        year: string;
        size: string;
        galleryPath: string;
    }[];
    // Future enhancement: Replace artworks array with references
    // featuredArtworkIds?: string[];
}
