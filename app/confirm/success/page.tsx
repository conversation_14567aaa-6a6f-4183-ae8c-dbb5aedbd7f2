import { NextPage } from 'next';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { CheckCircle } from 'lucide-react'; // Using an icon for visual feedback

const ConfirmationSuccessPage: NextPage = () => {
  return (
    <div className="container mx-auto px-4 py-12 flex justify-center items-center min-h-[calc(100vh-200px)]">
      <Card className="w-full max-w-md text-center">
        <CardHeader className="items-center">
          <CheckCircle className="w-12 h-12 text-green-500 mb-2" />
          <CardTitle>Email Confirmed!</CardTitle>
          <CardDescription>Your subscription is now active.</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-6">
            Thank you for confirming your email address. You will now receive updates on future events and news.
          </p>
          <Button asChild>
            <Link href="/">Return to Homepage</Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default ConfirmationSuccessPage;
