import { defineConfig } from "vitest/config";
import path from "path";

export default defineConfig({
  test: {
    globals: true,
    environment: "jsdom",
    setupFiles: ["./test/unit/setup.ts"],
    testTimeout: 30000,

    // Only run *.test.tsx? files, not *.spec.ts (which are for e2e/integration)
    include: ["**/*.test.{ts,tsx}"],

    // Explicitly exclude e2e, integration, and functions tests
    exclude: [
      "**/node_modules/**",
      "**/dist/**",
      "**/e2e/**",
      "**/integration/**",
      "**/functions/**",
      "../.next/**",
      "../out/**"
    ],

    coverage: {
      provider: "v8",
      reporter: ["text", "json", "html"],
      reportsDirectory: path.resolve(__dirname, "output/coverage/unit"),
      exclude: [
        "out/**",
        "out-dynamic/**",
        ".next/**",
        "functions/**/*",
        "test/**/*",
        "node_modules/**/*",
        "scripts/**/*",
        "types/**/*",
        "**/*.d.ts",
        "**/*.test.ts",
        "**/*.test.tsx",
        "**/*.spec.ts",
        "**/*.config.*",
        "**/webpack*.js",
        "next.config.ts",
        "playwright.config.ts",
        "postcss.config.mjs",
        "tailwind.config.ts",
        "vitest.config.ts",
        "eslint.config.mjs",
        "knip.json",
        "components.json",
      ],
    },
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "../"),
    },
  },
});
