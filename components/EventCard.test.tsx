import React from "react";
import { describe, it, expect, vi } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import { EventCard } from "./EventCard";

// Mock Next.js Image component
vi.mock("next/image", () => ({
  default: ({ src, alt, className, fill, sizes, priority }: any) => {
    return (
      // eslint-disable-next-line @next/next/no-img-element
      <img
        src={src}
        alt={alt}
        className={className}
        data-fill={fill}
        data-sizes={sizes}
        data-priority={priority}
      />
    );
  },
}));

describe("EventCard", () => {
  const mockEvent = {
    id: 1,
    title: "Test Event",
    date: "January 1, 2025",
    location: "Test Location",
    description: "This is a test event description. It has multiple sentences. This is the third sentence.",
    images: {
      main: "/test-image.jpg",
      details: "/test-details.jpg",
    },
  };

  it("renders event details correctly", () => {
    render(<EventCard {...mockEvent} />);
    
    expect(screen.getByText("Test Event")).toBeInTheDocument();
    expect(screen.getByText("January 1, 2025")).toBeInTheDocument();
    expect(screen.getByText("Test Location")).toBeInTheDocument();
    
    // Check if description paragraphs are rendered
    expect(screen.getByText("This is a test event description.")).toBeInTheDocument();
    expect(screen.getByText("It has multiple sentences.")).toBeInTheDocument();
    expect(screen.getByText("This is the third sentence.")).toBeInTheDocument();
  });

  it("renders main image correctly", () => {
    render(<EventCard {...mockEvent} />);
    
    const mainImage = screen.getByAltText("Test Event - Main Invitation");
    expect(mainImage).toBeInTheDocument();
    expect(mainImage).toHaveAttribute("src", "/test-image.jpg");
  });

  it("renders details image when provided", () => {
    render(<EventCard {...mockEvent} />);
    
    const detailsImage = screen.getByAltText("Test Event - Event Details");
    expect(detailsImage).toBeInTheDocument();
    expect(detailsImage).toHaveAttribute("src", "/test-details.jpg");
  });

  it("doesn't render details image when not provided", () => {
    const eventWithoutDetails = {
      ...mockEvent,
      images: {
        main: "/test-image.jpg",
      },
    };
    
    render(<EventCard {...eventWithoutDetails} />);
    
    expect(screen.queryByAltText("Test Event - Event Details")).not.toBeInTheDocument();
  });

  it("calls onImageClick with correct index when main image is clicked", () => {
    const onImageClick = vi.fn();
    render(<EventCard {...mockEvent} onImageClick={onImageClick} eventIndex={1} />);
    
    const mainImageButton = screen.getByTitle("View Test Event invitation");
    fireEvent.click(mainImageButton);
    
    expect(onImageClick).toHaveBeenCalledWith(2);
  });

  it("calls onImageClick with correct index when details image is clicked", () => {
    const onImageClick = vi.fn();
    render(<EventCard {...mockEvent} onImageClick={onImageClick} eventIndex={1} />);
    
    const detailsImageButton = screen.getByTitle("View Test Event details");
    fireEvent.click(detailsImageButton);
    
    expect(onImageClick).toHaveBeenCalledWith(3);
  });

  it("uses correct grid layout when details image is present", () => {
    render(<EventCard {...mockEvent} />);
    
    // Check if the grid has the correct class for two images
    const gridDiv = screen.getByText("Test Event").closest("div")?.nextElementSibling;
    expect(gridDiv).toHaveClass("grid");
    expect(gridDiv).toHaveClass("lg:grid-cols-2");
  });

  it("uses correct grid layout when only main image is present", () => {
    const eventWithoutDetails = {
      ...mockEvent,
      images: {
        main: "/test-image.jpg",
      },
    };
    
    render(<EventCard {...eventWithoutDetails} />);
    
    // Check if the grid has the correct class for one image
    const gridDiv = screen.getByText("Test Event").closest("div")?.nextElementSibling;
    expect(gridDiv).toHaveClass("grid");
    expect(gridDiv).toHaveClass("lg:grid-cols-1");
  });
});