import type { Config } from 'jest';

const config: Config = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  testMatch: ['**/__tests__/unit/**/*.test.ts'],
  collectCoverage: true,
  collectCoverageFrom: ['../src/**/*.ts', '!../src/index.ts'],
  coverageDirectory: '../../test/output/coverage/functions-unit',
  coverageReporters: ['json', 'lcov', 'text', 'clover', 'html'],
  setupFiles: ['./unit/setup.ts'],
  testTimeout: 10000,
  transform: {
    '^.+\\.tsx?$': 'ts-jest',
  },
};

export default config;