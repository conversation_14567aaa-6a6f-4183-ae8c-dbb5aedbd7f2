import type { Config } from 'jest';

const config: Config = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  testMatch: ['**/__tests__/integration/**/*.test.ts'],
  collectCoverage: true,
  collectCoverageFrom: ['../src/**/*.ts', '!../src/index.ts'],
  coverageDirectory: '../../test/output/coverage/functions-integration',
  coverageReporters: ['json', 'lcov', 'text', 'clover', 'html'],
  setupFilesAfterEnv: ['./integration/setup.ts'],
  testTimeout: 30000, // Longer timeout for integration tests
  // Force tests to run sequentially to prevent race conditions
  maxWorkers: 1,
  transform: {
    '^.+\\.tsx?$': 'ts-jest',
  },
};

export default config;