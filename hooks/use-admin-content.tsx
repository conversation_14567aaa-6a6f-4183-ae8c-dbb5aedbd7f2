"use client";

import { useState, useEffect, useCallback } from "react";
import { db } from "@/lib/firebase";
import { doc, getDoc, setDoc } from "firebase/firestore";
import { logError } from "@/lib/errorLogger";
import { useToast } from "./use-toast";

const isError = (a: unknown): a is Error => a instanceof Error;

interface UseAdminContentOptions<T> {
  previewCollection: string;
  previewDocId: string;
  liveCollection?: string;
  liveDocId?: string;
  defaultValue: T;
  createPreviewIfMissing?: boolean;
}

interface UseAdminContentReturn<T> {
  content: T;
  setContent: (content: T) => void;
  loading: boolean;
  saving: boolean;
  error: Error | null;
  isDirty: boolean;
  saveContent: () => Promise<void>;
  resetContent: () => void;
}

/**
 * A hook for managing admin content with preview/live data handling
 *
 * @param options Configuration options for the hook
 * @returns Admin content state and functions
 */
export function useAdminContent<T>({
  previewCollection,
  previewDocId,
  liveCollection,
  liveDocId,
  defaultValue,
  createPreviewIfMissing = false,
  transformData,
}: UseAdminContentOptions<T> & {
  transformData?: {
    load?: (data: unknown) => T;
    save?: (newData: T, existingData: unknown) => unknown;
  };
}): UseAdminContentReturn<T> {
  const [content, setContent] = useState<T>(defaultValue);
  const [originalContent, setOriginalContent] = useState<T>(defaultValue);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { showToast } = useToast();

  // Calculate if content has changed from original
  const isDirty = JSON.stringify(content) !== JSON.stringify(originalContent);

  // Function to update content and mark as dirty
  const updateContent = useCallback((newContent: T) => {
    setContent(newContent);
  }, []);

  // Reset content to original state
  const resetContent = useCallback(() => {
    setContent(originalContent);
  }, [originalContent]);

  // Initial data loading
  useEffect(() => {
    let isMounted = true; // Add mounted flag to prevent state updates after unmount
    
    async function loadContent() {
      if (!isMounted) return; // Skip if component unmounted
      
      setLoading(true);
      setError(null);
      try {
        // Try preview first
        const previewRef = doc(db, previewCollection, previewDocId);
        const previewSnap = await getDoc(previewRef);
        let foundInPreview = false;
        let previewData: T = defaultValue;
        
        if (previewSnap.exists()) {
          const rawPreviewData = previewSnap.data() as Record<string, unknown>;
          // If transformData.load is provided, use it
          if (transformData?.load) {
            previewData = transformData.load(rawPreviewData);
            // Special case: if the loaded data is empty (e.g. {artworks: []}), check if the slug is missing
            const slugsArr = Array.isArray(rawPreviewData.slugs)
              ? (rawPreviewData.slugs as Record<string, unknown>[])
              : [];
            const slugToCheck = (
              defaultValue as Record<string, unknown> & { slug?: string }
            ).slug;
            const slugExists = slugsArr.some((s) => s.slug === slugToCheck);
            if (slugExists) {
              foundInPreview = true;
            }
          } else {
            previewData = rawPreviewData as T;
            foundInPreview = true;
          }
          // If the previewData is not empty, use it
          if (
            previewData &&
            (previewData as any).artworks &&
            (previewData as any).artworks.length > 0
          ) {
            foundInPreview = true;
          }
        }
        
        if (!isMounted) return; // Check again before state updates
        
        if (foundInPreview) {
          setContent(previewData);
          setOriginalContent(previewData);
        } else if (liveCollection && liveDocId) {
          // Fallback to live data if not found in preview
          const liveRef = doc(db, liveCollection, liveDocId);
          const liveSnap = await getDoc(liveRef);
          
          if (!isMounted) return; // Check again before state updates
          
          if (liveSnap.exists()) {
            const rawLiveData = liveSnap.data() as Record<string, unknown>;
            const liveData = transformData?.load
              ? transformData.load(rawLiveData)
              : (rawLiveData as T);
            setContent(liveData);
            setOriginalContent(liveData);
          } else {
            setContent(defaultValue);
            setOriginalContent(defaultValue);
          }
        } else {
          setContent(defaultValue);
          setOriginalContent(defaultValue);
        }
      } catch (err) {
        if (!isMounted) return; // Check before setting error state
        
        const error = isError(err) ? err : Error(String(err));
        logError(error, "useAdminContent.loadContent", {
          context: {
            previewCollection,
            previewDocId,
            liveCollection,
            liveDocId,
          },
        });
        setError(error);
        showToast(`Failed to load content: ${error.message}`, {
          variant: "destructive",
        });
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    }
    
    loadContent();
    
    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, [
    previewCollection,
    previewDocId,
    liveCollection,
    liveDocId,
    defaultValue,
    createPreviewIfMissing,
    showToast,
    transformData,
  ]);

  // Save content to preview
  const saveContent = useCallback(async () => {
    setSaving(true);
    setError(null);
    try {
      const previewRef = doc(db, previewCollection, previewDocId);
      let dataToSave = content;
      if (transformData?.save) {
        let existingData = null;
        if (liveCollection && liveDocId) {
          const liveRef = doc(db, liveCollection, liveDocId);
          const liveSnap = await getDoc(liveRef);
          if (liveSnap.exists()) existingData = liveSnap.data();
        }
        dataToSave = transformData.save(content, existingData as any);
      }
      await setDoc(previewRef, dataToSave as Record<string, unknown>);
      setOriginalContent(content);
      showToast("Content saved successfully", {
        variant: "default",
      });
    } catch (err) {
      const error = isError(err) ? err : Error(String(err));
      logError(error, "useAdminContent.saveContent", {
        context: {
          previewCollection,
          previewDocId,
        },
      });
      setError(error);
      showToast(`Failed to save: ${error.message}`, {
        variant: "destructive",
      });
      throw error;
    } finally {
      setSaving(false);
    }
  }, [
    content,
    previewCollection,
    previewDocId,
    showToast,
    liveCollection,
    liveDocId,
    transformData,
  ]);

  return {
    content,
    setContent: updateContent,
    loading,
    saving,
    error,
    isDirty,
    saveContent,
    resetContent,
  };
}
