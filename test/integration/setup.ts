import { execSync } from 'child_process';
import { readFileSync } from 'fs';
import { resolve } from 'path';

// Load emulator ports from firebase.test.json
const configPath = resolve(__dirname, '../../firebase.test.json');
const config = JSON.parse(readFileSync(configPath, 'utf8'));
const firestorePort = config.emulators.firestore.port;
const storagePort = config.emulators.storage.port;
const functionsPort = config.emulators.functions.port;

// Global setup for integration tests
async function globalSetup() {
  console.log('Starting Firebase emulators...');
  console.log(`Using ports - Firestore: ${firestorePort}, Storage: ${storagePort}, Functions: ${functionsPort}`);
  
  try {
    // Start Firebase emulators in the background
    execSync(
      'cd functions && npm run serve:clean &',
      { stdio: 'inherit' }
    );
    
    // Give emulators time to start
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('Firebase emulators started successfully');
    
    // Return a function to stop emulators when tests are done
    return async () => {
      console.log('Stopping Firebase emulators...');
      try {
        execSync(`kill $(lsof -t -i:${firestorePort}) || true`);
        execSync(`kill $(lsof -t -i:${storagePort}) || true`);
        execSync(`kill $(lsof -t -i:${functionsPort}) || true`);
        console.log('Firebase emulators stopped');
      } catch (error) {
        console.error('Error stopping emulators:', error);
      }
    };
  } catch (error) {
    console.error('Failed to start Firebase emulators:', error);
    throw error;
  }
}

export default globalSetup;