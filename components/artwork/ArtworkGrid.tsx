"use client";

import React from "react";
import { Artwork, ArtworkDisplayMode } from "@/types/artworks";
import { Skeleton } from "@/components/ui/skeleton";
import { ArtworkDisplay } from "./ArtworkDisplay";

interface ArtworkGridProps {
  artworks: Artwork[];
  mode?: ArtworkDisplayMode;
  columns?: number;
  gap?: number;
  className?: string;
  resolvedUrls?: Record<string, string>;
  isLoading?: boolean;
}

/**
 * A grid component for displaying multiple artworks
 */
export function ArtworkGrid({
  artworks,
  mode = "standard",
  columns = 3,
  gap = 4,
  className = "",
  resolvedUrls = {},
  isLoading = false,
}: ArtworkGridProps) {
  if (isLoading)
    return (
      <div
        className={`grid gap-${gap} ${className}`}
        style={{
          gridTemplateColumns: `repeat(${columns}, minmax(0, 1fr))`,
        }}
      >
        {Array.from({
          length: 6,
        }).map((_, index) => (
          <Skeleton key={index} className="aspect-square w-full" />
        ))}
      </div>
    );

  if (!artworks.length)
    return (
      <div className="text-center py-8 text-muted-foreground">
        No artworks found
      </div>
    );

  return (
    <div
      className={`grid gap-${gap} ${className}`}
      style={{
        gridTemplateColumns: `repeat(${columns}, minmax(0, 1fr))`,
      }}
    >
      {artworks.map((artwork) => (
        <ArtworkDisplay
          key={artwork.id}
          artwork={artwork}
          mode={mode}
          resolvedUrls={resolvedUrls}
        />
      ))}
    </div>
  );
}
