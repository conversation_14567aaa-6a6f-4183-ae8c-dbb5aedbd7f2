import type { GalleryCategory, ArtworkCategory, Artwork } from "../types/artworks";
import { fetchContent } from "./content-repository";

interface GalleryData {
  categories?: GalleryCategory[] | { categories: GalleryCategory[] };
  slugs?: Array<{ slug: string; artworks: Artwork[] }> | Record<string, Artwork[]>;
}

/**
 * Fetch all gallery categories from Firestore (for gallery listing page)
 */
export async function getAllCategories(): Promise<GalleryCategory[]> {
  try {
    const galleryData = await fetchContent<GalleryData>('live', 'gallery', {
      defaultValue: {}
    });

    // Handle both possible structures
    if (galleryData.categories && Array.isArray(galleryData.categories))
      return galleryData.categories as GalleryCategory[];

    if (galleryData.categories?.categories && Array.isArray(galleryData.categories.categories))
      return galleryData.categories.categories as GalleryCategory[];

    return [];
  } catch {
    return [];
  }
}

/**
 * Fetch a specific gallery category with full artwork details
 */
export async function getCategoryBySlug(
  slug: string
): Promise<ArtworkCategory | null> {
  try {
    const galleryData = await fetchContent<GalleryData>('live', 'gallery', {
      defaultValue: {}
    });

    // Find the category in the categories array, handling both possible structures
    let categories = [];

    if (galleryData.categories && Array.isArray(galleryData.categories))
      ({ categories } = galleryData);
    else if (
      galleryData.categories?.categories &&
      Array.isArray(galleryData.categories.categories)
    )
      ({ categories } = galleryData.categories);
    else return null;

    const category = categories.find((cat: { slug: string }) => cat.slug === slug);

    if (!category) return null;

    // Get the artworks for this category from the slugs array (new format) or object (old format)
    let artworks: Artwork[] = [];
    if (Array.isArray(galleryData.slugs)) {
      const slugEntry = galleryData.slugs.find(
        (s: { slug: string }) => s.slug === slug
      );
      artworks = slugEntry?.artworks || [];
    } else if (galleryData.slugs && typeof galleryData.slugs === "object") {
      artworks = galleryData.slugs[slug] || [];
    }

    // Return in the ArtworkCategory format (only 'artworks' property)
    return {
      artworks,
    };
  } catch {
    return null;
  }
}
