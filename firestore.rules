rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // <PERSON><PERSON><PERSON> deny all access to other collections unless explicitly allowed
    match /{document=**} {
      allow read, write: if false;
    }

    // Rules for the 'subscribers' collection
    match /subscribers/{subscriberId} {
      // ALLOW CREATE: Anyone can create a new subscriber document if:
      // 1. The request is creating the document (not updating an existing one).
      // 2. The document being created has the expected fields and types.
      // 3. 'isConfirmed' is false, 'confirmationToken' is a string.
      // Note: Actual creation is done by Admin SDK, which bypasses these.
      // These rules are for defense-in-depth or if client-side creation was ever attempted.
      allow create: if request.auth == null // Allow unauthenticated users to sign up
                    && request.resource.data.email is string
                    && request.resource.data.confirmationToken is string
                    && request.resource.data.isConfirmed == false
                    && request.resource.data.subscribedAt == request.time
                    && !exists(/databases/$(database)/documents/subscribers/$(subscriberId)); // Ensure it's a create op

      // ALLOW READ: No public read access to individual subscriber documents.
      // If a user needed to check their own status, a more specific rule or a backend endpoint would be needed.
      allow read: if false; // Keep subscriber data private

      // ALLOW UPDATE:
      // Updates are handled by backend API with Admin SDK (bypasses rules).
      // If client-side updates were needed (e.g., user managing preferences),
      // rules would be like: allow update: if request.auth.uid == resource.data.userId;
      // For confirmation/unsubscription via tokens if done client-side (not our current design):
      // allow update: if request.resource.data.isConfirmed == true
      //                 && resource.data.isConfirmed == false
      //                 && request.resource.data.confirmationToken == resource.data.confirmationToken;
      // Or for unsubscription.
      // Since our backend handles this, we can keep client-side updates locked down.
      allow update: if false;


      // ALLOW DELETE:
      // Deletes are handled by backend API (if implemented, currently we mark as unsubscribed).
      // If client-side deletes were allowed:
      // allow delete: if request.auth.uid == resource.data.userId;
      // For now, lock down client-side deletes.
      allow delete: if false;
    }

    // --- Add other collection rules below if they exist ---
    // For example, if there was a 'publicContent' collection:
    // match /publicContent/{docId} {
    //   allow read: if true;
    //   allow write: if request.auth != null; // Example: only authenticated users can write
    // }
  }
}
