"use client";

import { useState, useEffect, useCallback } from "react";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Loader2, Trash2, RotateCcw } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface FirestoreTimestamp {
  _seconds: number;
  _nanoseconds: number;
}

type Timestamp = FirestoreTimestamp | Date;

interface Version {
  id: string;
  timestamp: Timestamp;
  pages: string[];
  gallery_items: string[];
}

export default function VersionManager() {
  const [versions, setVersions] = useState<Version[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedVersion, setSelectedVersion] = useState<Version | null>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteAfterRollbackDialogOpen, setDeleteAfterRollbackDialogOpen] =
    useState(false);
  const [rollbackInProgress, setRollbackInProgress] = useState(false);
  const [deleteInProgress, setDeleteInProgress] = useState(false);
  const { showToast } = useToast();

  // Helper to get the correct function endpoint
  const getFunctionUrl = (functionName: string) => {
    // Use the emulator if running locally
    if (
      typeof window !== "undefined" &&
      window.location.hostname === "localhost"
    ) {
      // Adjust projectId and region as needed
      return `http://127.0.0.1:5001/belagallery-9e01b/us-central1/${functionName}`;
    }
    // Production endpoint (adjust projectId and region as needed)
    return `https://us-central1-bela-gallery.cloudfunctions.net/${functionName}`;
  };

  const fetchVersions = useCallback(async () => {
    setLoading(true);
    try {
      // Fetch versions from Firestore
      const response = await fetch(getFunctionUrl("listVersionsFunction"), {
        method: "POST",
        headers: { "Content-Type": "application/json" },
      });

      const data = await response.json();

      if (data.success && Array.isArray(data.versions)) {
        setVersions(data.versions);
      } else {
        showToast(
          `Failed to load versions: ${data.error || "Invalid data format"}`,
          {
            variant: "error",
          }
        );
      }
    } catch (error) {
      showToast(
        `Error loading versions: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
        {
          variant: "error",
        }
      );
    } finally {
      setLoading(false);
    }
  }, [showToast]);

  useEffect(() => {
    fetchVersions();
  }, [fetchVersions]);

  const handleRollback = async () => {
    if (!selectedVersion) return;

    setRollbackInProgress(true);
    try {
      const response = await fetch(getFunctionUrl("rollback"), {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          data: {
            versionId: selectedVersion.id,
          },
        }),
      });

      const data = await response.json();
      if (data.success) {
        showToast(
          `Successfully rolled back to version: ${formatDate(
            selectedVersion.timestamp
          )}`,
          {
            variant: "default",
          }
        );
        setConfirmDialogOpen(false);

        // Ask if user wants to delete this backup
        setDeleteAfterRollbackDialogOpen(true);
      } else {
        showToast(`Rollback failed: ${data.error || "Unknown error"}`, {
          variant: "error",
        });
      }
    } catch (error) {
      showToast(
        `Error during rollback: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
        {
          variant: "error",
        }
      );
    } finally {
      setRollbackInProgress(false);
    }
  };

  const handleDeleteVersion = async (afterRollback = false) => {
    if (!selectedVersion) return;

    setDeleteInProgress(true);
    try {
      const response = await fetch(getFunctionUrl("deleteVersionFunction"), {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          data: {
            versionId: selectedVersion.id,
          },
        }),
      });

      const data = await response.json();
      if (data.success) {
        showToast(
          `Successfully deleted version: ${formatDate(
            selectedVersion.timestamp
          )}`,
          {
            variant: "default",
          }
        );
        setDeleteDialogOpen(false);
        setDeleteAfterRollbackDialogOpen(false);

        // Refresh the versions list
        fetchVersions();

        // If this was after a rollback, reload the page
        if (afterRollback) {
          window.location.reload();
        }
      } else {
        showToast(`Delete failed: ${data.error || "Unknown error"}`, {
          variant: "error",
        });
      }
    } catch (error) {
      showToast(
        `Error during delete: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
        {
          variant: "error",
        }
      );
    } finally {
      setDeleteInProgress(false);
    }
  };

  const formatDate = (timestamp: Timestamp) => {
    try {
      // Handle Firestore timestamp
      if (
        timestamp &&
        typeof timestamp === "object" &&
        "_seconds" in timestamp
      ) {
        const milliseconds =
          timestamp._seconds * 1000 + (timestamp._nanoseconds || 0) / 1000000;
        return new Intl.DateTimeFormat("en-US", {
          year: "numeric",
          month: "short",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
          timeZoneName: "short",
        }).format(new Date(milliseconds));
      }

      // Handle regular Date object
      if (timestamp instanceof Date) {
        return new Intl.DateTimeFormat("en-US", {
          year: "numeric",
          month: "short",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
          timeZoneName: "short",
        }).format(timestamp);
      }

      // Fallback for invalid dates
      return "Invalid date";
    } catch (error) {
      console.error("Error formatting date:", error);
      return "Invalid date";
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Version Management</h2>
        <Button
          onClick={fetchVersions}
          variant="outline"
          size="sm"
          disabled={loading}
        >
          {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : "Refresh"}
        </Button>
      </div>

      {loading ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      ) : versions.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          No versions found. Publish content to create versions.
        </div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Version Date</TableHead>
              <TableHead>Content</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {versions.map((version) => (
              <TableRow key={version.id}>
                <TableCell>{formatDate(version.timestamp)}</TableCell>
                <TableCell>
                  <div className="text-sm">
                    <span className="font-medium">Pages:</span>{" "}
                    {version.pages?.join(", ") || "None"}
                    <br />
                    <span className="font-medium">Gallery:</span>{" "}
                    {version.gallery_items?.join(", ") || "None"}
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <Button
                    variant="outline"
                    size="sm"
                    className="mr-2"
                    onClick={() => {
                      setSelectedVersion(version);
                      setConfirmDialogOpen(true);
                    }}
                  >
                    <RotateCcw className="h-4 w-4 mr-1" /> Rollback
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-red-500 hover:text-red-600"
                    onClick={() => {
                      setSelectedVersion(version);
                      setDeleteDialogOpen(true);
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}

      {/* Rollback Confirmation Dialog */}
      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Rollback</DialogTitle>
            <DialogDescription>
              Are you sure you want to roll back to version from{" "}
              {selectedVersion && formatDate(selectedVersion.timestamp)}? This
              will replace the current live content with this version.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setConfirmDialogOpen(false)}
              disabled={rollbackInProgress}
            >
              Cancel
            </Button>
            <Button
              onClick={handleRollback}
              disabled={rollbackInProgress}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {rollbackInProgress ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Rolling Back...
                </>
              ) : (
                "Confirm Rollback"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Version</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this version from{" "}
              {selectedVersion && formatDate(selectedVersion.timestamp)}? This
              action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleteInProgress}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleDeleteVersion();
              }}
              disabled={deleteInProgress}
              className="bg-red-600 hover:bg-red-700"
            >
              {deleteInProgress ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Delete After Rollback Dialog */}
      <AlertDialog
        open={deleteAfterRollbackDialogOpen}
        onOpenChange={(open) => {
          setDeleteAfterRollbackDialogOpen(open);
          if (!open) {
            window.location.reload(); // Reload if dialog is closed without action
          }
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Backup Version?</AlertDialogTitle>
            <AlertDialogDescription>
              Would you like to delete the backup version from{" "}
              {selectedVersion && formatDate(selectedVersion.timestamp)} that
              you just rolled back to?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => {
                window.location.reload(); // Reload the page
              }}
              disabled={deleteInProgress}
            >
              No, Keep Backup
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleDeleteVersion(true);
              }}
              disabled={deleteInProgress}
              className="bg-red-600 hover:bg-red-700"
            >
              {deleteInProgress ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Yes, Delete Backup"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
