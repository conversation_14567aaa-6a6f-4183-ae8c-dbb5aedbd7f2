{"project": ["!functions/**"], "entry": ["app/**/*.{ts,tsx}", "components/**/*.{ts,tsx}", "lib/**/*.{ts,tsx}", "hooks/**/*.{ts,tsx}", "scripts/**/*.{ts,js}", "eslint.config.mjs", "next.config.js", "playwright.config.ts", "postcss.config.mjs"], "ignore": ["**/*.test.{ts,tsx}", "**/*.spec.{ts,tsx}", "test-utils/**", "coverage/**", "e2e/**", "integration/**", "functions/**"], "ignoreExportsUsedInFile": true}