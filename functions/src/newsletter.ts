import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import { sendEmail, createNewsletterEmailHtml } from "./email";

if (!admin.apps.length) admin.initializeApp();

const db = admin.firestore();

// Send newsletter function
export const sendNewsletterFunction = functions.https.onRequest(async (req, res) => {
  // Enable CORS
  res.set("Access-Control-Allow-Origin", "*");
  res.set("Access-Control-Allow-Methods", "POST, OPTIONS");
  res.set("Access-Control-Allow-Headers", "Content-Type");
  
  if (req.method === "OPTIONS") {
    res.status(204).send("");
    return;
  }

  if (req.method !== "POST") {
    res.status(405).send("Method Not Allowed");
    return;
  }

  try {
    const { 
      subject, 
      content, 
      featuredArtworks, 
      upcomingEvents, 
      authorName 
    } = req.body;

    // Validate required fields
    if (!subject || !content) {
      res.status(400).json({ error: 'Subject and content are required' });
      return;
    }

    // Get all confirmed subscribers (filter out unsubscribed ones)
    const subscribersRef = db.collection('subscribers');
    const confirmedSubscribers = await subscribersRef
      .where('isConfirmed', '==', true)
      .get();

    if (confirmedSubscribers.empty) {
      res.status(404).json({ error: 'No confirmed subscribers found' });
      return;
    }

    const subscribers = confirmedSubscribers.docs
      .map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          name: data.name || data.email.split('@')[0],
          email: data.email,
          confirmationToken: data.confirmationToken,
          isConfirmed: data.isConfirmed,
          subscribedAt: data.subscribedAt,
          confirmedAt: data.confirmedAt,
          unsubscribedAt: data.unsubscribedAt
        };
      })
      .filter(subscriber => !subscriber.unsubscribedAt); // Filter out unsubscribed users

    functions.logger.info(`📧 Sending newsletter to ${subscribers.length} subscribers`);
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

    // Send emails to all subscribers
    const emailPromises = subscribers.map(async (subscriber) => {
      try {
        const unsubscribeUrl = `${baseUrl}/unsubscribe/${subscriber.confirmationToken}`;
        
        const emailHtml = createNewsletterEmailHtml({
          name: subscriber.name,
          subject,
          content,
          featuredArtworks: featuredArtworks || [],
          upcomingEvents: upcomingEvents || [],
          authorName: authorName || 'Bela Gallery Team',
          unsubscribeUrl
        });

        await sendEmail({
          to: subscriber.email,
          subject: `${subject} - Bela Gallery`,
          html: emailHtml,
        });

        return { email: subscriber.email, status: 'sent' };
      } catch (error) {
        functions.logger.error(`Failed to send newsletter to ${subscriber.email}:`, error);
        return { email: subscriber.email, status: 'failed', error: error instanceof Error ? error.message : 'Unknown error' };
      }
    });

    const results = await Promise.all(emailPromises);
    const successful = results.filter(r => r.status === 'sent').length;
    const failed = results.filter(r => r.status === 'failed').length;

    // Store newsletter in database for record keeping
    await db.collection('newsletters').add({
      subject,
      content,
      featuredArtworks: featuredArtworks || [],
      upcomingEvents: upcomingEvents || [],
      authorName: authorName || 'Bela Gallery Team',
      sentAt: new Date(),
      recipientCount: subscribers.length,
      successfulSends: successful,
      failedSends: failed,
      results
    });

    res.status(200).json({ 
      message: `Newsletter sent successfully to ${successful} subscribers`,
      successful,
      failed,
      total: subscribers.length,
      results
    });

  } catch (error) {
    functions.logger.error('Newsletter sending error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';
    res.status(500).json({ error: errorMessage });
  }
});

// Get newsletter history function
export const getNewsletterHistoryFunction = functions.https.onRequest(async (req, res) => {
  // Enable CORS
  res.set("Access-Control-Allow-Origin", "*");
  res.set("Access-Control-Allow-Methods", "GET, OPTIONS");
  res.set("Access-Control-Allow-Headers", "Content-Type");
  
  if (req.method === "OPTIONS") {
    res.status(204).send("");
    return;
  }

  if (req.method !== "GET") {
    res.status(405).send("Method Not Allowed");
    return;
  }

  try {
    const newslettersRef = db.collection('newsletters');
    const newsletters = await newslettersRef.orderBy('sentAt', 'desc').limit(20).get();

    const newsletterHistory = newsletters.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      sentAt: doc.data().sentAt?.toDate?.()?.toISOString() || doc.data().sentAt
    }));

    res.status(200).json({ newsletters: newsletterHistory });

  } catch (error) {
    functions.logger.error('Error fetching newsletter history:', error);
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';
    res.status(500).json({ error: errorMessage });
  }
});

// Get artworks for newsletter function
export const getNewsletterArtworksFunction = functions.https.onRequest(async (req, res) => {
  // Enable CORS
  res.set("Access-Control-Allow-Origin", "*");
  res.set("Access-Control-Allow-Methods", "GET, OPTIONS");
  res.set("Access-Control-Allow-Headers", "Content-Type");
  
  if (req.method === "OPTIONS") {
    res.status(204).send("");
    return;
  }

  if (req.method !== "GET") {
    res.status(405).send("Method Not Allowed");
    return;
  }

  try {
    const url = new URL(req.url!, `http://${req.headers.host}`);
    const search = url.searchParams.get('search') || '';
    const category = url.searchParams.get('category') || '';
    const limit = parseInt(url.searchParams.get('limit') || '20');

    // Get artworks from the artworks collection
    const artworksRef = db.collection('artworks');
    
    // Build query with category filter if specified
    const query = category && category !== 'all'
      ? artworksRef.where('category', '==', category)
      : artworksRef;

    // Get the artworks
    const artworksSnapshot = await query.limit(limit * 2).get(); // Get more to allow for filtering

    interface ArtworkData {
      id: string;
      title?: string;
      description?: string;
      medium?: string;
      tags?: string[];
      image?: string;
      year?: string;
      category?: string;
    }

    let artworks: ArtworkData[] = artworksSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as ArtworkData));

    // Apply search filter in memory
    if (search) {
      const searchLower = search.toLowerCase();
      artworks = artworks.filter((artwork) =>
        artwork.title?.toLowerCase().includes(searchLower) ||
        artwork.description?.toLowerCase().includes(searchLower) ||
        artwork.medium?.toLowerCase().includes(searchLower) ||
        artwork.tags?.some((tag: string) => tag.toLowerCase().includes(searchLower))
      );
    }

    // Limit results after filtering
    artworks = artworks.slice(0, limit);

    // Format artworks for newsletter selection
    const formattedArtworks = artworks.map((artwork) => ({
      id: artwork.id,
      title: artwork.title || 'Untitled',
      description: artwork.description || '',
      imageUrl: artwork.image || '',
      year: artwork.year || '',
      medium: artwork.medium || '',
      category: artwork.category || '',
      galleryUrl: artwork.category ? `/gallery/${artwork.category}` : '/gallery'
    }));

    // Get unique categories for filtering
    const allArtworks = await db.collection('artworks').get();
    const categories = [...new Set(allArtworks.docs.map(doc => doc.data().category).filter(Boolean))].sort();

    res.status(200).json({ 
      artworks: formattedArtworks,
      categories 
    });

  } catch (error) {
    functions.logger.error('Error fetching artworks:', error);
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';
    res.status(500).json({ error: errorMessage });
  }
});

// Get events for newsletter function
export const getNewsletterEventsFunction = functions.https.onRequest(async (req, res) => {
  // Enable CORS
  res.set("Access-Control-Allow-Origin", "*");
  res.set("Access-Control-Allow-Methods", "GET, OPTIONS");
  res.set("Access-Control-Allow-Headers", "Content-Type");

  if (req.method === "OPTIONS") {
    res.status(204).send("");
    return;
  }

  if (req.method !== "GET") {
    res.status(405).send("Method Not Allowed");
    return;
  }

  try {
    // Get events content directly (no "published" concept)
    const eventsRef = db.collection('live').doc('events');
    const eventsDoc = await eventsRef.get();

    if (!eventsDoc.exists) {
      res.status(200).json({
        events: [],
        message: 'No events found. Create events through the admin interface first.'
      });
      return;
    }

    const eventsData = eventsDoc.data();

    // Get events directly from the document
    const events = eventsData?.events || [];

    // Get relevant events (ongoing and upcoming) using helper
    const relevantEvents = getRelevantEvents(events);

    // Format events for newsletter selection
    const formattedEvents = relevantEvents.map((event, index) => ({
      id: `event-${index}`,
      title: event.title || 'Untitled Event',
      date: event.date || '',
      description: event.description || '',
      location: event.location || '',
      image: event.images?.main || '',
      status: event._status
    }));

    res.status(200).json({ events: formattedEvents });

  } catch (error) {
    functions.logger.error('❌ Error fetching events:', error);
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';
    res.status(500).json({ error: errorMessage });
  }
});

// Helper function to get relevant events (copied from eventsHelpers)
function getRelevantEvents(events: any[]): any[] {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  return events.filter(event => {
    if (!event.date) return false;

    const eventDate = new Date(event.date);
    const eventDateOnly = new Date(eventDate.getFullYear(), eventDate.getMonth(), eventDate.getDate());

    // Include events that are today or in the future
    return eventDateOnly >= today;
  }).map(event => ({
    ...event,
    _status: getEventStatus(event.date)
  }));
}

// Helper function to get event status
function getEventStatus(dateString: string): string {
  if (!dateString) return 'unknown';

  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const eventDate = new Date(dateString);
  const eventDateOnly = new Date(eventDate.getFullYear(), eventDate.getMonth(), eventDate.getDate());

  if (eventDateOnly < today) {
    return 'past';
  } else if (eventDateOnly.getTime() === today.getTime()) {
    return 'today';
  } else {
    return 'upcoming';
  }
}
