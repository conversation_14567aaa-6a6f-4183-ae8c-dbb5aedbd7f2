// functions/__tests__/integration/index.test.ts
import * as admin from 'firebase-admin';
import { Request as ExpressRequest, Response } from 'express';
import { listVersionsFunction, deleteVersionFunction } from '../../src/index';

// Firebase Functions Request extends Express Request with additional properties
interface Request extends ExpressRequest {
  rawBody: Buffer;
}

// Import setup to ensure emulator connection
import './setup';

describe('Index Functions Integration Tests', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let db: admin.firestore.Firestore;

  beforeAll(async () => {
    // Initialize Firebase Admin with emulator
    if (!admin.apps.length) {
      admin.initializeApp({
        projectId: 'belagallery-test',
      });
    }
    db = admin.firestore();
  });

  beforeEach(async () => {
    // Clear all collections before each test
    await clearFirestore();
    
    // Setup request and response objects
    req = {
      method: 'POST',
      body: { data: {} },
      headers: {
        'content-type': 'application/json',
      },
      rawBody: Buffer.from('{}'),
    };
    
    res = {
      set: jest.fn().mockReturnThis(),
      status: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };
  });

  afterAll(async () => {
    // Clean up
    await clearFirestore();
  });

  async function clearFirestore() {
    const collections = ['backup_meta', 'backups', 'audit_log'];
    for (const collectionName of collections) {
      const snapshot = await db.collection(collectionName).get();
      const batch = db.batch();
      snapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });
      await batch.commit();
    }
  }

  describe('listVersionsFunction Integration', () => {
    it('should handle OPTIONS request', async () => {
      // Setup
      req.method = 'OPTIONS';
      
      // Execute
      await listVersionsFunction(req as Request, res as Response);
      
      // Verify
      expect(res.status).toHaveBeenCalledWith(204);
      expect(res.send).toHaveBeenCalledWith('');
    });

    it('should reject non-POST methods', async () => {
      // Setup
      req.method = 'GET';
      
      // Execute
      await listVersionsFunction(req as Request, res as Response);
      
      // Verify
      expect(res.status).toHaveBeenCalledWith(405);
      expect(res.send).toHaveBeenCalledWith('Method Not Allowed');
    });

    it('should return empty list when no versions exist', async () => {
      // Execute
      await listVersionsFunction(req as Request, res as Response);
      
      // Verify
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        versions: []
      });
    });

    it('should return list of versions when they exist', async () => {
      // Setup - Create some versions
      const versions = [
        {
          id: 'version-1',
          timestamp: new Date('2023-01-01'),
          createdAt: new Date('2023-01-01'),
          pages: ['home', 'about']
        },
        {
          id: 'version-2',
          timestamp: new Date('2023-01-02'),
          createdAt: new Date('2023-01-02'),
          pages: ['home']
        }
      ];

      // Add versions to Firestore
      const batch = db.batch();
      versions.forEach(version => {
        const docRef = db.collection('backup_meta').doc(version.id);
        batch.set(docRef, {
          timestamp: version.timestamp,
          createdAt: version.createdAt,
          pages: version.pages
        });
      });
      await batch.commit();

      // Execute
      await listVersionsFunction(req as Request, res as Response);

      // Verify
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        versions: expect.arrayContaining([
          expect.objectContaining({
            id: 'version-2',
            pages: ['home']
          }),
          expect.objectContaining({
            id: 'version-1',
            pages: ['home', 'about']
          })
        ])
      });
    });

    it('should handle errors gracefully', async () => {
      // Setup - Force an error by closing the database connection
      // This is a bit tricky with the emulator, so we'll simulate an error differently
      // by mocking the collection method to throw an error
      const originalCollection = db.collection;
      jest.spyOn(db, 'collection').mockImplementation(() => {
        throw new Error('Database connection error');
      });

      // Execute
      await listVersionsFunction(req as Request, res as Response);

      // Verify
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: 'Database connection error'
      });

      // Restore original method
      db.collection = originalCollection;
    });
  });

  describe('deleteVersionFunction Integration', () => {
    it('should handle OPTIONS request', async () => {
      // Setup
      req.method = 'OPTIONS';
      
      // Execute
      await deleteVersionFunction(req as Request, res as Response);
      
      // Verify
      expect(res.status).toHaveBeenCalledWith(204);
      expect(res.send).toHaveBeenCalledWith('');
    });

    it('should reject non-POST methods', async () => {
      // Setup
      req.method = 'GET';
      
      // Execute
      await deleteVersionFunction(req as Request, res as Response);
      
      // Verify
      expect(res.status).toHaveBeenCalledWith(405);
      expect(res.send).toHaveBeenCalledWith('Method Not Allowed');
    });

    it('should require versionId parameter', async () => {
      // Setup
      req.body = { data: {} };
      
      // Execute
      await deleteVersionFunction(req as Request, res as Response);
      
      // Verify
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: 'Version ID is required'
      });
    });

    it('should successfully delete an existing version', async () => {
      // Setup - Create a version
      const versionId = 'version-to-delete';
      const versionData = {
        timestamp: new Date(),
        createdAt: new Date(),
        pages: ['home', 'about']
      };

      // Create version metadata
      await db.collection('backup_meta').doc(versionId).set(versionData);
      
      // Create backup documents
      await db.collection('backups').doc(`home_${versionId}`).set({
        title: 'Home',
        content: 'Home content',
        _meta: { page: 'home', versionId, timestamp: new Date() }
      });
      await db.collection('backups').doc(`about_${versionId}`).set({
        title: 'About',
        content: 'About content',
        _meta: { page: 'about', versionId, timestamp: new Date() }
      });

      // Verify version exists
      let versionDoc = await db.collection('backup_meta').doc(versionId).get();
      expect(versionDoc.exists).toBe(true);

      // Setup request
      req.body = { data: { versionId } };

      // Execute
      await deleteVersionFunction(req as Request, res as Response);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        success: true
      });

      // Verify version was deleted
      versionDoc = await db.collection('backup_meta').doc(versionId).get();
      expect(versionDoc.exists).toBe(false);

      // Verify backup documents were deleted
      const homeBackup = await db.collection('backups').doc(`home_${versionId}`).get();
      expect(homeBackup.exists).toBe(false);
      
      const aboutBackup = await db.collection('backups').doc(`about_${versionId}`).get();
      expect(aboutBackup.exists).toBe(false);
    });

    it('should handle deletion of non-existent version', async () => {
      // Setup
      req.body = { data: { versionId: 'non-existent-version' } };

      // Execute
      await deleteVersionFunction(req as Request, res as Response);

      // Verify - should handle gracefully
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: expect.stringContaining('not found')
      });
    });
  });
});