import React from "react";
import { describe, it, expect, vi } from "vitest";
import { render, screen } from "@testing-library/react";
import { ProfileCard } from "./ProfileCard";

// Mock Next.js Image component
vi.mock("next/image", () => ({
  default: ({ src, alt, className }: any) => (
    // eslint-disable-next-line @next/next/no-img-element
    <img src={src} alt={alt} className={className} data-testid="next-image" />
  ),
}));

describe("ProfileCard", () => {
  it("renders with required props", () => {
    render(
      <ProfileCard
        image="/test-image.jpg"
        name="<PERSON>"
        bio="Test biography"
      />
    );

    // Check if the image is rendered
    const image = screen.getByTestId("next-image");
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute("src", "/test-image.jpg");
    expect(image).toHaveAttribute("alt", "John Doe");
    expect(image).toHaveClass("object-cover");

    // Check if the name is rendered
    expect(screen.getByText("John Doe")).toBeInTheDocument();
    expect(screen.getByText("John Doe")).toHaveClass("text-2xl");
    expect(screen.getByText("John Doe")).toHaveClass("font-semibold");

    // Check if the bio is rendered
    expect(screen.getByText("Test biography")).toBeInTheDocument();
    expect(screen.getByText("Test biography")).toHaveClass("text-modernArt-mutedForeground");
  });

  it("applies custom className", () => {
    render(
      <ProfileCard
        image="/test-image.jpg"
        name="John Doe"
        bio="Test biography"
        className="custom-class"
      />
    );

    // Get the main container div
    const container = screen.getByText("John Doe").closest("div")?.parentElement;
    expect(container).toHaveClass("custom-class");
  });

  it("renders bio as ReactNode", () => {
    const bioParagraphs = (
      <>
        <p>First paragraph</p>
        <p>Second paragraph</p>
      </>
    );

    render(
      <ProfileCard
        image="/test-image.jpg"
        name="John Doe"
        bio={bioParagraphs}
      />
    );

    // Check if both paragraphs are rendered
    expect(screen.getByText("First paragraph")).toBeInTheDocument();
    expect(screen.getByText("Second paragraph")).toBeInTheDocument();
  });
});