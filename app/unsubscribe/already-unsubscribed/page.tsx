import { NextPage } from 'next';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Info } from 'lucide-react'; // Icon for information

const AlreadyUnsubscribedPage: NextPage = () => {
  return (
    <div className="container mx-auto px-4 py-12 flex justify-center items-center min-h-[calc(100vh-200px)]">
      <Card className="w-full max-w-md text-center">
        <CardHeader className="items-center">
          <Info className="w-12 h-12 text-blue-500 mb-2" />
          <CardTitle>Already Unsubscribed</CardTitle>
          <CardDescription>You are already unsubscribed from our mailing list.</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-6">
            No further action is needed. You will not receive further email updates unless you choose to resubscribe.
          </p>
          <But<PERSON> asChild>
            <Link href="/">Return to Homepage</Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default AlreadyUnsubscribedPage;
