"use client";

import React, { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

const isError = (a: unknown): a is Error => a instanceof Error;

export default function PublishButton() {
  const [publishing, setPublishing] = useState(false);
  const { showToast } = useToast();

  // Helper to get the correct publish endpoint
  const getPublishUrl = () => {
    // Use the emulator if running locally
    if (
      typeof window !== "undefined" &&
      window.location.hostname === "localhost"
    ) {
      // Adjust projectId and region as needed
      return "http://127.0.0.1:5001/belagallery-9e01b/us-central1/publish";
    }
    // Production endpoint (adjust projectId and region as needed)
    return "https://us-central1-bela-gallery.cloudfunctions.net/publish";
  };

  const handlePublish = async () => {
    setPublishing(true);
    try {
      const response = await fetch(getPublishUrl(), {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ data: { clearPreview: true } }),
      });
      const data = await response.json();
      if (data.success) {
        showToast(
          `Content published successfully (Version: ${data.versionId})`,
          {
            variant: "default",
          }
        );
        window.location.reload();
      } else {
        showToast(`Publish failed: ${data.error || "Unknown error"}`, {
          variant: "destructive",
        });
      }
    } catch (error) {
      showToast(
        `Failed to publish: ${
          isError(error) ? error.message : "Unknown error"
        }`,
        {
          variant: "destructive",
        }
      );
    } finally {
      setPublishing(false);
    }
  };

  return (
    <Button
      onClick={handlePublish}
      disabled={publishing}
      variant="default"
      className="bg-green-600 hover:bg-green-700"
    >
      {publishing ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Publishing...
        </>
      ) : (
        "Publish All"
      )}
    </Button>
  );
}
