// functions/__tests__/index.test.ts
import { Request as ExpressRequest, Response } from 'express';
import { listVersionsFunction, deleteVersionFunction } from '../../src/index';

// Firebase Functions Request extends Express Request with additional properties
interface Request extends ExpressRequest {
  rawBody: Buffer;
}

// Import setup to ensure mocks are initialized
import './setup';

// Mock the backup module functions
jest.mock('../../src/backup', () => ({
  listVersions: jest.fn(),
  deleteVersion: jest.fn(),
}));

describe('Firebase Functions', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  const backupModule = require('../../src/backup');

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup request and response objects
    req = {
      method: 'POST',
      body: { data: {} },
      headers: {
        'content-type': 'application/json',
      },
    };
    
    res = {
      set: jest.fn().mockReturnThis(),
      status: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };
  });

  describe('listVersionsFunction', () => {
    it('should return versions when successful', async () => {
      // Setup
      const mockVersions = [
        { id: 'version1', timestamp: new Date() },
        { id: 'version2', timestamp: new Date() },
      ];
      backupModule.listVersions.mockResolvedValue(mockVersions);
      
      // Execute
      await listVersionsFunction(req as Request, res as Response);
      
      // Verify
      expect(backupModule.listVersions).toHaveBeenCalled();
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        versions: mockVersions,
      });
    });

    it('should handle OPTIONS request', async () => {
      // Setup
      req.method = 'OPTIONS';
      
      // Execute
      await listVersionsFunction(req as Request, res as Response);
      
      // Verify
      expect(res.status).toHaveBeenCalledWith(204);
      expect(res.send).toHaveBeenCalledWith('');
    });

    it('should reject non-POST methods', async () => {
      // Setup
      req.method = 'GET';
      
      // Execute
      await listVersionsFunction(req as Request, res as Response);
      
      // Verify
      expect(res.status).toHaveBeenCalledWith(405);
      expect(res.send).toHaveBeenCalledWith('Method Not Allowed');
    });

    it('should handle errors', async () => {
      // Setup
      const errorMessage = 'Test error';
      backupModule.listVersions.mockRejectedValue(new Error(errorMessage));
      
      // Execute
      await listVersionsFunction(req as Request, res as Response);
      
      // Verify
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: errorMessage,
      });
    });
  });

  describe('deleteVersionFunction', () => {
    it('should delete version when valid versionId is provided', async () => {
      // Setup
      req.body = { data: { versionId: 'test-version-id' } };
      backupModule.deleteVersion.mockResolvedValue({ success: true });
      
      // Execute
      await deleteVersionFunction(req as Request, res as Response);
      
      // Verify
      expect(backupModule.deleteVersion).toHaveBeenCalledWith('test-version-id');
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        success: true,
      });
    });

    it('should handle missing versionId', async () => {
      // Setup
      req.body = { data: {} };
      
      // Execute
      await deleteVersionFunction(req as Request, res as Response);
      
      // Verify
      expect(backupModule.deleteVersion).not.toHaveBeenCalled();
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: 'Version ID is required',
      });
    });

    it('should handle errors during deletion', async () => {
      // Setup
      req.body = { data: { versionId: 'test-version-id' } };
      const errorMessage = 'Version not found';
      backupModule.deleteVersion.mockRejectedValue(new Error(errorMessage));
      
      // Execute
      await deleteVersionFunction(req as Request, res as Response);
      
      // Verify
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: errorMessage,
      });
    });
  });
});