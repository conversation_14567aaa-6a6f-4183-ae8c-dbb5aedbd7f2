import { NextPage } from 'next';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Info } from 'lucide-react'; // Using an icon

const AlreadyConfirmedPage: NextPage = () => {
  return (
    <div className="container mx-auto px-4 py-12 flex justify-center items-center min-h-[calc(100vh-200px)]">
      <Card className="w-full max-w-md text-center">
        <CardHeader className="items-center">
          <Info className="w-12 h-12 text-blue-500 mb-2" />
          <CardTitle>Already Confirmed</CardTitle>
          <CardDescription>This email address has already been confirmed.</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-6">
            No further action is needed. You are already on our mailing list.
          </p>
          <Button asChild>
            <Link href="/">Return to Homepage</Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default AlreadyConfirmedPage;
