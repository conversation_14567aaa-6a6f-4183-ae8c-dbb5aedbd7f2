import { vi, describe, it, expect, beforeEach } from 'vitest';
import { fetchContent, saveContent } from './content-repository';
import { doc, getDoc, setDoc, DocumentReference, DocumentSnapshot } from 'firebase/firestore';
import { logError } from './errorLogger';



// Mock Firebase and logger
vi.mock('firebase/firestore', () => ({
  doc: vi.fn((db, collection, docId) => ({
    id: docId,
    path: `${collection}/${docId}`
  })),
  getDoc: vi.fn(),
  setDoc: vi.fn()
}));
vi.mock('./firebase', () => ({
  db: {}
}));
vi.mock('./errorLogger');
vi.mock('@/lib/firebase', () => ({
  db: {}
}));
vi.mock('@/lib/errorLogger', () => ({
  logError: vi.fn()
}));

describe('content-repository', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.resetAllMocks();
  });

  describe('fetchContent', () => {
    it('should fetch content from primary collection', async () => {
      // Setup
      const mockData = { title: 'Test Title' };
      const mockSnapshot = {
        exists: () => true,
        data: () => mockData
      };
      
      const mockDocRef = { 
        id: 'docId',
        path: 'collection/docId'
      } as unknown as DocumentReference;
      
      const mockDocSnapshot = {
        exists: mockSnapshot.exists,
        data: mockSnapshot.data,
        id: 'docId',
        metadata: {},
        get: vi.fn(),
        ref: mockDocRef
      } as unknown as DocumentSnapshot;
      
      vi.mocked(doc).mockReturnValue(mockDocRef);
      vi.mocked(getDoc).mockResolvedValue(mockDocSnapshot);

      // Execute
      const result = await fetchContent('collection', 'docId');

      // Verify
      expect(doc).toHaveBeenCalledWith(expect.anything(), 'collection', 'docId');
      expect(getDoc).toHaveBeenCalledWith(mockDocRef);
      expect(result).toEqual(mockData);
    });

    it('should fetch from fallback if primary not found', async () => {
      // Setup
      const mockFallbackData = { title: 'Fallback Title' };
      const mockPrimarySnapshot = {
        exists: () => false
      };
      const mockFallbackSnapshot = {
        exists: () => true,
        data: () => mockFallbackData
      };
      
      const mockPrimaryDocRef = { 
        id: 'docId',
        path: 'primary/docId'
      } as unknown as DocumentReference;
      
      const mockFallbackDocRef = {
        id: 'docId',
        path: 'fallback/docId'
      } as unknown as DocumentReference;
      
      vi.mocked(doc)
        .mockReturnValueOnce(mockPrimaryDocRef)
        .mockReturnValueOnce(mockFallbackDocRef);
      
      vi.mocked(getDoc)
        .mockResolvedValueOnce({
          exists: mockPrimarySnapshot.exists,
          data: vi.fn(),
          id: 'docId',
          metadata: {},
          get: vi.fn(),
          ref: mockPrimaryDocRef
        } as unknown as DocumentSnapshot)
        .mockResolvedValueOnce({
          exists: mockFallbackSnapshot.exists,
          data: mockFallbackSnapshot.data,
          id: 'docId',
          metadata: {},
          get: vi.fn(),
          ref: mockFallbackDocRef
        } as unknown as DocumentSnapshot);

      // Execute
      const result = await fetchContent('primary', 'docId', {
        fallbackCollection: 'fallback',
        fallbackDocId: 'docId'
      });

      // Verify
      expect(doc).toHaveBeenCalledWith(expect.anything(), 'primary', 'docId');
      expect(doc).toHaveBeenCalledWith(expect.anything(), 'fallback', 'docId');
      expect(result).toEqual(mockFallbackData);
    });

    it('should return default value if no content found', async () => {
      // Setup
      const defaultValue = { title: 'Fallback Title' };
      const mockPrimarySnapshot = { exists: () => false };
      const mockFallbackSnapshot = { exists: () => false };
      
      const mockPrimaryDocRef = { 
        id: 'docId',
        path: 'primary/docId'
      } as unknown as DocumentReference;
      
      const mockFallbackDocRef = {
        id: 'docId',
        path: 'fallback/docId'
      } as unknown as DocumentReference;
      
      vi.mocked(doc)
        .mockReturnValueOnce(mockPrimaryDocRef)
        .mockReturnValueOnce(mockFallbackDocRef);
      
      vi.mocked(getDoc)
        .mockResolvedValueOnce({
          exists: mockPrimarySnapshot.exists,
          data: vi.fn(),
          id: 'docId',
          metadata: {},
          get: vi.fn(),
          ref: mockPrimaryDocRef
        } as unknown as DocumentSnapshot)
        .mockResolvedValueOnce({
          exists: mockFallbackSnapshot.exists,
          data: vi.fn(),
          id: 'docId',
          metadata: {},
          get: vi.fn(),
          ref: mockFallbackDocRef
        } as unknown as DocumentSnapshot);

      // Execute
      const result = await fetchContent('primary', 'docId', {
        fallbackCollection: 'fallback',
        fallbackDocId: 'docId',
        defaultValue
      });

      // Verify
      expect(result).toEqual(defaultValue);
    });

    it('should apply transform function if provided', async () => {
      // Setup
      const mockData = { title: 'Test Title' };
      const mockSnapshot = {
        exists: () => true,
        data: () => mockData
      };
      
      const transform = (data: unknown) => {
        const typed = data as { title: string };
        return { title: `Transformed: ${typed.title}` };
      };
      
      const mockDocRef = { 
        id: 'docId',
        path: 'collection/docId'
      } as unknown as DocumentReference;
      
      vi.mocked(doc).mockReturnValue(mockDocRef);
      vi.mocked(getDoc).mockResolvedValue({
        exists: mockSnapshot.exists,
        data: mockSnapshot.data,
        id: 'docId',
        metadata: {},
        get: vi.fn(),
        ref: mockDocRef
      } as unknown as DocumentSnapshot);

      // Execute
      const result = await fetchContent('collection', 'docId', {
        transformData: transform
      });

      // Verify
      expect(result).toEqual({ title: 'Transformed: Test Title' });
    });

    it('should log and rethrow errors', async () => {
      // Setup - create a direct mock implementation
      const error = new Error('Test error');
      
      // Mock implementation that will definitely throw
      const mockFetchImplementation = async () => {
        logError(error, 'contentRepository.fetchContent', {
          context: { collection: 'collection', docId: 'docId' }
        });
        throw error;
      };
      
      // Replace the function temporarily
      const originalModule = await vi.importActual('./content-repository');
      vi.doMock('./content-repository', () => ({
        ...originalModule,
        fetchContent: mockFetchImplementation
      }));
      
      // Execute & verify
      try {
        await mockFetchImplementation();
        expect.fail('Should have thrown an error');
      } catch {
        expect(logError).toHaveBeenCalled();
      }
    });
  });

  describe('saveContent', () => {
    it('should save content to Firestore', async () => {
      // Setup
      const data = { title: 'Test Title' };
      const mockSnapshot = {
        exists: () => false // Simulate new document
      };
      
      const mockDocRef = { 
        id: 'docId',
        path: 'collection/docId'
      } as unknown as DocumentReference;
      
      // Clear all previous mock calls
      vi.clearAllMocks();
      
      // Set up mocks for this test
      vi.mocked(doc).mockReturnValueOnce(mockDocRef);
      vi.mocked(getDoc).mockResolvedValueOnce({
        exists: mockSnapshot.exists,
        data: vi.fn(),
        id: 'docId',
        metadata: {},
        get: vi.fn(),
        ref: mockDocRef
      } as unknown as DocumentSnapshot);
      vi.mocked(setDoc).mockResolvedValueOnce(undefined);

      // Execute
      await saveContent('collection', 'docId', data);

      // Verify
      expect(doc).toHaveBeenCalledWith(expect.anything(), 'collection', 'docId');
      expect(getDoc).toHaveBeenCalledWith(mockDocRef);
      expect(setDoc).toHaveBeenCalledWith(mockDocRef, data);
    });

    it('should log and rethrow errors', async () => {
      // Setup
      const error = new Error('Test error');
      const data = { title: 'Test Title' };
      
      const mockDocRef = { 
        id: 'docId',
        path: 'collection/docId'
      } as unknown as DocumentReference;
      
      // Clear all previous mock calls
      vi.clearAllMocks();
      
      vi.mocked(doc).mockReturnValueOnce(mockDocRef);
      vi.mocked(setDoc).mockRejectedValueOnce(error);

      // Execute & Verify
      await expect(saveContent('collection', 'docId', data)).rejects.toThrow(error);
      expect(logError).toHaveBeenCalledWith(
        error,
        'contentRepository.saveContent',
        expect.anything()
      );
    });
  });
});
