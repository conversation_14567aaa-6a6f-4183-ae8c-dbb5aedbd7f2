"use client";

import { useEffect, useState } from "react";
import { useAdminContent } from "@/hooks/use-admin-content";
import { useImageResolver } from "@/hooks/use-image-resolver";
import { useToast } from "@/hooks/use-toast";
import { useFormValidation } from "@/hooks/use-form-validation";
import { AboutContent, Exhibition } from "@/types/about";
import AdminFormLayout from "@/components/admin/AdminFormLayout";
import { FormField } from "@/components/ui/form-field";
import { EnhancedImageField } from "@/components/admin";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Trash2, Plus, Eye } from "lucide-react";
import PreviewPanel from "@/components/admin/PreviewPanel";
import PreviewToggle from "@/components/admin/PreviewToggle";
import tryToCatch from "try-to-catch";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

const defaultAboutContent: AboutContent = {
  statement: [""],
  education: [],
  exhibitions: {
    solo: [],
    group: [],
    juried: [],
    duo: [],
  },
  awards: [],
  collections: [],
  profileImage: "",
  professionalExperience: [],
  research: [],
  publications: [],
  participation: [],
  otherDetails: [],
};

interface EducationItem {
  heading: string;
  details: string;
}

export default function AdminAboutPage() {
  const {
    content,
    setContent,
    loading,
    saving,
    isDirty,
    saveContent,
    resetContent,
  } = useAdminContent<AboutContent>({
    previewCollection: "preview",
    previewDocId: "about",
    liveCollection: "live",
    liveDocId: "about",
    defaultValue: defaultAboutContent,
  });

  const { toast, showToast } = useToast();
  const { resolveImages } = useImageResolver();

  // Preview panel visibility state - start with preview hidden
  const [showPreview, setShowPreview] = useState(false);

  // Form validation
  const { errors, validateField, validateForm } =
    useFormValidation<AboutContent>({
      profileImage: [
        {
          validate: (value) => Boolean(value && value.trim().length > 0),
          message: "Profile image is required",
        },
      ],
      statement: [
        {
          validate: (value) =>
            value.length > 0 && value.some((p) => p.trim().length > 0),
          message: "At least one statement paragraph is required",
        },
      ],
    });

  // Validation for education items
  const [educationErrors, setEducationErrors] = useState<
    Record<number, Record<string, string>>
  >({});

  // Resolve profile image when content changes
  useEffect(() => {
    if (content?.profileImage) resolveImages([content.profileImage]);
  }, [content?.profileImage, resolveImages]);

  // Statement management
  const updateStatement = (index: number, value: string) => {
    const updatedStatement = content.statement.slice();

    updatedStatement[index] = value;
    setContent({
      ...content,
      statement: updatedStatement,
    });

    // Validate statement after update
    validateField("statement", updatedStatement);
  };

  const addStatementParagraph = () => {
    const updatedStatement = [...content.statement, ""];

    setContent({
      ...content,
      statement: updatedStatement,
    });

    // Validate statement after update
    validateField("statement", updatedStatement);
  };

  const removeStatementParagraph = (index: number) => {
    const updatedStatement = content.statement.filter((_, i) => i !== index);

    setContent({
      ...content,
      statement: updatedStatement,
    });

    // Validate statement after update
    validateField("statement", updatedStatement);
  };

  // Education management
  const updateEducation = (
    index: number,
    field: keyof EducationItem,
    value: string
  ) => {
    const updatedEducation = content.education.slice();

    updatedEducation[index] = {
      ...updatedEducation[index],
      [field]: value,
    };

    setContent({
      ...content,
      education: updatedEducation,
    });

    // Validate education item
    const isValid = value.trim().length > 0;

    setEducationErrors((prev) => ({
      ...prev,
      [index]: {
        ...prev[index],
        [field]: isValid
          ? ""
          : `${field.charAt(0).toUpperCase() + field.slice(1)} is required`,
      },
    }));
  };

  const addEducation = () => {
    setContent({
      ...content,
      education: [
        ...content.education,
        {
          heading: "",
          details: "",
        },
      ],
    });
  };

  const removeEducation = (index: number) => {
    setContent({
      ...content,
      education: content.education.filter((_, i) => i !== index),
    });

    // Remove validation errors for this education item
    setEducationErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[index];
      return newErrors;
    });
  };

  // Exhibition management
  const updateExhibition = (
    category: keyof AboutContent["exhibitions"],
    index: number,
    field: keyof Exhibition,
    value: string
  ) => {
    const updatedCategory = content.exhibitions[category].slice();

    updatedCategory[index] = {
      ...updatedCategory[index],
      [field]: value,
    };

    setContent({
      ...content,
      exhibitions: {
        ...content.exhibitions,
        [category]: updatedCategory,
      },
    });
  };

  const addExhibition = (category: keyof AboutContent["exhibitions"]) => {
    setContent({
      ...content,
      exhibitions: {
        ...content.exhibitions,
        [category]: [
          ...content.exhibitions[category],
          {
            year: "",
            details: "",
          },
        ],
      },
    });
  };

  const removeExhibition = (
    category: keyof AboutContent["exhibitions"],
    index: number
  ) => {
    setContent({
      ...content,
      exhibitions: {
        ...content.exhibitions,
        [category]: content.exhibitions[category].filter((_, i) => i !== index),
      },
    });
  };


  const handleSave = async () => {
    // Validate main form fields
    const isFormValid = validateForm(content);

    // Validate education items
    let isEducationValid = true;
    const newEducationErrors: Record<number, Record<string, string>> = {};

    for (const [index, edu] of content.education.entries()) {
      const itemErrors: Record<string, string> = {};

      if (!edu.heading || !edu.heading.trim().length) {
        itemErrors.heading = "Heading is required";
        isEducationValid = false;
      }

      if (!edu.details || !edu.details.trim().length) {
        itemErrors.details = "Detail is required";
        isEducationValid = false;
      }

      if (Object.keys(itemErrors).length > 0)
        newEducationErrors[index] = itemErrors;
    }

    setEducationErrors(newEducationErrors);

    if (!isFormValid || !isEducationValid) {
      showToast("Please fix the validation errors before saving", {
        variant: "error",
      });
      return;
    }

    await tryToCatch(saveContent);
  };

  return (
    <AdminFormLayout
      title="Edit About Page"
      description="Manage the content displayed on the about page."
      isLoading={loading}
      isSaving={saving}
      isDirty={isDirty}
      onSave={handleSave}
      onReset={resetContent}
      previewDocId="about"
    >
      <div className="flex justify-end mb-4">
        <PreviewToggle
          isVisible={showPreview}
          onToggle={() => setShowPreview(!showPreview)}
        />
      </div>

      <div
        className={`grid grid-cols-1 ${
          showPreview ? "md:grid-cols-3" : "md:grid-cols-1"
        } gap-6`}
      >
        <div className={showPreview ? "md:col-span-2" : "md:col-span-1"}>
          <Tabs defaultValue="profile" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="profile">Profile</TabsTrigger>
              <TabsTrigger value="statement">Statement</TabsTrigger>
              <TabsTrigger value="education">Education</TabsTrigger>
              <TabsTrigger value="experiences">Experiences</TabsTrigger>
              <TabsTrigger value="exhibitions">Exhibitions</TabsTrigger>
            </TabsList>

            <TabsContent value="profile" className="mt-4">
              <div className="space-y-4 border rounded-md p-6">
                <h3 className="text-lg font-medium">Profile Image</h3>
                <FormField
                  label="Profile Image"
                  required
                  error={errors.profileImage}
                >
                  <EnhancedImageField
                    label=""
                    imagePath={content.profileImage}
                    onChange={(path) => {
                      setContent({
                        ...content,
                        profileImage: path,
                      });
                      validateField("profileImage", path);
                    }}
                    aspectRatio="square"
                    height={200}
                    className={
                      errors.profileImage
                        ? "border border-red-500 rounded-md"
                        : ""
                    }
                    required={true}
                  />
                </FormField>
              </div>
            </TabsContent>

            <TabsContent value="statement" className="mt-4">
              <div className="space-y-4 border rounded-md p-6">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Artist Statement</h3>
                  <Button
                    type="button"
                    size="sm"
                    variant="outline"
                    onClick={addStatementParagraph}
                  >
                    <Plus className="h-4 w-4 mr-1" /> Add Paragraph
                  </Button>
                </div>
                {errors.statement && (
                  <p className="text-sm text-red-500">{errors.statement}</p>
                )}
                {content.statement.map((paragraph, index) => (
                  <div key={index} className="flex gap-2">
                    <FormField
                      label={`Paragraph ${index + 1}`}
                      className="flex-1"
                    >
                      <Textarea
                        value={paragraph}
                        onChange={(e) => updateStatement(index, e.target.value)}
                        placeholder={`Paragraph ${index + 1}`}
                        rows={3}
                      />
                    </FormField>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => removeStatementParagraph(index)}
                      disabled={content.statement.length <= 1}
                      className="mt-7"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="education" className="mt-4">
              <div className="space-y-4 border rounded-md p-6">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Education</h3>
                  <Button
                    type="button"
                    size="sm"
                    variant="outline"
                    onClick={addEducation}
                  >
                    <Plus className="h-4 w-4 mr-1" /> Add Education
                  </Button>
                </div>
                {content.education.map((edu, index) => (
                  <div key={index} className="flex gap-2">
                    <FormField
                      label="Program"
                      error={educationErrors[index]?.heading}
                      required
                      className="w-1/3"
                    >
                      <Input
                        value={edu.heading}
                        onChange={(e) =>
                          updateEducation(index, "heading", e.target.value)
                        }
                        placeholder="Program"
                        className={
                          educationErrors[index]?.heading
                            ? "border-red-500"
                            : ""
                        }
                      />
                    </FormField>
                    <FormField
                      label="Details"
                      error={educationErrors[index]?.details}
                      required
                      className="flex-1"
                    >
                      <Input
                        value={edu.details}
                        onChange={(e) =>
                          updateEducation(index, "details", e.target.value)
                        }
                        placeholder="Details"
                        className={
                          educationErrors[index]?.details
                            ? "border-red-500"
                            : ""
                        }
                      />
                    </FormField>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => removeEducation(index)}
                      className="mt-7"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                {content.education.length === 0 && (
                  <div className="text-center py-4 border rounded-md bg-gray-50">
                    <p className="text-gray-500">
                      No education entries added yet
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={addEducation}
                    >
                      <Plus className="h-4 w-4 mr-1" /> Add Education
                    </Button>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="exhibitions" className="mt-4">
              <div className="border rounded-md p-6">
                <h3 className="text-lg font-medium mb-4">Exhibitions</h3>

                <Tabs defaultValue="solo" className="w-full">
                  <TabsList className="grid w-full grid-cols-4 mb-4">
                    <TabsTrigger value="solo">Solo Exhibitions</TabsTrigger>
                    <TabsTrigger value="duo">Duo Exhibitions</TabsTrigger>
                    <TabsTrigger value="group">Group Exhibitions</TabsTrigger>
                    <TabsTrigger value="juried">Juried Exhibitions</TabsTrigger>
                  </TabsList>

                  {(
                    [
                      ["solo", "Solo"],
                      ["group", "Group"],
                      ["juried", "Juried"],
                      ["duo", "Duo"],
                    ] as const
                  ).map(([category, label]) => (
                    <TabsContent
                      key={category}
                      value={category}
                      className="mt-4"
                    >
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <h4 className="font-medium text-elegant-text">
                            {label} Exhibitions
                          </h4>
                          <Button
                            type="button"
                            size="sm"
                            variant="outline"
                            onClick={() => addExhibition(category)}
                          >
                            <Plus className="h-4 w-4 mr-1" /> Add Exhibition
                          </Button>
                        </div>
                        {content.exhibitions[category].map(
                          (exhibition, index) => (
                            <div key={index} className="flex gap-2">
                              <FormField label="Year" className="w-24">
                                <Input
                                  value={exhibition.year}
                                  onChange={(e) =>
                                    updateExhibition(
                                      category,
                                      index,
                                      "year",
                                      e.target.value
                                    )
                                  }
                                  placeholder="Year"
                                />
                              </FormField>
                              <FormField label="Details" className="flex-1">
                                <Input
                                  value={exhibition.details}
                                  onChange={(e) =>
                                    updateExhibition(
                                      category,
                                      index,
                                      "details",
                                      e.target.value
                                    )
                                  }
                                  placeholder="Details"
                                />
                              </FormField>
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                onClick={() =>
                                  removeExhibition(category, index)
                                }
                                className="mt-7"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          )
                        )}
                        {content.exhibitions[category].length === 0 && (
                          <div className="text-center py-4 border rounded-md bg-gray-50">
                            <p className="text-gray-500">
                              No {category} exhibitions added yet
                            </p>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              className="mt-2"
                              onClick={() => addExhibition(category)}
                            >
                              <Plus className="h-4 w-4 mr-1" /> Add Exhibition
                            </Button>
                          </div>
                        )}
                      </div>
                    </TabsContent>
                  ))}
                </Tabs>
              </div>
            </TabsContent>

            <TabsContent value="experiences" className="mt-4">
              <div className="border rounded-md p-6">
                <h3 className="text-lg font-medium mb-4">Experiences</h3>

                <Tabs defaultValue="professional" className="w-full">
                  <TabsList className="grid w-full grid-cols-5 mb-4">
                    <TabsTrigger value="professional">Professional</TabsTrigger>
                    <TabsTrigger value="research">Research</TabsTrigger>
                    <TabsTrigger value="participation">Participation</TabsTrigger>
                    <TabsTrigger value="publication">Publication</TabsTrigger>
                    <TabsTrigger value="others">Others</TabsTrigger>
                  </TabsList>

                  {/* Professional Experience Tab */}
                  <TabsContent value="professional" className="mt-4">
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <h4 className="font-medium text-elegant-text">Professional Experience</h4>
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setContent({
                              ...content,
                              professionalExperience: [
                                ...content.professionalExperience || [],
                                {
                                  years: "",
                                  details: ""
                                }
                              ]
                            });
                          }}
                        >
                          <Plus className="h-4 w-4 mr-1" /> Add Experience
                        </Button>
                      </div>
                      {content.professionalExperience?.map((exp, index) => (
                        <div key={index} className="flex gap-2">
                          <FormField label="Years" className="w-24">
                            <Input
                              value={exp.years}
                              onChange={(e) => {
                                const updated = [...(content.professionalExperience || [])];
                                updated[index] = { ...updated[index], years: e.target.value };
                                setContent({
                                  ...content,
                                  professionalExperience: updated
                                });
                              }}
                              placeholder="Years"
                            />
                          </FormField>
                          <FormField label="Details" className="flex-1">
                            <Input
                              value={exp.details || ""}
                              onChange={(e) => {
                                const updated = [...(content.professionalExperience || [])];
                                updated[index] = { ...updated[index], details: e.target.value };
                                setContent({
                                  ...content,
                                  professionalExperience: updated
                                });
                              }}
                              placeholder="Details"
                            />
                          </FormField>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setContent({
                                ...content,
                                professionalExperience: content.professionalExperience?.filter((_, i) => i !== index)
                              });
                            }}
                            className="mt-7"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      {(!content.professionalExperience || content.professionalExperience.length === 0) && (
                        <div className="text-center py-4 border rounded-md bg-gray-50">
                          <p className="text-gray-500">No professional experience added yet</p>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            className="mt-2"
                            onClick={() => {
                              setContent({
                                ...content,
                                professionalExperience: [
                                  {
                                    years: "",
                                    details: ""
                                  }
                                ]
                              });
                            }}
                          >
                            <Plus className="h-4 w-4 mr-1" /> Add Experience
                          </Button>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  {/* Research Tab */}
                  <TabsContent value="research" className="mt-4">
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <h4 className="font-medium text-elegant-text">Research Experience</h4>
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setContent({
                              ...content,
                              research: [
                                ...content.research || [],
                                {
                                  years: "",
                                  details: ""
                                }
                              ]
                            });
                          }}
                        >
                          <Plus className="h-4 w-4 mr-1" /> Add Research
                        </Button>
                      </div>
                      {content.research?.map((item, index) => (
                        <div key={index} className="flex gap-2">
                          <FormField label="Years" className="w-24">
                            <Input
                              value={item.years}
                              onChange={(e) => {
                                const updated = [...(content.research || [])];
                                updated[index] = { ...updated[index], years: e.target.value };
                                setContent({
                                  ...content,
                                  research: updated
                                });
                              }}
                              placeholder="Years"
                            />
                          </FormField>
                          <FormField label="Details" className="flex-1">
                            <Input
                              value={item.details || ""}
                              onChange={(e) => {
                                const updated = [...(content.research || [])];
                                updated[index] = { ...updated[index], details: e.target.value };
                                setContent({
                                  ...content,
                                  research: updated
                                });
                              }}
                              placeholder="details"
                            />
                          </FormField>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setContent({
                                ...content,
                                research: content.research?.filter((_, i) => i !== index)
                              });
                            }}
                            className="mt-7"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      {(!content.research || content.research.length === 0) && (
                        <div className="text-center py-4 border rounded-md bg-gray-50">
                          <p className="text-gray-500">No research experience added yet</p>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            className="mt-2"
                            onClick={() => {
                              setContent({
                                ...content,
                                research: [
                                  {
                                    years: "",
                                    details: ""
                                  }
                                ]
                              });
                            }}
                          >
                            <Plus className="h-4 w-4 mr-1" /> Add Research
                          </Button>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  {/* Participation Tab */}
                  <TabsContent value="participation" className="mt-4">
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <h4 className="font-medium text-elegant-text">Participation</h4>
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setContent({
                              ...content,
                              participation: [
                                ...content.participation || [],
                                {
                                  year: "",
                                  details: ""
                                }
                              ]
                            });
                          }}
                        >
                          <Plus className="h-4 w-4 mr-1" /> Add Participation
                        </Button>
                      </div>
                      {content.participation?.map((item, index) => (
                        <div key={index} className="flex gap-2">
                          <FormField label="Year" className="w-24">
                            <Input
                              value={item.year}
                              onChange={(e) => {
                                const updated = [...(content.participation || [])];
                                updated[index] = { ...updated[index], year: e.target.value };
                                setContent({
                                  ...content,
                                  participation: updated
                                });
                              }}
                              placeholder="Year"
                            />
                          </FormField>
                          <FormField label="Details" className="flex-1">
                            <Input
                              value={item.details}
                              onChange={(e) => {
                                const updated = [...(content.participation || [])];
                                updated[index] = { ...updated[index], details: e.target.value };
                                setContent({
                                  ...content,
                                  participation: updated
                                });
                              }}
                              placeholder="Details"
                            />
                          </FormField>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setContent({
                                ...content,
                                participation: content.participation?.filter((_, i) => i !== index)
                              });
                            }}
                            className="mt-7"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      {(!content.participation || content.participation.length === 0) && (
                        <div className="text-center py-4 border rounded-md bg-gray-50">
                          <p className="text-gray-500">No participation added yet</p>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            className="mt-2"
                            onClick={() => {
                              setContent({
                                ...content,
                                participation: [
                                  {
                                    year: "",
                                    details: ""
                                  }
                                ]
                              });
                            }}
                          >
                            <Plus className="h-4 w-4 mr-1" /> Add Participation
                          </Button>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  {/* Publication Tab */}
                  <TabsContent value="publication" className="mt-4">
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <h4 className="font-medium text-elegant-text">Publications</h4>
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setContent({
                              ...content,
                              publications: [
                                ...content.publications || [],
                                {
                                  year: "",
                                  details: ""
                                }
                              ]
                            });
                          }}
                        >
                          <Plus className="h-4 w-4 mr-1" /> Add Publication
                        </Button>
                      </div>
                      {content.publications?.map((item, index) => (
                        <div key={index} className="flex gap-2">
                          <FormField label="Year" className="w-24">
                            <Input
                              value={item.year}
                              onChange={(e) => {
                                const updated = [...(content.publications || [])];
                                updated[index] = { ...updated[index], year: e.target.value };
                                setContent({
                                  ...content,
                                  publications: updated
                                });
                              }}
                              placeholder="Year"
                            />
                          </FormField>
                          <FormField label="Details" className="flex-1">
                            <Input
                              value={item.details}
                              onChange={(e) => {
                                const updated = [...(content.publications || [])];
                                updated[index] = { ...updated[index], details: e.target.value };
                                setContent({
                                  ...content,
                                  publications: updated
                                });
                              }}
                              placeholder="Details"
                            />
                          </FormField>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setContent({
                                ...content,
                                publications: content.publications?.filter((_, i) => i !== index)
                              });
                            }}
                            className="mt-7"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      {(!content.publications || content.publications.length === 0) && (
                        <div className="text-center py-4 border rounded-md bg-gray-50">
                          <p className="text-gray-500">No publications added yet</p>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            className="mt-2"
                            onClick={() => {
                              setContent({
                                ...content,
                                publications: [
                                  {
                                    year: "",
                                    details: ""
                                  }
                                ]
                              });
                            }}
                          >
                            <Plus className="h-4 w-4 mr-1" /> Add Publication
                          </Button>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  {/* Others Tab */}
                  <TabsContent value="others" className="mt-4">
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <h4 className="font-medium text-elegant-text">Other Experiences</h4>
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setContent({
                              ...content,
                              otherDetails: [
                                ...content.otherDetails || [],
                                {
                                  years: "",
                                  details: ""
                                }
                              ]
                            });
                          }}
                        >
                          <Plus className="h-4 w-4 mr-1" /> Add Other Experience
                        </Button>
                      </div>
                      {content.otherDetails?.map((item, index) => (
                        <div key={index} className="flex gap-2">
                          <FormField label="Years" className="w-24">
                            <Input
                              value={item.years}
                              onChange={(e) => {
                                const updated = [...(content.otherDetails || [])];
                                updated[index] = { ...updated[index], years: e.target.value };
                                setContent({
                                  ...content,
                                  otherDetails: updated
                                });
                              }}
                              placeholder="Years"
                            />
                          </FormField>
                          <FormField label="Details" className="flex-1">
                            <Input
                              value={item.details || ""}
                              onChange={(e) => {
                                const updated = [...(content.otherDetails || [])];
                                updated[index] = { ...updated[index], details: e.target.value };
                                setContent({
                                  ...content,
                                  otherDetails: updated
                                });
                              }}
                              placeholder="Details"
                            />
                          </FormField>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setContent({
                                ...content,
                                otherDetails: content.otherDetails?.filter((_, i) => i !== index)
                              });
                            }}
                            className="mt-7"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      {(!content.otherDetails || content.otherDetails.length === 0) && (
                        <div className="text-center py-4 border rounded-md bg-gray-50">
                          <p className="text-gray-500">No other experiences added yet</p>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            className="mt-2"
                            onClick={() => {
                              setContent({
                                ...content,
                                otherDetails: [
                                  {
                                    years: "",
                                    details: ""
                                  }
                                ]
                              });
                            }}
                          >
                            <Plus className="h-4 w-4 mr-1" /> Add Other Experience
                          </Button>
                        </div>
                      )}
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* Live Preview Panel */}
        {showPreview && (
          <div className="md:col-span-1">
            <div className="sticky top-24">
              <div className="flex items-center mb-2">
                <Eye className="h-4 w-4 mr-1 text-elegant-accent" />
                <h3 className="text-sm font-medium text-elegant-text">
                  Live Preview
                </h3>
              </div>
              <PreviewPanel content={content} type="about" />
              <p className="text-xs text-elegant-text/60 mt-2 italic">
                This preview shows how your content will appear on the public
                site
              </p>
            </div>
          </div>
        )}
      </div>
      {toast}
    </AdminFormLayout>
  );
}
