{"rules": {"gitignore": "off", "esm/group-imports-by-source": "off", "remove-useless-variables/remove": "on", "nodejs/add-missing-strict-mode": "off", "remove-unused-variables": "on", "remove-console": "off", "conditions/apply-consistent-blocks": "off", "remove-useless-push": "off", "convert-array-copy-to-slice": "off", "conditions/remove-zero": "off", "new/remove-useless": "off", "convert-template-to-string": "off", "typescript/convert-generic-to-shorthand": "off", "remove-useless-variables/declaration": "off", "nodejs/declare": "off", "conditions/merge-if-statements": "off", "promises/remove-useless-async": "off", "optional-chaining/convert-logical-to-optional": "off", "remove-useless-functions": "off", "try-catch/async": "off", "apply-arrow": "off", "nodejs/add-node-prefix": "off", "esm/merge-duplicate-imports-join": "on", "remove-useless-escape": "off", "esm/sort-imports-by-specifiers": "off", "esm/merge-declaration-with-export": "off", "return/remove-useless": "on", "apply-shorthand-properties": "off", "math/apply-numeric-separators": "off", "types/convert-typeof-to-is-type": "off", "for-of/for-each": "off", "extract-sequence-expressions": "off", "nodejs/convert-exports-to-module-exports": "off", "apply-dot-notation": "off", "assignment/convert-to-declaration": "off", "convert-quotes-to-backticks": "off", "remove-iife": "off", "convert-index-of-to-includes": "off", "conditions/convert-equal-to-strict-equal": "off", "maybe/noop": "off", "apply-destructuring/object": "off", "apply-template-literals": "off", "declare-before-reference": "off", "apply-at": "off", "remove-useless-arguments/unused": "off", "split-variable-declarations": "off", "regexp/optimize": "off", "logical-expressions/remove-duplicates": "off", "conditions/apply-comparison-order": "off", "apply-destructuring/array": "off", "extract-keywords-from-variables": "off", "remove-unreferenced-variables": "off", "remove-unused-expressions": "off", "remove-useless-variables/rename": "off", "remove-useless-map": "off", "remove-useless-constructor": "off", "remove-useless-operand": "off", "split-call-with-destructuring": "off", "convert-object-assign-to-merge-spread": "off", "math/apply-exponentiation": "off", "promises/convert-reject-to-throw": "off", "promises/add-missing-await": "off", "promises/remove-useless-await": "off", "regexp/apply-literal-notation": "off", "try-catch/sync": "off", "logical-expressions/simplify": "off", "types/apply-is-array": "off", "types/remove-double-negations": "off", "conditions/reverse-condition": "off"}, "ignore": ["process.env", "**/*.test.ts", "next.config.js", "node_modules/", "/.next/", "/out/", "/build", "npm-debug.log*", "yarn-debug.log*", "yarn-error.log*", ".pnpm-debug.log*", ".env*", "!.env.local.example", ".firebase/hosting.b3V0.cache", "firebase-debug.log", "firestore-debug.log", "functions/firebase-data", "/app/gallery/[slug]/slugs.json", "*.tsbuildinfo", "next-env.d.ts"]}