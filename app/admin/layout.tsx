"use client";

import { ReactNode } from "react";
import { useAdminAuth } from "@/lib/useAdminAuth";
import PublishButton from "@/components/PublishButton";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import {
  Loader2,
  LogOut,
  Image as ImageIcon,
  LayoutDashboard,
  Home,
  Mail,
  Calendar,
  Menu,
  Palette,
} from "lucide-react";

export default function AdminLayout({ children }: { children: ReactNode }) {
  const { user, loading, signIn, signOut } = useAdminAuth();

  if (loading)
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500 mr-2" />
        <span className="text-gray-500">Loading...</span>
      </div>
    );

  if (!user)
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <h1 className="text-2xl font-bold mb-6 text-center">Admin Login</h1>
          <p className="mb-6 text-gray-600 text-center">
            Sign in with your admin account to manage content
          </p>
          <Button onClick={signIn} className="w-full">
            Sign in as Admin
          </Button>
        </div>
      </div>
    );

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="flex justify-between items-center px-8 py-4 bg-white shadow sticky top-0 z-10">
        <Link
          href="/admin"
          className="text-xl font-semibold hover:text-blue-600 transition-colors"
        >
          Admin Dashboard
        </Link>
        <div className="flex items-center space-x-3">
          <PublishButton />
          <Button
            onClick={signOut}
            variant="outline"
            size="sm"
            className="text-red-500 border-red-200 hover:bg-red-50"
          >
            <LogOut className="h-4 w-4 mr-1" />
            Sign Out
          </Button>
        </div>
      </header>
      <div className="flex min-h-[calc(100vh-73px)]">
        <nav className="w-64 bg-white shadow-sm p-4 overflow-y-auto sticky top-[73px]">
          <ul className="space-y-2">
            <li>
              <Link
                href="/admin"
                className="flex items-center p-2 rounded-md hover:bg-gray-100"
              >
                <LayoutDashboard className="h-4 w-4 mr-2" />
                <span>Dashboard</span>
              </Link>
            </li>
            <li className="py-2">
              <div className="h-px bg-gray-400" />
            </li>
            <li>
              <Link
                href="/admin/newsletter"
                className="flex items-center p-2 rounded-md hover:bg-gray-100"
              >
                <Mail className="h-4 w-4 mr-2" />
                <span>Newsletter</span>
              </Link>
            </li>
            <li>
              <Link
                href="/admin/artworks"
                className="flex items-center p-2 rounded-md hover:bg-gray-100"
              >
                <Palette className="h-4 w-4 mr-2" />
                <span>Artwork Repository</span>
              </Link>
            </li>
            <li>
              <Link
                href="/admin/versions"
                className="flex items-center p-2 rounded-md hover:bg-gray-100"
              >
                <Calendar className="h-4 w-4 mr-2" />
                <span>Version Management</span>
              </Link>
            </li>

            <li className="py-2">
              <div className="h-px bg-gray-400" />
            </li>
            <li>
              <Link
                href="/admin/home"
                className="flex items-center p-2 rounded-md hover:bg-gray-100"
              >
                <Home className="h-4 w-4 mr-2" />
                <span>Home</span>
              </Link>
            </li>
            <li>
              <Link
                href="/admin/about"
                className="flex items-center p-2 rounded-md hover:bg-gray-100"
              >
                <Menu className="h-4 w-4 mr-2" />
                <span>About</span>
              </Link>
            </li>
            <li>
              <Link
                href="/admin/gallery"
                className="flex items-center p-2 rounded-md hover:bg-gray-100"
              >
                <ImageIcon className="h-4 w-4 mr-2" />
                <span>Gallery</span>
              </Link>
            </li>
            <li>
              <Link
                href="/admin/events"
                className="flex items-center p-2 rounded-md hover:bg-gray-100"
              >
                <Calendar className="h-4 w-4 mr-2" />
                <span>Events</span>
              </Link>
            </li>
            <li>
              <Link
                href="/admin/contact"
                className="flex items-center p-2 rounded-md hover:bg-gray-100"
              >
                <Mail className="h-4 w-4 mr-2" />
                <span>Contact</span>
              </Link>
            </li>
          </ul>
        </nav>
        <main className="flex-1 p-8 bg-gray-50">{children}</main>
      </div>
    </div>
  );
}
