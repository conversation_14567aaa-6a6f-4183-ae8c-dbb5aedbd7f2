import React from 'react';
import Image from 'next/image';

interface FeaturedArtworkCardProps {
    image: string;
    title: string;
    description: string;
    year: string;
    size: string;
    onClick?: () => void;
}

export function FeaturedArtworkCard({image, title, description, year, size, onClick}: FeaturedArtworkCardProps) {
    return (
        <div
            className="group relative overflow-hidden cursor-pointer transition-all duration-500"
            onClick={onClick}
        >
            <div className="relative h-64 flex items-center justify-center overflow-hidden image-zoom-container">
                <Image
                    src={image || '/placeholder.svg'}
                    alt={title}
                    width={500}
                    height={500}
                    className="object-contain max-w-full max-h-full image-zoom"
                    sizes="(max-width: 768px) 100vw, 33vw"
                />
                <div className="absolute inset-0 bg-black/10 transition-opacity duration-500 group-hover:bg-black/20"/>
            </div>
            <div className="absolute inset-0 flex flex-col justify-end p-6 bg-gradient-to-t from-black/70 via-black/40 to-transparent text-center opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                <h2 className="text-white text-lg md:text-xl font-cormorant font-medium mb-2 line-clamp-1">
                    {title}
                </h2>
                <p className="text-white/90 text-xs md:text-sm mb-3 line-clamp-2 font-montserrat">
                    {description}
                </p>
                <div className="text-white/80 text-[10px] md:text-xs flex justify-center gap-4 font-montserrat">
                    <span>{year}</span>
                    <span className="w-1 h-1 rounded-full bg-white/50 self-center"></span>
                    <span>{size}</span>
                </div>
            </div>
            <div className="absolute bottom-0 left-0 right-0 p-4 bg-modernArt-cardBackground/90 backdrop-blur-sm transform translate-y-full group-hover:translate-y-0 transition-transform duration-500">
                <h3 className="font-cormorant text-modernArt-cardForeground text-lg font-medium">{title}</h3>
            </div>
        </div>
    );
}
