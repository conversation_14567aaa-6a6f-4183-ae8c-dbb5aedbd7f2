import React from "react";
import { describe, it, expect } from "vitest";
import { render, screen } from "@testing-library/react";
import { Badge, badgeVariants } from "./badge";

describe("Badge", () => {
  it("renders with default props", () => {
    render(<Badge data-testid="badge">Test Badge</Badge>);
    const badge = screen.getByTestId("badge");
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveTextContent("Test Badge");
    expect(badge).toHaveClass("bg-primary");
    expect(badge).toHaveClass("text-primary-foreground");
  });

  it("renders with secondary variant", () => {
    render(<Badge data-testid="badge" variant="secondary">Secondary Badge</Badge>);
    const badge = screen.getByTestId("badge");
    expect(badge).toHaveClass("bg-secondary");
    expect(badge).toHaveClass("text-secondary-foreground");
  });

  it("renders with destructive variant", () => {
    render(<Badge data-testid="badge" variant="destructive">Destructive Badge</Badge>);
    const badge = screen.getByTestId("badge");
    expect(badge).toHaveClass("bg-destructive");
    expect(badge).toHaveClass("text-destructive-foreground");
  });

  it("renders with outline variant", () => {
    render(<Badge data-testid="badge" variant="outline">Outline Badge</Badge>);
    const badge = screen.getByTestId("badge");
    expect(badge).toHaveClass("text-foreground");
    expect(badge).not.toHaveClass("bg-primary");
  });

  it("applies custom className", () => {
    render(<Badge data-testid="badge" className="custom-class">Custom Badge</Badge>);
    const badge = screen.getByTestId("badge");
    expect(badge).toHaveClass("custom-class");
  });

  it("passes additional props to the div element", () => {
    render(<Badge data-testid="badge" aria-label="Badge Label">Props Badge</Badge>);
    const badge = screen.getByTestId("badge");
    expect(badge).toHaveAttribute("aria-label", "Badge Label");
  });

  it("badgeVariants function generates correct class names", () => {
    const defaultClasses = badgeVariants();
    expect(defaultClasses).toContain("bg-primary");
    
    const secondaryClasses = badgeVariants({ variant: "secondary" });
    expect(secondaryClasses).toContain("bg-secondary");
    
    const destructiveClasses = badgeVariants({ variant: "destructive" });
    expect(destructiveClasses).toContain("bg-destructive");
    
    const outlineClasses = badgeVariants({ variant: "outline" });
    expect(outlineClasses).toContain("text-foreground");
  });
});