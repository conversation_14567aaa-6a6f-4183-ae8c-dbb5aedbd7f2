import 'dotenv/config';
import { config } from 'dotenv';
import fs from 'fs/promises';
import path from 'path';
import { initializeApp, applicationDefault } from 'firebase-admin/app';
import { getStorage } from 'firebase-admin/storage';

// Load environment variables
config({
  path: '.env.local',
});

// Use emulator if NEXT_PUBLIC_FIREBASE_USE_EMULATOR is set to true
if (process.env.NEXT_PUBLIC_FIREBASE_USE_EMULATOR === 'true') {
  process.env.FIREBASE_STORAGE_EMULATOR_HOST = process.env.NEXT_PUBLIC_FIREBASE_STORAGE_EMULATOR_HOST || 'localhost:9199';
}

// Ensure the Admin SDK uses the same project ID as the emulator/app
process.env.GCLOUD_PROJECT = process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'belagallery-9e01b';
process.env.FIREBASE_CONFIG = JSON.stringify({
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'belagallery-9e01b',
});

// Initialize Firebase Admin SDK
initializeApp({
  credential: applicationDefault(),
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || 'belagallery-9e01b.firebasestorage.app',
});

const storage = getStorage();

async function exportFirebaseImages() {
  try {
    console.log('🖼️  Starting Firebase Storage images export...');
    
    const bucket = storage.bucket();
    
    // Get output directory from command line argument or default to 'public'
    const outputDir = process.argv.includes('--output')
      ? process.argv[process.argv.indexOf('--output') + 1]
      : '.';
    
    // Export all images from bela-gallery folder (not just images subfolder)
    const [files] = await bucket.getFiles({
      prefix: 'bela-gallery/',
    });
    
    console.log(`🔍 Found ${files.length} files to process`);
    
    let exportedCount = 0;
    
    // Process each file
    for (const file of files) {
      try {
        // Skip directories (files ending with /)
        if (file.name.endsWith('/')) {
          continue;
        }
        
        // Only process image files
        const isImage = /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(file.name);
        if (!isImage) {
          continue;
        }
        
        // Download the file
        const [buffer] = await file.download();
        
        // Create local path: images/2013/08/filename.jpg
        // file.name is like: "bela-gallery/images/2013/08/filename.jpg"
        const relativePath = file.name.replace('bela-gallery/', '');
        const localPath = path.join(process.cwd(), outputDir, relativePath);
        
        // Create directory if it doesn't exist
        await fs.mkdir(path.dirname(localPath), { recursive: true });
        
        // Write the image file
        await fs.writeFile(localPath, buffer);
        
        console.log(`✅ Exported: ${relativePath}`);
        exportedCount++;
      } catch (imageError) {
        console.error(`❌ Failed to export ${file.name}:`, imageError);
      }
    }
    
    console.log(`🎉 Firebase Storage images export completed! Exported ${exportedCount} images.`);
  } catch (error) {
    console.error('❌ Firebase Storage images export failed:', error);
    process.exit(1);
  }
}

// Run the export
exportFirebaseImages().catch(console.error);