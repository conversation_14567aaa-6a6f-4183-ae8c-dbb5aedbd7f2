"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { ArrowUp, Save, RefreshCw, Trash2, Loader2 } from "lucide-react";

interface FloatingActionBarProps {
  isDirty: boolean;
  isSaving: boolean;
  isRemovingPreview?: boolean;
  onSave: () => void;
  onReset: () => void;
  onRemovePreview?: () => void;
  onSaveComplete?: () => void;
}

export function FloatingActionBar({
  isDirty,
  isSaving,
  isRemovingPreview = false,
  onSave,
  onReset,
  onRemovePreview,
  onSaveComplete,
}: FloatingActionBarProps) {
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 z-40 p-4 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-t shadow-lg">
      <div className="max-w-4xl mx-auto flex items-center justify-between">
        <Button
          variant="ghost"
          size="sm"
          onClick={scrollToTop}
          className="text-gray-500"
        >
          <ArrowUp className="h-4 w-4 mr-1" />
          Top
        </Button>
        <div className="flex items-center gap-2">
          {isDirty && (
            <span className="text-sm text-amber-600 mr-2">Unsaved changes</span>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={onReset}
            disabled={!isDirty || isSaving}
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Reset
          </Button>
          {onRemovePreview && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRemovePreview}
              disabled={isRemovingPreview}
              className="text-red-500 border-red-200 hover:bg-red-50 hover:text-red-600"
            >
              {isRemovingPreview ? (
                <>
                  <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                  Removing...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-1" />
                  Remove Preview
                </>
              )}
            </Button>
          )}
          <Button
            onClick={() => {
              onSave();
              // Call onSaveComplete after a short delay to allow save to complete
              if (onSaveComplete) {
                setTimeout(onSaveComplete, 1000);
              }
            }}
            disabled={!isDirty || isSaving}
            size="sm"
          >
            <Save className="h-4 w-4 mr-1" />
            {isSaving ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </div>
    </div>
  );
}
