# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules

# next.js
/.next/
/out/
/out-dynamic/
# production
/build

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env*
!.env.local.example

# firebase
/.firebase/
firebase-debug.log
firestore-debug.log
#Firebase emulator
functions/firebase-data

# slugs
/app/gallery/\[slug]/slugs.json

# typescript
*.tsbuildinfo
next-env.d.ts
.idea
*.swp
yarn-error.log

# test coverage and output
/coverage/
/test/output/

# playwright
/playwright-report/
/playwright/.cache/
/test-results/

action/.secrets
