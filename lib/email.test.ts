import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createNewsletterEmailHtml } from './email';

// Mock environment variables
const mockEnv = {
  NEXT_PUBLIC_STATIC_BUILD: 'false',
  NEXT_PUBLIC_BASE_URL: 'https://example.com',
  NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: 'test-bucket.firebasestorage.app',
  NEXT_PUBLIC_STORAGE_BASE_PATH: 'test-gallery',
  NEXT_PUBLIC_FIREBASE_USE_EMULATOR: 'false'
};

// Mock fetch globally
global.fetch = vi.fn();

describe('Email Newsletter Image Embedding', () => {
  beforeEach(() => {
    // Reset environment variables
    Object.keys(mockEnv).forEach(key => {
      process.env[key] = mockEnv[key as keyof typeof mockEnv];
    });
    
    // Reset fetch mock
    vi.clearAllMocks();
  });

  afterEach(() => {
    // Clean up environment variables
    Object.keys(mockEnv).forEach(key => {
      delete process.env[key];
    });
  });

  it('should generate newsletter HTML with placeholder when image fetch fails', async () => {
    // Mock fetch to simulate image fetch failure
    (global.fetch as any).mockRejectedValue(new Error('Network error'));

    const newsletterData = {
      name: 'Test User',
      subject: 'Test Newsletter',
      content: 'This is a test newsletter content.',
      featuredArtworks: [
        {
          title: 'Test Artwork',
          description: 'A beautiful test artwork',
          imageUrl: '/images/test-artwork.jpg',
          galleryUrl: '/gallery/test-artwork'
        }
      ],
      upcomingEvents: [],
      authorName: 'Test Author',
      unsubscribeUrl: 'https://example.com/unsubscribe'
    };

    const html = await createNewsletterEmailHtml(newsletterData);

    // Should contain the newsletter content
    expect(html).toContain('Test Newsletter');
    expect(html).toContain('Test User');
    expect(html).toContain('This is a test newsletter content.');
    
    // Should contain the artwork section
    expect(html).toContain('Featured Artworks');
    expect(html).toContain('Test Artwork');
    expect(html).toContain('A beautiful test artwork');
    
    // Should contain placeholder instead of broken image
    expect(html).toContain('Artwork Preview');
    expect(html).toContain('🖼️');
    expect(html).not.toContain('<img src=""'); // Should not have empty img src
    
    // Should contain the gallery link
    expect(html).toContain('https://example.com/gallery/test-artwork');
  });

  it('should generate newsletter HTML with embedded image when fetch succeeds', async () => {
    // Mock successful image fetch
    const mockImageBuffer = Buffer.from('fake-image-data');
    (global.fetch as any).mockResolvedValue({
      ok: true,
      status: 200,
      statusText: 'OK',
      headers: new Map([['content-type', 'image/jpeg']]),
      arrayBuffer: () => Promise.resolve(mockImageBuffer.buffer)
    });

    const newsletterData = {
      name: 'Test User',
      subject: 'Test Newsletter',
      content: 'This is a test newsletter content.',
      featuredArtworks: [
        {
          title: 'Test Artwork',
          description: 'A beautiful test artwork',
          imageUrl: '/images/test-artwork.jpg',
          galleryUrl: '/gallery/test-artwork'
        }
      ],
      upcomingEvents: [],
      authorName: 'Test Author',
      unsubscribeUrl: 'https://example.com/unsubscribe'
    };

    const html = await createNewsletterEmailHtml(newsletterData);

    // Should contain the newsletter content
    expect(html).toContain('Test Newsletter');
    expect(html).toContain('Test User');
    
    // Should contain the artwork section
    expect(html).toContain('Featured Artworks');
    expect(html).toContain('Test Artwork');
    
    // Should contain base64 embedded image
    expect(html).toContain('data:image/jpeg;base64,');
    expect(html).toContain('<img src="data:image/jpeg;base64,');
    
    // Should contain the gallery link
    expect(html).toContain('https://example.com/gallery/test-artwork');
  });

  it('should handle static build mode correctly', async () => {
    // Set static build mode
    process.env.NEXT_PUBLIC_STATIC_BUILD = 'true';
    
    // Mock successful image fetch
    const mockImageBuffer = Buffer.from('fake-image-data');
    (global.fetch as any).mockResolvedValue({
      ok: true,
      status: 200,
      statusText: 'OK',
      headers: new Map([['content-type', 'image/jpeg']]),
      arrayBuffer: () => Promise.resolve(mockImageBuffer.buffer)
    });

    const newsletterData = {
      name: 'Test User',
      subject: 'Test Newsletter',
      content: 'This is a test newsletter content.',
      featuredArtworks: [
        {
          title: 'Test Artwork',
          imageUrl: '/images/test-artwork.jpg'
        }
      ],
      upcomingEvents: [],
      authorName: 'Test Author',
      unsubscribeUrl: 'https://example.com/unsubscribe'
    };

    const html = await createNewsletterEmailHtml(newsletterData);

    // Should fetch from absolute URL in static mode
    expect(global.fetch).toHaveBeenCalledWith(
      'https://example.com/images/test-artwork.jpg',
      expect.any(Object)
    );
  });

  it('should handle Firebase storage mode correctly', async () => {
    // Ensure Firebase mode
    process.env.NEXT_PUBLIC_STATIC_BUILD = 'false';
    
    // Mock successful image fetch
    const mockImageBuffer = Buffer.from('fake-image-data');
    (global.fetch as any).mockResolvedValue({
      ok: true,
      status: 200,
      statusText: 'OK',
      headers: new Map([['content-type', 'image/jpeg']]),
      arrayBuffer: () => Promise.resolve(mockImageBuffer.buffer)
    });

    const newsletterData = {
      name: 'Test User',
      subject: 'Test Newsletter',
      content: 'This is a test newsletter content.',
      featuredArtworks: [
        {
          title: 'Test Artwork',
          imageUrl: '/images/test-artwork.jpg'
        }
      ],
      upcomingEvents: [],
      authorName: 'Test Author',
      unsubscribeUrl: 'https://example.com/unsubscribe'
    };

    const html = await createNewsletterEmailHtml(newsletterData);

    // Should fetch from Firebase Storage URL
    expect(global.fetch).toHaveBeenCalledWith(
      'https://firebasestorage.googleapis.com/v0/b/test-bucket.firebasestorage.app/o/test-gallery%2Fimages%2Ftest-artwork.jpg?alt=media',
      expect.any(Object)
    );
  });

  it('should handle newsletter without artworks', async () => {
    const newsletterData = {
      name: 'Test User',
      subject: 'Test Newsletter',
      content: 'This is a test newsletter content.',
      featuredArtworks: [],
      upcomingEvents: [],
      authorName: 'Test Author',
      unsubscribeUrl: 'https://example.com/unsubscribe'
    };

    const html = await createNewsletterEmailHtml(newsletterData);

    // Should contain the newsletter content
    expect(html).toContain('Test Newsletter');
    expect(html).toContain('Test User');
    expect(html).toContain('This is a test newsletter content.');
    
    // Should not contain artwork section
    expect(html).not.toContain('Featured Artworks');
    
    // Should not call fetch since no images
    expect(global.fetch).not.toHaveBeenCalled();
  });
});
