import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import { v4 as uuidv4 } from "uuid";
import { sendEmail, createConfirmationEmailHtml, createWelcomeEmailHtml } from "./email";

if (!admin.apps.length) admin.initializeApp();

const db = admin.firestore();

// Subscribe function
export const subscribeFunction = functions.https.onRequest(async (req, res) => {
  // Enable CORS
  res.set("Access-Control-Allow-Origin", "*");
  res.set("Access-Control-Allow-Methods", "POST, OPTIONS");
  res.set("Access-Control-Allow-Headers", "Content-Type");
  
  if (req.method === "OPTIONS") {
    res.status(204).send("");
    return;
  }

  if (req.method !== "POST") {
    res.status(405).send("Method Not Allowed");
    return;
  }

  try {
    const { email, name } = req.body;
    // Rest of your subscription logic...
    // Similar to app/api/subscribe/route.ts
  } catch (error) {
    // Error handling
  }
});

// Similar functions for confirm and unsubscribe