// functions/__tests__/integration/publish.test.ts
import * as admin from 'firebase-admin';
import { Request as ExpressRequest, Response } from 'express';
import { publishFunction, rollbackFunction } from '../../src/publish';

// Firebase Functions Request extends Express Request with additional properties
interface Request extends ExpressRequest {
  rawBody: Buffer;
}

// Import setup to ensure emulator connection
import './setup';

describe('Publish Functions Integration Tests', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let db: admin.firestore.Firestore;

  beforeAll(async () => {
    // Initialize Firebase Admin with emulator
    if (!admin.apps.length) {
      admin.initializeApp({
        projectId: 'belagallery-test',
      });
    }
    db = admin.firestore();
  });

  beforeEach(async () => {
    // Clear all collections before each test
    await clearFirestore();
    
    // Setup request and response objects
    req = {
      method: 'POST',
      body: { data: { clearPreview: true } },
      headers: {
        'content-type': 'application/json',
      },
    };
    
    res = {
      set: jest.fn().mockReturnThis(),
      status: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };
  });

  afterAll(async () => {
    // Clean up
    await clearFirestore();
  });

  async function clearFirestore() {
    const collections = ['live', 'preview', 'versions', 'audit'];
    for (const collectionName of collections) {
      const snapshot = await db.collection(collectionName).get();
      const batch = db.batch();
      snapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });
      await batch.commit();
    }
  }

  describe('publishFunction Integration', () => {
    it('should handle OPTIONS request', async () => {
      // Setup
      req.method = 'OPTIONS';
      
      // Execute
      await publishFunction(req as Request, res as Response);
      
      // Verify
      expect(res.status).toHaveBeenCalledWith(204);
      expect(res.send).toHaveBeenCalledWith('');
    });

    it('should reject non-POST methods', async () => {
      // Setup
      req.method = 'GET';
      
      // Execute
      await publishFunction(req as Request, res as Response);
      
      // Verify
      expect(res.status).toHaveBeenCalledWith(405);
      expect(res.send).toHaveBeenCalledWith('Method Not Allowed');
    });

    it('should successfully publish content from preview to live', async () => {
      // Setup - Add simple preview content
      const previewData = {
        home: { title: 'Home Page', content: 'Welcome to our gallery' }
      };

      // Add preview document
      await db.collection('preview').doc('home').set(previewData.home);

      // Execute
      await publishFunction(req as Request, res as Response);

      // Check what response we actually got
      const statusCalls = (res.status as jest.Mock).mock.calls;
      const jsonCalls = (res.json as jest.Mock).mock.calls;
      
      if (statusCalls.length > 0 && statusCalls[0][0] !== 200) {
        console.log('Status:', statusCalls[0][0]);
        console.log('Response:', jsonCalls[0] ? jsonCalls[0][0] : 'No JSON response');
      }

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        versionId: expect.any(String)
      });

      // Verify live content was created
      const liveHome = await db.collection('live').doc('home').get();
      expect(liveHome.exists).toBe(true);
      expect(liveHome.data()).toEqual(previewData.home);

      // Verify preview was cleared (since clearPreview: true by default)
      const previewSnapshot = await db.collection('preview').get();
      expect(previewSnapshot.empty).toBe(true);
    });

    it('should handle empty preview collection', async () => {
      // Execute with empty preview
      await publishFunction(req as Request, res as Response);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        versionId: expect.any(String)
      });

      // Verify no live content was created
      const liveSnapshot = await db.collection('live').get();
      expect(liveSnapshot.empty).toBe(true);
    });

    it('should not clear preview when clearPreview is false', async () => {
      // Setup - explicitly set clearPreview to false
      req.body = { data: { clearPreview: false } };
      
      const previewData = {
        home: { title: 'Home Page', content: 'Welcome to our gallery' }
      };

      // Add preview document
      await db.collection('preview').doc('home').set(previewData.home);

      // Execute
      await publishFunction(req as Request, res as Response);

      // Wait for the operation to complete
      await new Promise(resolve => setTimeout(resolve, 500));

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);

      // Verify preview was NOT cleared
      const previewSnapshot = await db.collection('preview').get();
      expect(previewSnapshot.empty).toBe(false);
    });
  });

  describe('rollbackFunction Integration', () => {
    it('should handle OPTIONS request', async () => {
      // Setup
      req.method = 'OPTIONS';
      
      // Execute
      await rollbackFunction(req as Request, res as Response);
      
      // Verify
      expect(res.status).toHaveBeenCalledWith(204);
      expect(res.send).toHaveBeenCalledWith('');
    });

    it('should reject non-POST methods', async () => {
      // Setup
      req.method = 'GET';
      
      // Execute
      await rollbackFunction(req as Request, res as Response);
      
      // Verify
      expect(res.status).toHaveBeenCalledWith(405);
      expect(res.send).toHaveBeenCalledWith('Method Not Allowed');
    });

    it('should require versionId', async () => {
      // Setup
      req.body = { data: {} };
      
      // Execute
      await rollbackFunction(req as Request, res as Response);
      
      // Verify
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: 'Version ID is required',
      });
    });

    it('should rollback content from a version', async () => {
      // Setup - Create a version with some content
      const versionId = 'test-version-123';
      const versionData = {
        timestamp: new Date(),
        createdAt: new Date(),
        pages: ['home']
      };

      // Create version metadata
      await db.collection('backup_meta').doc(versionId).set(versionData);
      
      // Create backup document
      await db.collection('backups').doc(`home_${versionId}`).set({
        title: 'Old Home',
        content: 'Old content',
        _meta: { page: 'home', versionId, timestamp: new Date() }
      });

      // Create some current live content
      await db.collection('live').doc('home').set({ title: 'Current Home', content: 'Current content' });

      // Setup request
      req.body = { data: { versionId } };

      // Wait a bit to ensure documents are written
      await new Promise(resolve => setTimeout(resolve, 100));

      // Execute
      await rollbackFunction(req as Request, res as Response);

      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        versionId,
        restoredPages: ['home']
      });

      // Verify live content was updated
      const liveHome = await db.collection('live').doc('home').get();
      expect(liveHome.exists).toBe(true);
      expect(liveHome.data()).toMatchObject({
        title: 'Old Home',
        content: 'Old content'
      });
    });
  });
});