// functions/__tests__/unit/setup.ts
/* eslint-disable @typescript-eslint/no-explicit-any */

// Mock Firebase Admin SDK for unit tests
const mockBatch: any = {
  set: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  commit: jest.fn().mockResolvedValue(undefined),
};

const mockDoc: any = {
  get: jest.fn(),
  set: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
};

const mockCollection: any = {
  doc: jest.fn(() => mockDoc),
  add: jest.fn(),
  get: jest.fn(),
  where: jest.fn(() => mockCollection),
  orderBy: jest.fn(() => mockCollection),
  limit: jest.fn(() => mockCollection),
  listDocuments: jest.fn(),
};

const mockFirestore: any = {
  collection: jest.fn(() => mockCollection),
  doc: jest.fn(() => mockDoc),
  batch: jest.fn(() => mockBatch),
};

jest.mock('firebase-admin', () => ({
  apps: [],
  initializeApp: jest.fn(),
  firestore: jest.fn(() => mockFirestore),
  storage: jest.fn(() => ({
    bucket: jest.fn(),
  })),
}));

// Mock Firebase Functions SDK
jest.mock('firebase-functions', () => ({
  logger: {
    log: jest.fn(),
    info: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    write: jest.fn(),
  },
  https: {
    onRequest: jest.fn((handler) => handler),
  },
}));

// Note: Individual test files will handle their own module mocking as needed
// This setup file only provides global Firebase Admin SDK mocks

// Silence console output during tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Export an empty object to make this file a module
export {};