import GalleryCategoryPage from '@/components/GalleryCategoryPage';
import slugs from './slugs.json';
import { notFound } from 'next/navigation';

export function generateStaticParams() {
    // Use the slugs generated at build time
    return slugs.map((slug) => ({
        slug,
    }));
}

export default async function GalleryCategoryPageWrapper({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;
  
  // Check if the slug is valid
  if (!slugs.includes(slug)) {
    notFound();
  }
  
  return (
    <GalleryCategoryPage slug={slug} />
  );
}