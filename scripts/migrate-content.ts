import * as fs from "node:fs/promises";
import * as path from "node:path";
import { initializeApp, getApps, deleteApp } from "firebase/app";
import {
  getFirestore,
  connectFirestoreEmulator,
  doc,
  setDoc,
  getDoc,
  terminate,
} from "firebase/firestore";
import { ref, uploadBytes } from "firebase/storage";
import { glob } from "glob";
import * as dotenv from "dotenv";
import tryToCatch from "try-to-catch";
import { storage } from "./firebase-storage";
import { Artwork } from "../types/artworks";

dotenv.config({
  path: ".env.local",
});

// Source collections
const ARTWORKS_COLLECTION = "artworks";

/**
 * Creates a URL-friendly ID from an artwork title
 * @param title The artwork title
 * @returns A URL-friendly ID
 */
function createArtworkId(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\da-z]+/g, "-")
    .replace(/^-|-$/g, "");
}

// Initialize Firebase for the script
const firebaseConfig = {
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || "bela-gallery",
  storageBucket:
    process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET ||
    "bela-gallery.appspot.com",
};

// Initialize Firebase
const app = initializeApp(firebaseConfig, "content-migration");
const db = getFirestore(app);

// Connect to Firestore emulator
connectFirestoreEmulator(db, "localhost", 8080);

const BASE_PATH = process.env.NEXT_PUBLIC_STORAGE_BASE_PATH || "bela-gallery";

// Update paths to point to the new seed directory
const SEED_IMAGES_DIR = path.join(process.cwd(), "scripts/seed/images");
const SEED_DATA_DIR = path.join(process.cwd(), "scripts/seed/data");

interface ImageRef {
  sourcePath: string;

  // Path in scripts/seed/images
  storagePath: string;

  // Path in Firebase Storage
  relativePath: string;

  // Path relative to seed images dir (used for storage path construction)
}

// Helper to read JSON file
async function readJsonFile(filePath: string) {
  const content = await fs.readFile(filePath, "utf-8");
  return JSON.parse(content);
}

// Find all images in scripts/seed/images
async function findImages(): Promise<ImageRef[]> {
  const files = await glob("**/*.{jpg,jpeg,png,gif,webp}", {
    cwd: SEED_IMAGES_DIR, // Use new seed images directory
    nodir: true,
  });

  return files.map((file) => ({
    sourcePath: path.join(SEED_IMAGES_DIR, file), // Path to the source file for reading
    storagePath: `${BASE_PATH}/images/${file.replace(
      /^[\\/]*images[\\/]+/,
      ""
    )}`,
    // Ensure /images/ prefix is present
    relativePath: file, // Relative path within the seed images dir
  }));
}

// Upload a single image to Firebase Storage
async function uploadImage(image: ImageRef): Promise<void> {
  const fileContent = await fs.readFile(image.sourcePath); // Read from the source path
  const storageRef = ref(storage, image.storagePath);
  const [error] = await tryToCatch(uploadBytes, storageRef, fileContent);

  if (error) throw error;
}

// Upload content from data directory to Firestore with new structure
async function uploadContent(): Promise<void> {
  // Map of directory names to Firestore subcollection names under 'live'
  const collectionMap: Record<string, string> = {
    home: "home",
    about: "about",
    gallery: "gallery",
    contact: "contact",
    events: "events",
  };

  // Process each section directory
  for (const [dir, subcollection] of Object.entries(collectionMap)) {
    const sectionPath = path.join(SEED_DATA_DIR, dir);

    try {
      // Get all JSON files in the directory
      const files = await glob("*.json", {
        cwd: sectionPath,
        nodir: true,
      });

      // Special handling for gallery collection
      if (subcollection === "gallery") {
        // Initialize gallery data structure
        const galleryData: {
          categories: unknown[];
          slugs: { slug: string; artworks: unknown[] }[];
        } = {
          categories: [],
          slugs: [],
        };
        // Temporary map to collect slug->artworks
        const slugsMap: Record<string, unknown[]> = {};
        // Process all gallery files
        for (const file of files) {
          const filePath = path.join(sectionPath, file);
          const content = await readJsonFile(filePath);
          const docId = path.basename(file, ".json");
          if (docId === "categories") {
            if (content.categories && Array.isArray(content.categories))
              galleryData.categories = content.categories;
            else if (Array.isArray(content)) galleryData.categories = content;
            else galleryData.categories = [];
          } else if (Array.isArray(content)) {
            slugsMap[docId] = content;
          }
        }
        // Convert slugsMap to array of {slug, artworks}
        galleryData.slugs = Object.entries(slugsMap).map(
          ([slug, artworks]) => ({ slug, artworks })
        );
        // Upload the consolidated gallery data to live/gallery
        await setDoc(doc(db, "live", subcollection), galleryData);
      } else if (subcollection === "events") {
        // Special handling for events to remove status field
        for (const file of files) {
          const filePath = path.join(sectionPath, file);
          const content = await readJsonFile(filePath);
          const docId = path.basename(file, ".json");

          if (docId === "content" || docId === "events") {
            // Process events to remove status field
            if (content.events && Array.isArray(content.events)) {
              const eventsWithoutStatus = content.events.map((event: { status?: string; [key: string]: unknown }) => {
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                const { status, ...eventWithoutStatus } = event;
                return eventWithoutStatus;
              });
              
              await setDoc(doc(db, "live", subcollection), {
                events: eventsWithoutStatus
              });
            } else {
              await setDoc(doc(db, "live", subcollection), content);
            }
          }
        }
      } else {
        // For non-gallery collections
        for (const file of files) {
          const filePath = path.join(sectionPath, file);
          const content = await readJsonFile(filePath);
          const docId = path.basename(file, ".json");

          // Skip 'content.json' as we're storing data directly in the subcollection
          if (docId === "content") {
            // Store content directly in the subcollection document
            
            // Special handling for about content to ensure exhibitions.duo and professionalExperience are properly handled
            if (subcollection === "about") {
              // Ensure exhibitions.duo exists
              if (content.exhibitions && !content.exhibitions.duo) {
                content.exhibitions.duo = [];
              }
              
              // Ensure professionalExperience exists
              if (!content.professionalExperience) {
                content.professionalExperience = [];
              }
            }
            
            await setDoc(doc(db, "live", subcollection), content);
          } else {
            // For other documents, maintain their structure but under the live collection
            let data = content;

            if (Array.isArray(content))
              data = {
                items: content,
              };

            await setDoc(doc(db, "live", subcollection), {
              [docId]: data,
            });
          }
        }
      }
    } catch (error) {
      throw error;
    }
  }
}

async function migrateArtworks() {
  // Track all created artworks to avoid duplicates
  const createdArtworks = new Map<string, string>();

  try {
    // Step 1: Extract artworks from gallery categories in live/gallery/slugs
    const galleryDoc = await getDoc(doc(db, "live", "gallery"));

    if (galleryDoc.exists()) {
      const galleryData = galleryDoc.data();
      const slugs = galleryData.slugs || [];

      // Process each slug (category)
      for (const entry of slugs) {
        const { slug, artworks } = entry;
        if (!Array.isArray(artworks)) continue;

        for (const artwork of artworks) {
          if (!artwork.image) continue;

          // Skip if we've already processed this image
          if (createdArtworks.has(artwork.image)) continue;

          // Create new artwork document
          const artworkData: Artwork = {
            title: artwork.title || "Untitled",
            image: artwork.image,
            description: artwork.description || "",
            year: artwork.year || "",
            size: artwork.size || "",
            medium: artwork.medium || "",
            category: slug,
            tags: [slug],
            galleryPath: slug,
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          const artworkId = createArtworkId(artworkData.title);
          await setDoc(doc(db, ARTWORKS_COLLECTION, artworkId), artworkData);
          createdArtworks.set(artwork.image, artworkId);
        }
      }
    }

    // Step 2: Extract artworks from events
    const eventsDoc = await getDoc(doc(db, "live", "events"));

    if (eventsDoc.exists()) {
      const eventsData = eventsDoc.data();
      const events = eventsData.events || [];

      for (const [i, event] of events.entries()) {
        if (event.images?.main) {
          if (createdArtworks.has(event.images.main)) continue;

          const artworkData: Artwork = {
            title: `Event Image - ${event.title || i}`,
            image: event.images.main,
            description: `Main image for event: ${event.title || "Untitled"}`,
            year: "",
            size: "",
            medium: "",
            category: "Events",
            tags: ["Event"],
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          const artworkId = `event-${i}-main`;
          await setDoc(doc(db, ARTWORKS_COLLECTION, artworkId), artworkData);
          createdArtworks.set(event.images.main, artworkId);
        }

        if (event.images?.details) {
          if (createdArtworks.has(event.images.details)) continue;

          const artworkData: Artwork = {
            title: `Event Details - ${event.title || i}`,
            image: event.images.details,
            description: `Details image for event: ${event.title || "Untitled"}`,
            year: "",
            size: "",
            medium: "",
            category: "Events",
            tags: ["Event", "Details"],
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          const artworkId = `event-${i}-details`;
          await setDoc(doc(db, ARTWORKS_COLLECTION, artworkId), artworkData);
          createdArtworks.set(event.images.details, artworkId);
        }
      }
    }

    // Step 3: Handle artist profile image if needed
    try {
      const aboutDoc = await getDoc(doc(db, "live", "about"));

      if (aboutDoc.exists()) {
        const aboutData = aboutDoc.data();

        if (aboutData.profileImage && !createdArtworks.has(aboutData.profileImage)) {
          const artworkData: Artwork = {
            title: `Artist Profile`,
            image: aboutData.profileImage,
            description: `Profile image for the artist`,
            year: "",
            size: "",
            medium: "",
            category: "Profile",
            tags: ["Profile"],
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          const artworkId = `artist-profile`;
          await setDoc(doc(db, ARTWORKS_COLLECTION, artworkId), artworkData);
          createdArtworks.set(aboutData.profileImage, artworkId);
        }
      }
    } catch {}
  } catch (error) {
    throw error;
  }
}

async function main() {
  try {
    // 1. Upload content to Firestore
    await uploadContent();
    // 2. Process images
    const images = await findImages();
    for (const image of images) {
      await uploadImage(image);
    }
    // 3. Migrate artworks
    await migrateArtworks();
    
    console.log("Migration completed successfully!");
    
    // Clean up Firebase connections
    await terminate(db);
    
    // Clean up any Firebase apps
    const apps = getApps();
    for (const app of apps) {
      await deleteApp(app);
    }
    
    // Explicitly exit the process when done
    process.exit(0);
  } catch (error) {
    console.error("Migration failed:", error);
    
    // Clean up Firebase connections even on error
    try {
      await terminate(db);
      const apps = getApps();
      for (const app of apps) {
        await deleteApp(app);
      }
    } catch (cleanupError) {
      console.error("Error during cleanup:", cleanupError);
    }
    
    process.exit(1);
  }
}

main();
