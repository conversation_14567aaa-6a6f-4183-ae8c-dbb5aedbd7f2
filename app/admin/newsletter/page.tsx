'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAdminAuth } from '@/lib/useAdminAuth';
import { useImageResolver } from '@/hooks/use-image-resolver';
import { Search, Plus, Image, Calendar, Bold, Italic, List } from 'lucide-react';

interface Artwork {
  id?: string;
  title: string;
  description: string;
  imageUrl: string;
  galleryUrl: string;
  year?: string;
  medium?: string;
  category?: string;
}

interface Event {
  id?: string;
  title: string;
  date: string;
  description: string;
  location: string;
  image?: string;
}

interface AvailableArtwork {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  year: string;
  medium: string;
  category: string;
  galleryUrl: string;
}

interface AvailableEvent {
  id: string;
  title: string;
  date: string;
  description: string;
  location: string;
  image: string;
  status?: string;
}

interface NewsletterHistory {
  id: string;
  subject: string;
  content: string;
  sentAt: string;
  recipientCount: number;
  successfulSends: number;
  failedSends: number;
}

export default function NewsletterPage() {
  const { user, loading } = useAdminAuth();
  const isAdmin = !!user;
  const [subject, setSubject] = useState('An update from Bela Gallery ');
  const [content, setContent] = useState('');
  const [authorName, setAuthorName] = useState('Bela Raval');
  const [artworks, setArtworks] = useState<Artwork[]>([]);
  const [events, setEvents] = useState<Event[]>([]);
  const [sending, setSending] = useState(false);
  const [history, setHistory] = useState<NewsletterHistory[]>([]);
  const [loadingHistory, setLoadingHistory] = useState(true);

  // Available content for selection
  const [availableArtworks, setAvailableArtworks] = useState<AvailableArtwork[]>([]);
  const [availableEvents, setAvailableEvents] = useState<AvailableEvent[]>([]);
  const [artworkCategories, setArtworkCategories] = useState<string[]>([]);
  
  // Search and filter states
  const [artworkSearch, setArtworkSearch] = useState('');
  const [artworkCategory, setArtworkCategory] = useState('');
  const [loadingArtworks, setLoadingArtworks] = useState(false);
  const [loadingEvents, setLoadingEvents] = useState(false);

  // Content enhancement tools
  const [contentTemplates, setContentTemplates] = useState([
    "I'm excited to share some wonderful updates from the gallery...",
    "This month has been filled with creative energy and new discoveries...",
    "I hope this message finds you well and inspired...",
    "It's been an incredible journey, and I wanted to share some highlights with you..."
  ]);
  const [showTemplateManager, setShowTemplateManager] = useState(false);
  const [newTemplate, setNewTemplate] = useState('');
  const [editingTemplateIndex, setEditingTemplateIndex] = useState<number | null>(null);

  // Image resolution for artwork thumbnails
  const { resolvedUrls, resolveImages } = useImageResolver();

  // Load newsletter history and available content
  useEffect(() => {
    if (isAdmin) {
      fetchHistory();
      fetchAvailableEvents();
    }
  }, [isAdmin]);

  // Fetch artworks when search or category changes
  useEffect(() => {
    if (isAdmin) {
      fetchAvailableArtworks();
    }
  }, [isAdmin, artworkSearch, artworkCategory]);

  // Resolve artwork images when available artworks change
  useEffect(() => {
    if (availableArtworks.length > 0) {
      const imagePaths = availableArtworks
        .map(artwork => artwork.imageUrl)
        .filter(Boolean);
      resolveImages(imagePaths);
    }
  }, [availableArtworks, resolveImages]);

  const fetchHistory = async () => {
    try {
      const functionUrl = process.env.NODE_ENV === 'production'
        ? 'https://us-central1-belagallery-9e01b.cloudfunctions.net/getNewsletterHistory'
        : 'http://127.0.0.1:5001/belagallery-9e01b/us-central1/getNewsletterHistory';

      const response = await fetch(functionUrl);
      if (response.ok) {
        const data = await response.json();
        setHistory(data.newsletters);
      }
    } catch (error) {
      console.error('Error fetching newsletter history:', error);
    } finally {
      setLoadingHistory(false);
    }
  };

  const fetchAvailableArtworks = async () => {
    setLoadingArtworks(true);
    try {
      const params = new URLSearchParams();
      if (artworkSearch) params.append('search', artworkSearch);
      if (artworkCategory && artworkCategory !== 'all') params.append('category', artworkCategory);
      params.append('limit', '20');

      const functionUrl = process.env.NODE_ENV === 'production'
        ? 'https://us-central1-belagallery-9e01b.cloudfunctions.net/getNewsletterArtworks'
        : 'http://127.0.0.1:5001/belagallery-9e01b/us-central1/getNewsletterArtworks';

      const response = await fetch(`${functionUrl}?${params}`);
      if (response.ok) {
        const data = await response.json();
        setAvailableArtworks(data.artworks);
        setArtworkCategories(data.categories);
      }
    } catch (error) {
      console.error('Error fetching artworks:', error);
    } finally {
      setLoadingArtworks(false);
    }
  };

  const fetchAvailableEvents = async () => {
    setLoadingEvents(true);
    try {
      const functionUrl = process.env.NODE_ENV === 'production'
        ? 'https://us-central1-belagallery-9e01b.cloudfunctions.net/getNewsletterEvents'
        : 'http://127.0.0.1:5001/belagallery-9e01b/us-central1/getNewsletterEvents';

      const response = await fetch(functionUrl);
      if (response.ok) {
        const data = await response.json();
        setAvailableEvents(data.events);
      }
    } catch (error) {
      console.error('Error fetching events:', error);
    } finally {
      setLoadingEvents(false);
    }
  };

  const addArtwork = () => {
    setArtworks([...artworks, { title: '', description: '', imageUrl: '', galleryUrl: '' }]);
  };

  const addArtworkFromSelection = (selectedArtwork: AvailableArtwork) => {
    const newArtwork: Artwork = {
      id: selectedArtwork.id,
      title: selectedArtwork.title,
      description: selectedArtwork.description,
      imageUrl: selectedArtwork.imageUrl,
      galleryUrl: selectedArtwork.galleryUrl,
      year: selectedArtwork.year,
      medium: selectedArtwork.medium,
      category: selectedArtwork.category
    };
    setArtworks([...artworks, newArtwork]);
  };

  const updateArtwork = (index: number, field: keyof Artwork, value: string) => {
    const updated = [...artworks];
    updated[index][field] = value;
    setArtworks(updated);
  };

  const removeArtwork = (index: number) => {
    setArtworks(artworks.filter((_, i) => i !== index));
  };

  const addEvent = () => {
    setEvents([...events, { title: '', date: '', description: '', location: '' }]);
  };

  const addEventFromSelection = (selectedEvent: AvailableEvent) => {
    const newEvent: Event = {
      id: selectedEvent.id,
      title: selectedEvent.title,
      date: selectedEvent.date,
      description: selectedEvent.description,
      location: selectedEvent.location,
      image: selectedEvent.image
    };
    setEvents([...events, newEvent]);
  };

  const insertContentTemplate = (template: string) => {
    setContent(content + (content ? '\n\n' : '') + template);
  };

  const formatText = (format: 'bold' | 'italic' | 'list') => {
    // Simple text formatting helpers
    const textarea = document.querySelector('textarea[placeholder*="main newsletter content"]') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = content.substring(start, end);
    
    let formattedText = '';
    switch (format) {
      case 'bold':
        formattedText = `**${selectedText || 'bold text'}**`;
        break;
      case 'italic':
        formattedText = `*${selectedText || 'italic text'}*`;
        break;
      case 'list':
        formattedText = selectedText ?
          selectedText.split('\n').map(line => `• ${line}`).join('\n') :
          '• List item 1\n• List item 2\n• List item 3';
        break;
    }

    const newContent = content.substring(0, start) + formattedText + content.substring(end);
    setContent(newContent);
  };

  // Template management functions
  const addTemplate = () => {
    if (newTemplate.trim()) {
      setContentTemplates([...contentTemplates, newTemplate.trim()]);
      setNewTemplate('');
    }
  };

  const editTemplate = (index: number) => {
    setEditingTemplateIndex(index);
    setNewTemplate(contentTemplates[index]);
  };

  const saveTemplateEdit = () => {
    if (editingTemplateIndex !== null && newTemplate.trim()) {
      const updated = [...contentTemplates];
      updated[editingTemplateIndex] = newTemplate.trim();
      setContentTemplates(updated);
      setEditingTemplateIndex(null);
      setNewTemplate('');
    }
  };

  const deleteTemplate = (index: number) => {
    setContentTemplates(contentTemplates.filter((_, i) => i !== index));
  };

  const cancelTemplateEdit = () => {
    setEditingTemplateIndex(null);
    setNewTemplate('');
  };

  const updateEvent = (index: number, field: keyof Event, value: string) => {
    const updated = [...events];
    updated[index][field] = value;
    setEvents(updated);
  };

  const removeEvent = (index: number) => {
    setEvents(events.filter((_, i) => i !== index));
  };

  const sendNewsletter = async () => {
    if (!subject.trim() || !content.trim()) {
      alert('Please fill in both subject and content');
      return;
    }

    setSending(true);
    try {
      const functionUrl = process.env.NODE_ENV === 'production'
        ? 'https://us-central1-belagallery-9e01b.cloudfunctions.net/sendNewsletter'
        : 'http://127.0.0.1:5001/belagallery-9e01b/us-central1/sendNewsletter';

      const response = await fetch(functionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subject,
          content,
          featuredArtworks: artworks.filter(a => a.title.trim()),
          upcomingEvents: events.filter(e => e.title.trim()),
          authorName,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        alert(`Newsletter sent successfully to ${result.successful} subscribers!`);
        // Reset form
        console.log('Resetting subject to: "An update from Bela Gallery "');
        setSubject('An update from Bela Gallery ');
        setContent('');
        setArtworks([]);
        setEvents([]);
        // Refresh history
        fetchHistory();
      } else {
        alert(`Error: ${result.error}`);
      }
    } catch (error) {
      alert('Failed to send newsletter. Please try again.');
      console.error('Newsletter send error:', error);
    } finally {
      setSending(false);
    }
  };

  if (loading) {
    return <div className="p-8">Loading...</div>;
  }

  if (!isAdmin) {
    return <div className="p-8">Access denied. Admin privileges required.</div>;
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Newsletter Management</h1>
        <p className="text-gray-600">Send newsletters to your subscribers with artwork updates and event announcements.</p>
      </div>

      <div className="grid gap-8 lg:grid-cols-3">
        {/* Newsletter Composer */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Compose Newsletter</CardTitle>
              <CardDescription>Create and send a newsletter to all confirmed subscribers</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Basic Info */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Subject Line</label>
                  <Input
                    value={subject}
                    onChange={(e) => {
                      console.log('Subject changed to:', e.target.value);
                      setSubject(e.target.value);
                    }}
                    placeholder="e.g., New Artworks & Upcoming Exhibition"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">Author Name</label>
                  <Input
                    value={authorName}
                    onChange={(e) => setAuthorName(e.target.value)}
                    placeholder="e.g., Bela Raval"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Main Content</label>
                  
                  {/* Content Enhancement Tools */}
                  <div className="mb-3 flex flex-wrap gap-2">
                    <div className="flex gap-1">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => formatText('bold')}
                        className="h-8"
                      >
                        <Bold className="h-3 w-3" />
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => formatText('italic')}
                        className="h-8"
                      >
                        <Italic className="h-3 w-3" />
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => formatText('list')}
                        className="h-8"
                      >
                        <List className="h-3 w-3" />
                      </Button>
                    </div>
                    
                    <div className="border-l border-gray-300 mx-2"></div>
                    
                    <div className="flex gap-1">
                      <Select value="" onValueChange={insertContentTemplate}>
                        <SelectTrigger className="w-48 h-8">
                          <SelectValue placeholder="Insert template..." />
                        </SelectTrigger>
                        <SelectContent>
                          {contentTemplates.map((template, index) => (
                            <SelectItem key={index} value={template}>
                              {template.substring(0, 40)}...
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setShowTemplateManager(!showTemplateManager)}
                        className="h-8"
                      >
                        Manage
                      </Button>
                    </div>
                  </div>

                  <Textarea
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                    placeholder="Write your main newsletter content here..."
                    rows={8}
                    className="font-mono text-sm"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Use **bold**, *italic*, and • for lists. Templates and formatting tools available above.
                  </p>

                  {/* Template Manager */}
                  {showTemplateManager && (
                    <Card className="mt-4 bg-blue-50">
                      <CardContent className="pt-4">
                        <h4 className="font-medium mb-3">Manage Content Templates</h4>
                        
                        {/* Add/Edit Template */}
                        <div className="mb-4">
                          <div className="flex gap-2 mb-2">
                            <Textarea
                              value={newTemplate}
                              onChange={(e) => setNewTemplate(e.target.value)}
                              placeholder="Enter a new template..."
                              rows={2}
                              className="flex-1"
                            />
                            <div className="flex flex-col gap-1">
                              {editingTemplateIndex !== null ? (
                                <>
                                  <Button
                                    onClick={saveTemplateEdit}
                                    size="sm"
                                    disabled={!newTemplate.trim()}
                                  >
                                    Save
                                  </Button>
                                  <Button
                                    onClick={cancelTemplateEdit}
                                    variant="outline"
                                    size="sm"
                                  >
                                    Cancel
                                  </Button>
                                </>
                              ) : (
                                <Button
                                  onClick={addTemplate}
                                  size="sm"
                                  disabled={!newTemplate.trim()}
                                >
                                  <Plus className="h-3 w-3 mr-1" />
                                  Add
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Existing Templates */}
                        <div className="space-y-2">
                          <h5 className="text-sm font-medium text-gray-700">Existing Templates:</h5>
                          {contentTemplates.map((template, index) => (
                            <div key={index} className="flex items-start gap-2 p-2 bg-white rounded border">
                              <div className="flex-1 text-sm">
                                {template}
                              </div>
                              <div className="flex gap-1">
                                <Button
                                  onClick={() => editTemplate(index)}
                                  variant="outline"
                                  size="sm"
                                  className="h-6 text-xs"
                                >
                                  Edit
                                </Button>
                                <Button
                                  onClick={() => deleteTemplate(index)}
                                  variant="destructive"
                                  size="sm"
                                  className="h-6 text-xs"
                                >
                                  Delete
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>

              <Separator />

              {/* Featured Artworks */}
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold">Featured Artworks</h3>
                  <div className="flex gap-2">
                    <Button onClick={addArtwork} variant="outline" size="sm">
                      <Plus className="h-3 w-3 mr-1" />
                      Add Custom
                    </Button>
                  </div>
                </div>

                {/* Artwork Selection from Gallery */}
                <Card className="mb-4 bg-gray-50">
                  <CardContent className="pt-4">
                    <h4 className="font-medium mb-3 flex items-center">
                      <Image className="h-4 w-4 mr-2" />
                      Select from Gallery
                    </h4>
                    
                    {/* Search and Filter */}
                    <div className="flex gap-2 mb-3">
                      <div className="relative flex-1">
                        <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400" />
                        <Input
                          value={artworkSearch}
                          onChange={(e) => setArtworkSearch(e.target.value)}
                          placeholder="Search artworks..."
                          className="pl-8 h-8"
                        />
                      </div>
                      <Select value={artworkCategory} onValueChange={setArtworkCategory}>
                        <SelectTrigger className="w-40 h-8">
                          <SelectValue placeholder="Category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Categories</SelectItem>
                          {artworkCategories.map((category) => (
                            <SelectItem key={category} value={category}>
                              {category}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Available Artworks */}
                    <div className="max-h-48 overflow-y-auto">
                      {loadingArtworks ? (
                        <p className="text-sm text-gray-500 py-4">Loading artworks...</p>
                      ) : availableArtworks.length === 0 ? (
                        <p className="text-sm text-gray-500 py-4">No artworks found.</p>
                      ) : (
                        <div className="space-y-2">
                          {availableArtworks.map((artwork) => (
                            <div key={artwork.id} className="flex items-center gap-3 p-2 bg-white rounded border">
                              {/* Thumbnail Preview */}
                              <div className="w-12 h-12 bg-gray-100 rounded overflow-hidden flex-shrink-0">
                                {artwork.imageUrl && resolvedUrls[artwork.imageUrl] ? (
                                  <img
                                    src={resolvedUrls[artwork.imageUrl] || ''}
                                    alt={artwork.title}
                                    className="w-full h-full object-cover"
                                    onError={(e) => {
                                      const target = e.target as HTMLImageElement;
                                      target.style.display = 'none';
                                      target.nextElementSibling?.classList.remove('hidden');
                                    }}
                                  />
                                ) : null}
                                <div className={`w-full h-full flex items-center justify-center text-gray-400 ${artwork.imageUrl && resolvedUrls[artwork.imageUrl] ? 'hidden' : ''}`}>
                                  <Image className="h-4 w-4" />
                                </div>
                              </div>
                              
                              <div className="flex-1 min-w-0">
                                <p className="font-medium text-sm truncate">{artwork.title}</p>
                                <p className="text-xs text-gray-500">{artwork.year} • {artwork.medium}</p>
                                {artwork.category && (
                                  <p className="text-xs text-gray-400">{artwork.category}</p>
                                )}
                              </div>
                              
                              <Button
                                onClick={() => addArtworkFromSelection(artwork)}
                                variant="outline"
                                size="sm"
                                className="h-7 text-xs flex-shrink-0"
                              >
                                Add
                              </Button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
                
                {artworks.map((artwork, index) => (
                  <Card key={index} className="mb-4">
                    <CardContent className="pt-4">
                      <div className="grid gap-3">
                        <div className="flex justify-between items-center">
                          <h4 className="font-medium">Artwork {index + 1}</h4>
                          <Button onClick={() => removeArtwork(index)} variant="destructive" size="sm">
                            Remove
                          </Button>
                        </div>
                        <Input
                          value={artwork.title}
                          onChange={(e) => updateArtwork(index, 'title', e.target.value)}
                          placeholder="Artwork title"
                        />
                        <Textarea
                          value={artwork.description}
                          onChange={(e) => updateArtwork(index, 'description', e.target.value)}
                          placeholder="Artwork description"
                          rows={2}
                        />
                        <Input
                          value={artwork.imageUrl}
                          onChange={(e) => updateArtwork(index, 'imageUrl', e.target.value)}
                          placeholder="Image URL (optional)"
                        />
                        <Input
                          value={artwork.galleryUrl}
                          onChange={(e) => updateArtwork(index, 'galleryUrl', e.target.value)}
                          placeholder="Gallery page URL (optional)"
                        />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <Separator />

              {/* Upcoming Events */}
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold">Upcoming Events</h3>
                  <div className="flex gap-2">
                    <Button onClick={addEvent} variant="outline" size="sm">
                      <Plus className="h-3 w-3 mr-1" />
                      Add Custom
                    </Button>
                  </div>
                </div>

                {/* Event Selection from Site */}
                <Card className="mb-4 bg-gray-50">
                  <CardContent className="pt-4">
                    <h4 className="font-medium mb-3 flex items-center">
                      <Calendar className="h-4 w-4 mr-2" />
                      Select from Published Events
                    </h4>
                    
                    {/* Available Events */}
                    <div className="max-h-48 overflow-y-auto">
                      {loadingEvents ? (
                        <p className="text-sm text-gray-500 py-4">Loading events...</p>
                      ) : availableEvents.length === 0 ? (
                        <div className="text-sm text-gray-500 py-4">
                          <p>No ongoing or upcoming events found.</p>
                          <p className="text-xs mt-1">
                            Create events through <a href="/admin/events" className="text-blue-600 hover:underline">Admin → Events</a> first.
                          </p>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {availableEvents.map((event) => (
                            <div key={event.id} className="flex items-center justify-between p-2 bg-white rounded border">
                              <div className="flex-1">
                                <div className="flex items-center gap-2">
                                  <p className="font-medium text-sm">{event.title}</p>
                                  {event.status && (
                                    <span className={`text-xs px-2 py-1 rounded ${
                                      event.status === 'ongoing'
                                        ? 'bg-green-100 text-green-700'
                                        : 'bg-blue-100 text-blue-700'
                                    }`}>
                                      {event.status}
                                    </span>
                                  )}
                                </div>
                                <p className="text-xs text-gray-500">{event.date}</p>
                                {event.location && (
                                  <p className="text-xs text-gray-400">📍 {event.location}</p>
                                )}
                              </div>
                              <Button
                                onClick={() => addEventFromSelection(event)}
                                variant="outline"
                                size="sm"
                                className="h-7 text-xs"
                              >
                                Add
                              </Button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
                
                {events.map((event, index) => (
                  <Card key={index} className="mb-4">
                    <CardContent className="pt-4">
                      <div className="grid gap-3">
                        <div className="flex justify-between items-center">
                          <h4 className="font-medium">Event {index + 1}</h4>
                          <Button onClick={() => removeEvent(index)} variant="destructive" size="sm">
                            Remove
                          </Button>
                        </div>
                        <Input
                          value={event.title}
                          onChange={(e) => updateEvent(index, 'title', e.target.value)}
                          placeholder="Event title"
                        />
                        <Input
                          value={event.date}
                          onChange={(e) => updateEvent(index, 'date', e.target.value)}
                          placeholder="Event date (e.g., March 15, 2024)"
                        />
                        <Input
                          value={event.location}
                          onChange={(e) => updateEvent(index, 'location', e.target.value)}
                          placeholder="Event location (optional)"
                        />
                        <Textarea
                          value={event.description}
                          onChange={(e) => updateEvent(index, 'description', e.target.value)}
                          placeholder="Event description"
                          rows={2}
                        />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <Button 
                onClick={sendNewsletter} 
                disabled={sending || !subject.trim() || !content.trim()}
                className="w-full"
                size="lg"
              >
                {sending ? 'Sending Newsletter...' : 'Send Newsletter'}
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Newsletter History */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Newsletter History</CardTitle>
              <CardDescription>Recent newsletters sent to subscribers</CardDescription>
            </CardHeader>
            <CardContent>
              {loadingHistory ? (
                <p>Loading history...</p>
              ) : history.length === 0 ? (
                <p className="text-gray-500">No newsletters sent yet.</p>
              ) : (
                <div className="space-y-4">
                  {history.map((newsletter) => (
                    <div key={newsletter.id} className="border rounded-lg p-3">
                      <h4 className="font-medium text-sm mb-2">{newsletter.subject}</h4>
                      <div className="flex flex-wrap gap-1 mb-2">
                        <Badge variant="secondary" className="text-xs">
                          {newsletter.recipientCount} recipients
                        </Badge>
                        <Badge variant="default" className="text-xs">
                          {newsletter.successfulSends} sent
                        </Badge>
                        {newsletter.failedSends > 0 && (
                          <Badge variant="destructive" className="text-xs">
                            {newsletter.failedSends} failed
                          </Badge>
                        )}
                      </div>
                      <p className="text-xs text-gray-500">
                        {new Date(newsletter.sentAt).toLocaleDateString()}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}