import React from "react";
import { describe, it, expect, vi } from "vitest";
import { render, screen } from "@testing-library/react";
import EditButton from "./EditButton";

// Mock Next.js Link component
vi.mock("next/link", () => {
  return {
    default: ({ href, children }: { href: string; children: React.ReactNode }) => (
      <a href={href} data-testid="mock-link">
        {children}
      </a>
    ),
  };
});

// Mock Lucide icons
vi.mock("lucide-react", () => ({
  Pencil: () => <span data-testid="pencil-icon" />,
}));

describe("EditButton", () => {
  it("renders with default label", () => {
    render(<EditButton path="/admin/edit" />);
    
    expect(screen.getByText("Edit")).toBeInTheDocument();
    expect(screen.getByTestId("pencil-icon")).toBeInTheDocument();
    expect(screen.getByTestId("mock-link")).toHaveAttribute("href", "/admin/edit");
  });

  it("renders with custom label", () => {
    render(<EditButton path="/admin/edit" label="Custom Edit" />);
    
    expect(screen.getByText("Custom Edit")).toBeInTheDocument();
    expect(screen.getByTestId("pencil-icon")).toBeInTheDocument();
  });

  it("doesn't render when hidden is true", () => {
    const { container } = render(<EditButton path="/admin/edit" hidden={true} />);
    
    expect(container).toBeEmptyDOMElement();
    expect(screen.queryByText("Edit")).not.toBeInTheDocument();
    expect(screen.queryByTestId("pencil-icon")).not.toBeInTheDocument();
  });
});