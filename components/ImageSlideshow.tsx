"use client";

import {useState, useEffect} from 'react';
import Image from 'next/image';

interface Artwork {
    title: string;
    image: string;
    imageUrl?: string;
    description: string;
    year: string;
    size: string;
}

interface ImageSlideshowProps {
    artworks: Artwork[];
    currentIndex: number;
    isOpen: boolean;
    onClose: () => void;
    showThumbnails?: boolean;
    enableZoom?: boolean;
}

export default function ImageSlideshow({artworks, currentIndex, isOpen, onClose, showThumbnails = true, enableZoom = true}: ImageSlideshowProps) {
    const [index, setIndex] = useState(currentIndex);
    const [isZoomed, setIsZoomed] = useState(false);
    const [isFullscreen, setIsFullscreen] = useState(false);
    
    useEffect(() => {
        if (currentIndex >= 0 && currentIndex < artworks.length)
            setIndex(currentIndex);
    }, [currentIndex, artworks.length]);
    
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Escape')
                onClose();
            else if (e.key === 'ArrowLeft')
                setIndex((prev) => (!prev ? artworks.length - 1 : prev - 1));
            else if (e.key === 'ArrowRight')
                setIndex((prev) => (prev === artworks.length - 1 ? 0 : prev + 1));
        };
        
        if (isOpen)
            document.addEventListener('keydown', handleKeyDown);
        
        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [isOpen, artworks.length, onClose]);

    // Added onClose to dependency array
    if (!isOpen || index < 0 || index >= artworks.length)
        return null;
    
    const artwork = artworks[index];
    
    const toggleZoom = () => {
        if (enableZoom)
            setIsZoomed(!isZoomed);
    };
    
    const toggleFullscreen = () => {
        if (document.fullscreenElement)
            document.exitFullscreen();
        else
            document.documentElement.requestFullscreen();
        
        setIsFullscreen(!isFullscreen);
    };
    
    return (
        <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center">
            <div className="relative w-full h-full max-w-7xl mx-auto p-4">
                {/* Close button */}
                <button
                    onClick={onClose}
                    className="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
                    aria-label="Close slideshow"
                >
                <svg
                    className="w-8 h-8"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                />
            </svg>
            </button>
                <div className="relative h-[80vh] flex flex-col items-center">
                    <div
                            className={`relative w-full h-full transition-transform duration-300 ${isZoomed ? 'scale-150 cursor-zoom-out' : 'cursor-zoom-in'}`}
                            onClick={toggleZoom}
                        >
                        <Image
                            src={artwork.image}
                            alt={artwork.title}
                            fill
                            style={{
                                objectFit: isZoomed ? 'scale-down' : 'contain',
                            }}
                            sizes="100vw"
                            priority
                        />
                    </div>
                    {/* Fullscreen button */}
                    <button
                        onClick={toggleFullscreen}
                        className="absolute top-4 left-4 text-white hover:text-gray-300 z-10"
                        aria-label="Toggle fullscreen"
                    >
                    <svg
                        className="w-8 h-8"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d={isFullscreen ? 'M6 16L16 6M6 6l10 10' : 'M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4'}
                    />
                </svg>
                </button>
                    {/* Text info - positioned below image but above thumbnails on all screens */}
                    <div className="absolute bottom-28 md:bottom-36 left-0 right-0 bg-black/50 text-white p-4 rounded-t-lg max-w-[90vw] mx-auto">
                        <h2 className="text-xl md:text-3xl font-bold mb-2 text-shadow truncate">
                            {artwork.title}
                        </h2>
                        <p className="text-gray-100 mb-2 line-clamp-1 md:line-clamp-2 font-medium">
                            {artwork.description}
                        </p>
                        <div className="text-sm text-gray-300 flex flex-wrap gap-x-4">
                            <span>Year: {artwork.year}</span>
                            <span>Size: {artwork.size}</span>
                        </div>
                    </div>
                    {/* Thumbnail navigation */}
                    {showThumbnails && (
<div className="absolute bottom-8 md:bottom-20 left-0 right-0 overflow-x-auto py-2 px-4">
                        <div className="flex space-x-2 justify-center">
                            {artworks.map((art, i) => (
                            <button
                                key={i}
                                onClick={() => setIndex(i)}
                                className={`w-16 h-16 rounded overflow-hidden border-2 transition-all ${i === index ? 'border-white' : 'border-transparent'}`}
                                title={`View ${art.title}`}
                                aria-label={`View ${art.title}`}
                            >
                                <Image
                                    src={art.image}
                                    alt={art.title}
                                    width={64}
                                    height={64}
                                    className="object-cover w-full h-full"
                                />
                            </button>
                            ))}
                        </div>
                    </div>
                    )}
                    {/* Navigation buttons */}
                    <button
                        onClick={() => setIndex((prev) => (prev === 0 ? artworks.length - 1 : prev - 1))}
                        className="absolute left-4 top-1/2 -translate-y-1/2 text-white hover:text-gray-300 z-10"
                        aria-label="Previous image"
                    >
                    <svg
                        className="w-10 h-10"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 19l-7-7 7-7"
                    />
                </svg>
                </button>
                    <button
                        onClick={() => setIndex((prev) => (prev === artworks.length - 1 ? 0 : prev + 1))}
                        className="absolute right-4 top-1/2 -translate-y-1/2 text-white hover:text-gray-300 z-10"
                        aria-label="Next image"
                    >
                    <svg
                        className="w-10 h-10"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                    />
                </svg>
                </button>
                </div>
            </div>
        </div>
    );
}
