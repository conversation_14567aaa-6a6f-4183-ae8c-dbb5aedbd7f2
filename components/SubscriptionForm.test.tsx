import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { vi, describe, it, beforeEach, expect } from 'vitest';
import { SubscriptionForm } from './SubscriptionForm';
import { useToast } from '@/components/ui/use-toast';

// Mock the useToast hook
vi.mock('@/components/ui/use-toast', () => ({
  useToast: vi.fn(),
}));

// Mock fetch
global.fetch = vi.fn();

describe('SubscriptionForm', () => {
  let mockToast: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    mockToast = vi.fn();
    (useToast as ReturnType<typeof vi.fn>).mockReturnValue({ toast: mockToast });
    (global.fetch as ReturnType<typeof vi.fn>).mockClear();
    vi.clearAllMocks();
  });

  it('renders the subscription form correctly', () => {
    render(<SubscriptionForm />);
    expect(screen.getByPlaceholderText('Your name')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Your email address')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Subscribe' })).toBeInTheDocument();
  });

  it('shows an error toast if name is not provided', async () => {
    render(<SubscriptionForm />);
    fireEvent.change(screen.getByPlaceholderText('Your email address'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Subscribe' }));

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        message: "Name is required.",
        variant: "error",
      });
    });
    expect(global.fetch).not.toHaveBeenCalled();
  });

  it('shows an error toast if email is not provided', async () => {
    render(<SubscriptionForm />);
    fireEvent.change(screen.getByPlaceholderText('Your name'), {
      target: { value: 'Test User' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Subscribe' }));

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        message: "Email address is required.",
        variant: "error",
      });
    });
    expect(global.fetch).not.toHaveBeenCalled();
  });

  it('calls the subscribe API and shows success toast on successful subscription', async () => {
    (global.fetch as ReturnType<typeof vi.fn>).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ message: 'Subscribed successfully!' }),
    });

    render(<SubscriptionForm />);
    fireEvent.change(screen.getByPlaceholderText('Your name'), {
      target: { value: 'Test User' },
    });
    fireEvent.change(screen.getByPlaceholderText('Your email address'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Subscribe' }));

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('http://127.0.0.1:5001/belagallery-9e01b/us-central1/subscribe', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: 'Test User', email: '<EMAIL>' }),
      });
    });

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        message: "Subscribed successfully!",
        variant: "success",
      });
    });
    expect(screen.getByPlaceholderText('Your name')).toHaveValue(''); // Name cleared
    expect(screen.getByPlaceholderText('Your email address')).toHaveValue(''); // Email cleared
  });

  it('shows an error toast if API call fails', async () => {
    (global.fetch as ReturnType<typeof vi.fn>).mockResolvedValueOnce({
      ok: false,
      json: async () => ({ error: 'API Error' }),
    });

    render(<SubscriptionForm />);
    fireEvent.change(screen.getByPlaceholderText('Your name'), {
      target: { value: 'Test User' },
    });
    fireEvent.change(screen.getByPlaceholderText('Your email address'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Subscribe' }));

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledTimes(1);
    });

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        message: "API Error",
        variant: "error",
      });
    });
  });

  it('shows a generic error toast if fetch throws an error', async () => {
    (global.fetch as ReturnType<typeof vi.fn>).mockRejectedValueOnce(new Error('Network error'));

    render(<SubscriptionForm />);
    fireEvent.change(screen.getByPlaceholderText('Your name'), {
      target: { value: 'Test User' },
    });
    fireEvent.change(screen.getByPlaceholderText('Your email address'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Subscribe' }));

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledTimes(1);
    });

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        message: "An error occurred while subscribing. Please try again later.",
        variant: "error",
      });
    });
  });

  it('disables button and shows "Subscribing..." text during API call', async () => {
    // Make fetch resolve slowly to check loading state
    (global.fetch as ReturnType<typeof vi.fn>).mockImplementationOnce(() =>
      new Promise(resolve => setTimeout(() => resolve({
        ok: true,
        json: async () => ({ message: 'Done' }),
      }), 100))
    );

    render(<SubscriptionForm />);
    fireEvent.change(screen.getByPlaceholderText('Your name'), {
      target: { value: 'Test User' },
    });
    fireEvent.change(screen.getByPlaceholderText('Your email address'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Subscribe' }));

    // Check immediately after click
    expect(screen.getByRole('button', { name: 'Subscribing...' })).toBeDisabled();

    await waitFor(() => {
      // After fetch completes
      expect(screen.getByRole('button', { name: 'Subscribe' })).not.toBeDisabled();
    });
  });
});
