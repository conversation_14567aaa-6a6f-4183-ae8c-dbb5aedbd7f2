import { NextPage } from 'next';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';

const SubscribePendingPage: NextPage = () => {
  return (
    <div className="container mx-auto px-4 py-12 flex justify-center items-center min-h-[calc(100vh-200px)]">
      <Card className="w-full max-w-md text-center">
        <CardHeader>
          <CardTitle>Subscription Pending</CardTitle>
          <CardDescription>Check Your Email!</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-6">
            Thank you for subscribing! We've sent a confirmation link to your email address.
            Please click the link in the email to activate your subscription.
          </p>
          <p className="text-sm text-gray-500 mb-4">
            If you don't see the email within a few minutes, please check your spam or junk folder.
          </p>
          <Button asChild>
            <Link href="/">Return to Homepage</Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default SubscribePendingPage;
