import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { SubscriptionForm } from './SubscriptionForm';
import { useToast } from '@/components/ui/use-toast';

// Mock the useToast hook
jest.mock('@/components/ui/use-toast', () => ({
  useToast: jest.fn(),
}));

// Mock fetch
global.fetch = jest.fn();

describe('SubscriptionForm', () => {
  let mockToast: jest.Mock;

  beforeEach(() => {
    mockToast = jest.fn();
    (useToast as jest.Mock).mockReturnValue({ toast: mockToast });
    (global.fetch as jest.Mock).mockClear();
    jest.clearAllMocks();
  });

  it('renders the subscription form correctly', () => {
    render(<SubscriptionForm />);
    expect(screen.getByPlaceholderText('Enter your email address')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Subscribe' })).toBeInTheDocument();
  });

  it('shows an error toast if email is not provided', async () => {
    render(<SubscriptionForm />);
    fireEvent.click(screen.getByRole('button', { name: 'Subscribe' }));

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Email address is required.",
        variant: "destructive",
      });
    });
    expect(global.fetch).not.toHaveBeenCalled();
  });

  it('calls the subscribe API and shows success toast on successful subscription', async () => {
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ message: 'Subscribed successfully!' }),
    });

    render(<SubscriptionForm />);
    fireEvent.change(screen.getByPlaceholderText('Enter your email address'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Subscribe' }));

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('http://127.0.0.1:5001/belagallery-9e01b/us-central1/subscribe', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: '', email: '<EMAIL>' }),
      });
    });

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: "Subscription Submitted!",
        description: "Subscribed successfully!",
      });
    });
    expect(screen.getByPlaceholderText('Enter your email address')).toHaveValue(''); // Email cleared
  });

  it('shows an error toast if API call fails', async () => {
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      json: async () => ({ error: 'API Error' }),
    });

    render(<SubscriptionForm />);
    fireEvent.change(screen.getByPlaceholderText('Enter your email address'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Subscribe' }));

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledTimes(1);
    });

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: "Subscription Failed",
        description: "API Error",
        variant: "destructive",
      });
    });
  });

  it('shows a generic error toast if fetch throws an error', async () => {
    (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

    render(<SubscriptionForm />);
    fireEvent.change(screen.getByPlaceholderText('Enter your email address'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Subscribe' }));

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledTimes(1);
    });

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: "Subscription Error",
        description: "An error occurred while subscribing. Please try again later.",
        variant: "destructive",
      });
    });
  });

  it('disables button and shows "Subscribing..." text during API call', async () => {
    // Make fetch resolve slowly to check loading state
    (global.fetch as jest.Mock).mockImplementationOnce(() =>
      new Promise(resolve => setTimeout(() => resolve({
        ok: true,
        json: async () => ({ message: 'Done' }),
      }), 100))
    );

    render(<SubscriptionForm />);
    fireEvent.change(screen.getByPlaceholderText('Enter your email address'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.click(screen.getByRole('button', { name: 'Subscribe' }));

    // Check immediately after click
    expect(screen.getByRole('button', { name: 'Subscribing...' })).toBeDisabled();

    await waitFor(() => {
      // After fetch completes
      expect(screen.getByRole('button', { name: 'Subscribe' })).not.toBeDisabled();
    });
  });
});
