// functions/__tests__/audit.test.ts
import * as admin from 'firebase-admin';
import { 
  logAuditEvent, 
  getVersionAuditLogs, 
  getRecentAuditLogs, 
  AuditEntry 
} from '../../src/audit';

// Import setup to ensure mocks are initialized
import './setup';

describe('Audit Functions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Set default environment for tests
    process.env.NODE_ENV = 'test-environment';
  });

  afterEach(() => {
    // Clean up environment variables
    delete process.env.NODE_ENV;
  });

  describe('logAuditEvent', () => {
    it('should log an audit entry with correct details and override timestamp', async () => {
      // Setup
      const originalTimestamp = new Date('2023-01-01T00:00:00.000Z');
      const auditEntry = {
        event: 'test_event',
        details: { info: 'some test info' },
        versionId: 'v1.0.0',
        timestamp: originalTimestamp, // This should be overridden
      } as unknown as AuditEntry;

      const mockAdd = jest.fn().mockResolvedValue({ id: 'test-log-id' });
      const mockCollection = jest.spyOn(admin.firestore(), 'collection')
        .mockReturnValue({ add: mockAdd } as any);

      // Execute
      await logAuditEvent(auditEntry);

      // Verify
      expect(mockCollection).toHaveBeenCalledWith('audit_log');
      expect(mockAdd).toHaveBeenCalledTimes(1);
      
      const calledWith = mockAdd.mock.calls[0][0];
      expect(calledWith.event).toBe('test_event');
      expect(calledWith.details).toEqual({ info: 'some test info' });
      expect(calledWith.versionId).toBe('v1.0.0');
      expect(calledWith.environment).toBe('test-environment');
      expect(calledWith.timestamp).toBeInstanceOf(Date);
      expect(calledWith.timestamp.getTime()).not.toBe(originalTimestamp.getTime());
      // Check if the timestamp is recent (within the last 5 seconds)
      expect(new Date().getTime() - calledWith.timestamp.getTime()).toBeLessThan(5000);
    });

    it('should use "development" for environment if NODE_ENV is not set', async () => {
      // Setup
      delete process.env.NODE_ENV;
      const auditEntry = {
        event: 'dev_event',
        details: { info: 'dev info' },
      } as unknown as AuditEntry;
      
      const mockAdd = jest.fn().mockResolvedValue({ id: 'dev-log-id' });
      jest.spyOn(admin.firestore(), 'collection')
        .mockReturnValue({ add: mockAdd } as any);

      // Execute
      await logAuditEvent(auditEntry);

      // Verify
      expect(mockAdd).toHaveBeenCalledTimes(1);
      const calledWith = mockAdd.mock.calls[0][0];
      expect(calledWith.environment).toBe('development');
    });
  });

  describe('getVersionAuditLogs', () => {
    it('should return an empty array if no logs are found for the version', async () => {
      // Setup
      const mockGet = jest.fn().mockResolvedValue({ empty: true, docs: [] });
      const mockWhere = jest.fn().mockReturnThis();
      const mockOrderBy = jest.fn().mockReturnThis();
      
      jest.spyOn(admin.firestore(), 'collection')
        .mockReturnValue({ 
          where: mockWhere,
          orderBy: mockOrderBy,
          get: mockGet
        } as any);

      // Execute
      const logs = await getVersionAuditLogs('v1.0.0');

      // Verify
      expect(logs).toEqual([]);
      expect(admin.firestore().collection).toHaveBeenCalledWith('audit_log');
      expect(mockWhere).toHaveBeenCalledWith('version', '==', 'v1.0.0');
      expect(mockOrderBy).toHaveBeenCalledWith('timestamp', 'desc');
      expect(mockGet).toHaveBeenCalledTimes(1);
    });

    it('should return mapped audit logs for a version', async () => {
      // Setup
      const timestamp1 = new Date();
      const timestamp2 = new Date();
      const mockDocs = [
        { 
          id: 'log1', 
          data: () => ({ event: 'event1', timestamp: timestamp1 }) 
        },
        { 
          id: 'log2', 
          data: () => ({ event: 'event2', timestamp: timestamp2 }) 
        },
      ];
      
      const mockGet = jest.fn().mockResolvedValue({ empty: false, docs: mockDocs });
      const mockWhere = jest.fn().mockReturnThis();
      const mockOrderBy = jest.fn().mockReturnThis();
      
      jest.spyOn(admin.firestore(), 'collection')
        .mockReturnValue({ 
          where: mockWhere,
          orderBy: mockOrderBy,
          get: mockGet
        } as any);

      // Execute
      const logs = await getVersionAuditLogs('v1.0.1');

      // Verify
      expect(logs).toHaveLength(2);
      expect(logs[0].id).toBe('log1');
      // Use type assertion to fix TypeScript errors
      expect((logs[0] as any).event).toBe('event1');
      expect((logs[0] as any).timestamp).toBe(timestamp1);
      expect(logs[1].id).toBe('log2');
      expect((logs[1] as any).event).toBe('event2');
      expect((logs[1] as any).timestamp).toBe(timestamp2);
      expect(admin.firestore().collection).toHaveBeenCalledWith('audit_log');
      expect(mockWhere).toHaveBeenCalledWith('version', '==', 'v1.0.1');
      expect(mockOrderBy).toHaveBeenCalledWith('timestamp', 'desc');
    });
  });

  describe('getRecentAuditLogs', () => {
    it('should return an empty array if no recent logs are found', async () => {
      // Setup
      const mockGet = jest.fn().mockResolvedValue({ empty: true, docs: [] });
      const mockOrderBy = jest.fn().mockReturnThis();
      const mockLimit = jest.fn().mockReturnThis();
      
      jest.spyOn(admin.firestore(), 'collection')
        .mockReturnValue({ 
          orderBy: mockOrderBy,
          limit: mockLimit,
          get: mockGet
        } as any);

      // Execute
      const logs = await getRecentAuditLogs();

      // Verify
      expect(logs).toEqual([]);
      expect(admin.firestore().collection).toHaveBeenCalledWith('audit_log');
      expect(mockOrderBy).toHaveBeenCalledWith('timestamp', 'desc');
      expect(mockLimit).toHaveBeenCalledWith(20); // Default limit
      expect(mockGet).toHaveBeenCalledTimes(1);
    });

    it('should return recent audit logs with default limit 20', async () => {
      // Setup
      const mockDocs = Array.from({ length: 5 }, (_, i) => ({
        id: `log${i}`,
        data: () => ({ event: `event${i}`, timestamp: new Date() }),
      }));
      
      const mockGet = jest.fn().mockResolvedValue({ empty: false, docs: mockDocs });
      const mockOrderBy = jest.fn().mockReturnThis();
      const mockLimit = jest.fn().mockReturnThis();
      
      jest.spyOn(admin.firestore(), 'collection')
        .mockReturnValue({ 
          orderBy: mockOrderBy,
          limit: mockLimit,
          get: mockGet
        } as any);

      // Execute
      const logs = await getRecentAuditLogs();

      // Verify
      expect(logs).toHaveLength(5);
      expect(logs[0].id).toBe('log0');
      expect(admin.firestore().collection).toHaveBeenCalledWith('audit_log');
      expect(mockOrderBy).toHaveBeenCalledWith('timestamp', 'desc');
      expect(mockLimit).toHaveBeenCalledWith(20);
    });

    it('should return recent audit logs with custom limit', async () => {
      // Setup
      const mockDocs = Array.from({ length: 3 }, (_, i) => ({
        id: `log${i}`,
        data: () => ({ event: `event${i}`, timestamp: new Date() }),
      }));
      
      const mockGet = jest.fn().mockResolvedValue({ empty: false, docs: mockDocs });
      const mockOrderBy = jest.fn().mockReturnThis();
      const mockLimit = jest.fn().mockReturnThis();
      
      jest.spyOn(admin.firestore(), 'collection')
        .mockReturnValue({ 
          orderBy: mockOrderBy,
          limit: mockLimit,
          get: mockGet
        } as any);

      // Execute
      const logs = await getRecentAuditLogs(3);

      // Verify
      expect(logs).toHaveLength(3);
      expect(logs[0].id).toBe('log0');
      expect(admin.firestore().collection).toHaveBeenCalledWith('audit_log');
      expect(mockOrderBy).toHaveBeenCalledWith('timestamp', 'desc');
      expect(mockLimit).toHaveBeenCalledWith(3);
    });

    it('should correctly map returned documents for recent logs', async () => {
      // Setup
      const timestamp = new Date();
      const details = { key: 'value' };
      const mockDocs = [
        { 
          id: 'logXYZ', 
          data: () => ({ 
            event: 'specific_event', 
            details, 
            timestamp 
          }) 
        }
      ];
      
      const mockGet = jest.fn().mockResolvedValue({ empty: false, docs: mockDocs });
      const mockOrderBy = jest.fn().mockReturnThis();
      const mockLimit = jest.fn().mockReturnThis();
      
      jest.spyOn(admin.firestore(), 'collection')
        .mockReturnValue({ 
          orderBy: mockOrderBy,
          limit: mockLimit,
          get: mockGet
        } as any);

      // Execute
      const logs = await getRecentAuditLogs(1);

      // Verify
      expect(logs).toHaveLength(1);
      expect(logs[0].id).toBe('logXYZ');
      // Use type assertion to fix TypeScript errors
      expect((logs[0] as any).event).toBe('specific_event');
      expect((logs[0] as any).details).toBe(details);
      expect((logs[0] as any).timestamp).toBe(timestamp);
      expect(admin.firestore().collection).toHaveBeenCalledWith('audit_log');
      expect(mockOrderBy).toHaveBeenCalledWith('timestamp', 'desc');
      expect(mockLimit).toHaveBeenCalledWith(1);
    });
  });
});