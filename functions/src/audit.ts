import * as admin from 'firebase-admin';

if (!admin.apps.length)
    admin.initializeApp();

const db = admin.firestore();

/**
 * Interface for audit log entries
 */
export interface AuditEntry {
    action: 'publish' | 'rollback' | 'content_edit';
    user: string;
    version?: string;
    timestamp: Date;
    changes: string[];
    metadata?: Record<string, any>;
}

/**
 * Logs an audit event to Firestore
 */
export const logAuditEvent = async (entry: AuditEntry) => {
    // Use current date instead of server timestamp
    const now = new Date();
    
    await db
        .collection('audit_log')
        .add({
            ...entry,
            environment: process.env.NODE_ENV || 'development',
            timestamp: now,
        });
};

/**
 * Retrieves audit logs for a specific version
 */
export const getVersionAuditLogs = async (versionId: string) => {
    const snapshot = await db
        .collection('audit_log')
        .where('version', '==', versionId)
        .orderBy('timestamp', 'desc')
        .get();
    
    return snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
    }));
};

/**
 * Retrieves recent audit logs
 */
export const getRecentAuditLogs = async (limit = 20) => {
    const snapshot = await db
        .collection('audit_log')
        .orderBy('timestamp', 'desc')
        .limit(limit)
        .get();
    
    return snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
    }));
};
