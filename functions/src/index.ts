import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import { publishFunction, rollbackFunction } from "./publish";
import { listVersions, deleteVersion } from "./backup";

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp();
}

// Export functions
export const publish = publishFunction;
export const rollback = rollbackFunction;

// Version management functions
export const listVersionsFunction = functions.https.onRequest(
  async (req, res) => {
    // Enable CORS
    res.set("Access-Control-Allow-Origin", "*");
    res.set("Access-Control-Allow-Methods", "POST, OPTIONS");
    res.set("Access-Control-Allow-Headers", "Content-Type");

    // Handle preflight OPTIONS request
    if (req.method === "OPTIONS") {
      res.status(204).send("");
      return;
    }

    // Only allow POST
    if (req.method !== "POST") {
      res.status(405).send("Method Not Allowed");
      return;
    }

    try {
      const versions = await listVersions();
      res.status(200).json({
        success: true,
        versions,
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: (error as Error).message,
      });
    }
  }
);

export const deleteVersionFunction = functions.https.onRequest(
  async (req, res) => {
    // Enable CORS
    res.set("Access-Control-Allow-Origin", "*");
    res.set("Access-Control-Allow-Methods", "POST, OPTIONS");
    res.set("Access-Control-Allow-Headers", "Content-Type");

    // Handle preflight OPTIONS request
    if (req.method === "OPTIONS") {
      res.status(204).send("");
      return;
    }

    // Only allow POST
    if (req.method !== "POST") {
      res.status(405).send("Method Not Allowed");
      return;
    }

    try {
      const { versionId } = req.body.data || {};

      if (!versionId) {
        res.status(400).json({
          success: false,
          error: "Version ID is required",
        });
        return;
      }

      await deleteVersion(versionId);
      res.status(200).json({
        success: true,
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: (error as Error).message,
      });
    }
  }
);