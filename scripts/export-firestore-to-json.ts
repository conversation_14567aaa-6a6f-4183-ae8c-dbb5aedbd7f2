import 'dotenv/config';
import { config } from 'dotenv';
import fs from 'fs/promises';
import path from 'path';
import { initializeApp, applicationDefault } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';

// Load environment variables
config({
  path: '.env.local',
});

// Use emulator if NEXT_PUBLIC_FIREBASE_USE_EMULATOR is set to true
if (process.env.NEXT_PUBLIC_FIREBASE_USE_EMULATOR === 'true') {
  process.env.FIRESTORE_EMULATOR_HOST = process.env.FIRESTORE_EMULATOR_HOST || 'localhost:8080';
}

// Ensure the Admin SDK uses the same project ID as the emulator/app
process.env.GCLOUD_PROJECT = process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'belagallery-9e01b';
process.env.FIREBASE_CONFIG = JSON.stringify({
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'belagallery-9e01b',
});

// Initialize Firebase Admin SDK
initializeApp({
  credential: applicationDefault(),
});

const db = getFirestore();

async function exportFirestoreToJson() {
  try {
    console.log('🔥 Starting Firestore data export...');
    
    const liveCollection = db.collection('live');
    const snapshot = await liveCollection.get();
    
    // Get output directory from command line argument or default to 'data'
    const outputDir = process.argv.includes('--output')
      ? process.argv[process.argv.indexOf('--output') + 1]
      : 'data';
    const dataDir = path.join(process.cwd(), outputDir, 'data');
    await fs.mkdir(dataDir, { recursive: true });
    
    console.log(`📄 Found ${snapshot.size} documents in 'live' collection`);
    
    // Export each document as a separate JSON file
    for (const doc of snapshot.docs) {
      const docId = doc.id;
      const docData = doc.data();
      
      const filePath = path.join(dataDir, `${docId}.json`);
      await fs.writeFile(filePath, JSON.stringify(docData, null, 2));
      
      console.log(`✅ Exported: ${docId}.json`);
    }
    
    console.log('🎉 Firestore data export completed successfully!');
  } catch (error) {
    console.error('❌ Firestore data export failed:', error);
    process.exit(1);
  }
}

// Run the export
exportFirestoreToJson().catch(console.error);