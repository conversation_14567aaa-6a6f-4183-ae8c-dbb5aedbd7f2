import { GET } from './route';
import { NextRequest } from 'next/server';
import { adminDb } from '@/lib/firebase-admin';

jest.mock('@/lib/firebase-admin', () => ({
  adminDb: {
    collection: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    get: jest.fn(),
  },
}));

const originalEnv = process.env;
beforeEach(() => {
  process.env = { ...originalEnv, NEXT_PUBLIC_BASE_URL: 'http://localhost:3000' };
  jest.clearAllMocks();
});
afterEach(() => {
  process.env = originalEnv;
});

describe('GET /api/unsubscribe/[token]', () => {
  it('should unsubscribe a user and redirect to success', async () => {
    const mockUpdate = jest.fn().mockResolvedValue(undefined);
    (adminDb.get as jest.Mock).mockResolvedValueOnce({
      empty: false,
      docs: [{
        id: 'doc-id',
        data: () => ({ confirmationToken: 'test-token', isConfirmed: true }), // Assumed they were confirmed
        ref: { update: mockUpdate },
      }],
    });

    const request = new NextRequest('http://localhost/api/unsubscribe/test-token');
    const response = await GET(request, { params: { token: 'test-token' } });

    expect(response.status).toBe(307); // Redirect status
    expect(response.headers.get('Location')).toBe('http://localhost:3000/unsubscribe/success');
    expect(adminDb.collection).toHaveBeenCalledWith('subscribers');
    expect(adminDb.where).toHaveBeenCalledWith('confirmationToken', '==', 'test-token');
    expect(mockUpdate).toHaveBeenCalledWith(expect.objectContaining({
      isConfirmed: false,
      unsubscribedAt: expect.any(Date),
    }));
  });

  it('should redirect to already-unsubscribed if already unsubscribed', async () => {
     const mockUpdate = jest.fn(); // Should not be called
    (adminDb.get as jest.Mock).mockResolvedValueOnce({
      empty: false,
      docs: [{
        data: () => ({ confirmationToken: 'test-token', unsubscribedAt: new Date() }),
        ref: { update: mockUpdate }
      }],
    });
    const request = new NextRequest('http://localhost/api/unsubscribe/test-token');
    const response = await GET(request, { params: { token: 'test-token' } });
    expect(response.status).toBe(307);
    expect(response.headers.get('Location')).toBe('http://localhost:3000/unsubscribe/already-unsubscribed');
    expect(mockUpdate).not.toHaveBeenCalled();
  });

  it('should redirect to invalid-token if token is not found', async () => {
    (adminDb.get as jest.Mock).mockResolvedValueOnce({ empty: true, docs: [] });
    const request = new NextRequest('http://localhost/api/unsubscribe/invalid-token');
    const response = await GET(request, { params: { token: 'invalid-token' } });
    expect(response.status).toBe(307);
    expect(response.headers.get('Location')).toBe('http://localhost:3000/unsubscribe/invalid-token');
  });

  it('should redirect to error page on Firestore error', async () => {
    (adminDb.get as jest.Mock).mockRejectedValueOnce(new Error('Firestore error'));
    const request = new NextRequest('http://localhost/api/unsubscribe/error-token');
    const response = await GET(request, { params: { token: 'error-token' } });
    expect(response.status).toBe(307);
    expect(response.headers.get('Location')).toContain('/unsubscribe/error?message=');
  });
});
