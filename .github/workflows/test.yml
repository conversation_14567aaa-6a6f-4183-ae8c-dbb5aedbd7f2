name: Run Tests

on:
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Type of tests to run'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - unit
          - integration
          - e2e

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        cache: 'npm'
    
    - name: Set up Java for Firebase Emulator
      uses: actions/setup-java@v3
      with:
        distribution: 'temurin'
        java-version: '17'
    
    - name: Install system dependencies
      run: |
        apt-get update && apt-get install -y lsof
        
    - name: Install npm dependencies
      run: |
        npm ci
        npx playwright install chromium --with-deps
        cd functions && npm ci && cd ..
    
    - name: Create .env.local file
      run: |
        echo "${{ secrets.ENV_LOCAL_FILE }}" > .env.local
      env:
        ENV_LOCAL_FILE: ${{ secrets.ENV_LOCAL_FILE }}
    
    - name: Run tests with coverage
      run: |
        if [ "${{ github.event.inputs.test_type }}" = "all" ]; then
          npm run coverage:all
        elif [ "${{ github.event.inputs.test_type }}" = "unit" ]; then
          npm run coverage:unit
        elif [ "${{ github.event.inputs.test_type }}" = "integration" ]; then
          npm run coverage:integration
        elif [ "${{ github.event.inputs.test_type }}" = "e2e" ]; then
          npm run coverage:e2e
        fi

    - name: Upload coverage reports
      if: ${{ !env.ACT }}
      uses: actions/upload-artifact@v3
      with:
        name: coverage-report
        path: coverage/
        retention-days: 30
        
    - name: Show coverage report location (local run)
      if: ${{ env.ACT }}
      run: |
        echo "Coverage reports are available in:"
        echo "- Unit tests: coverage/unit/"
        echo "- E2E & Integration tests: coverage/playwright/"
        echo "- Functions tests: coverage/functions/"
        echo "- Merged report: coverage/report/"
        
    - name: Report Coverage
      if: always() && github.event_name == 'pull_request'
      uses: romeovs/lcov-reporter-action@v0.3.1
      with:
        lcov-file: ./coverage/merged/lcov.info
        github-token: ${{ secrets.GITHUB_TOKEN }}