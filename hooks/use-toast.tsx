"use client";

import React from "react";
import { useState, useCallback, ReactNode } from "react";
import { Toast, ToastProps } from "@/components/ui/toast";

export interface UseToastReturn {
  toast: ReactNode;
  showToast: (
    message: string,
    options?: Partial<Omit<ToastProps, "message" | "onClose">>
  ) => void;
  hideToast: () => void;
}

export function useToast(): UseToastReturn {
  const [toastState, setToastState] = useState<Omit<
    ToastProps,
    "onClose"
  > | null>(null);

  const showToast = useCallback(
    (
      message: string,
      options?: Partial<Omit<ToastProps, "message" | "onClose">>
    ) => {
      setToastState({
        message,
        ...options,
      });
    },
    []
  );

  const hideToast = useCallback(() => {
    setToastState(null);
  }, []);

  const toast = toastState ? (
    <Toast {...toastState} onClose={hideToast} />
  ) : null;

  return {
    toast,
    showToast,
    hideToast,
  };
}
