import {initializeApp} from 'firebase/app';
import {
    getStorage,
    connectStorageEmulator,
} from 'firebase/storage';
import * as dotenv from 'dotenv';

dotenv.config({
    path: '.env.local',
});

const config = {
    apiKey: 'demo-key-no-auth-needed',
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
};

if (!config.projectId || !config.storageBucket)
    throw Error('Missing required environment variables. Check .env.local file.');

const app = initializeApp(config, 'storage-migration');
export const storage = getStorage(app);

// Always connect to Storage emulator for migration
connectStorageEmulator(storage, 'localhost', 9199);
