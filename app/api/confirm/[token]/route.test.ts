import { GET } from './route';
import { NextRequest, NextResponse } from 'next/server';
import { adminDb } from '@/lib/firebase-admin';

jest.mock('@/lib/firebase-admin', () => ({
  adminDb: {
    collection: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    get: jest.fn(),
  },
}));

const originalEnv = process.env;
beforeEach(() => {
  process.env = { ...originalEnv, NEXT_PUBLIC_BASE_URL: 'http://localhost:3000' };
  jest.clearAllMocks();
});
afterEach(() => {
  process.env = originalEnv;
});

describe('GET /api/confirm/[token]', () => {
  it('should confirm a subscription and redirect to success', async () => {
    const mockUpdate = jest.fn().mockResolvedValue(undefined);
    (adminDb.get as jest.Mock).mockResolvedValueOnce({
      empty: false,
      docs: [{
        id: 'doc-id',
        data: () => ({ isConfirmed: false, confirmationToken: 'test-token' }),
        ref: { update: mockUpdate },
      }],
    });

    const request = new NextRequest('http://localhost/api/confirm/test-token');
    const response = await GET(request, { params: { token: 'test-token' } });

    expect(response.status).toBe(307); // Redirect status
    expect(response.headers.get('Location')).toBe('http://localhost:3000/confirm/success');
    expect(adminDb.collection).toHaveBeenCalledWith('subscribers');
    expect(adminDb.where).toHaveBeenCalledWith('confirmationToken', '==', 'test-token');
    expect(mockUpdate).toHaveBeenCalledWith(expect.objectContaining({
      isConfirmed: true,
    }));
  });

  it('should redirect to already-confirmed if already confirmed', async () => {
    (adminDb.get as jest.Mock).mockResolvedValueOnce({
      empty: false,
      docs: [{
        data: () => ({ isConfirmed: true, confirmationToken: 'test-token' }),
      }],
    });
    const request = new NextRequest('http://localhost/api/confirm/test-token');
    const response = await GET(request, { params: { token: 'test-token' } });
    expect(response.headers.get('Location')).toBe('http://localhost:3000/confirm/already-confirmed');
  });

  it('should redirect to error page if token is invalid', async () => {
    (adminDb.get as jest.Mock).mockResolvedValueOnce({ empty: true, docs: [] });
    const request = new NextRequest('http://localhost/api/confirm/invalid-token');
    // For some reason, NextResponse.json().status is not being set in the test when an error is returned directly
    // But a redirect is always a 307 or similar. Here the API returns JSON, not redirect.
    // The test environment for NextRequest might not fully replicate NextResponse behavior for direct JSON returns.
    // Let's test the redirect path for errors as per current code.
    // The API route actually redirects on error in the catch block.
    // If snapshot.empty, it returns NextResponse.json, not a redirect. This needs adjustment in test or API.
    // For now, let's assume the API is changed to redirect on "Invalid or expired token" for consistency.
    // Or, we test the JSON response if that's the intended behavior.
    // The current code returns JSON for "Invalid or expired token":
    // return NextResponse.json({ error: 'Invalid or expired token' }, { status: 404 });
    // Let's test that for now.

    const response = await GET(request, { params: { token: 'invalid-token' } });
    // const body = await response.json(); // This line causes issues in test if it's a redirect
    // If it is a JSON response:
    expect(response.status).toBe(404);
    const body = await response.json();
    expect(body.error).toBe('Invalid or expired token');
  });


  it('should redirect to error page on Firestore error', async () => {
    (adminDb.get as jest.Mock).mockRejectedValueOnce(new Error('Firestore error'));
    const request = new NextRequest('http://localhost/api/confirm/error-token');
    const response = await GET(request, { params: { token: 'error-token' } });
    expect(response.status).toBe(307); // Redirect
    expect(response.headers.get('Location')).toContain('/confirm/error?message=');
  });
});
