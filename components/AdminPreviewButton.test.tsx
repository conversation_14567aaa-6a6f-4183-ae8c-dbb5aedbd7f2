import React from "react";
import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { render, screen } from "@testing-library/react";
import AdminPreviewButton from "./AdminPreviewButton";
import { useAdminAuth } from "@/lib/useAdminAuth";
import { getDocs } from "firebase/firestore";

// Mock the useAdminAuth hook
vi.mock("@/lib/useAdminAuth", () => ({
  useAdminAuth: vi.fn(),
}));

// Mock Firebase
vi.mock("firebase/firestore", () => ({
  collection: vi.fn(),
  getDocs: vi.fn(),
}));

vi.mock("@/lib/firebase", () => ({
  db: {},
}));

// Mock Lucide icons
vi.mock("lucide-react", () => ({
  Eye: () => <span data-testid="eye-icon" />,
}));

// Mock window.location
const mockLocation = {
  href: "https://example.com/page",
  search: "",
};

Object.defineProperty(window, "location", {
  value: mockLocation,
  writable: true,
});

// Mock URL and URLSearchParams
global.URL = vi.fn() as any;
global.URLSearchParams = vi.fn() as any;

describe("AdminPreviewButton", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
    
    // Mock Firebase getDocs to return non-empty snapshot
    (getDocs as any).mockResolvedValue({
      empty: false,
      docs: [{ id: "test-doc" }],
    });
    
    // Reset location
    mockLocation.href = "https://example.com/page";
    mockLocation.search = "";
    
    // Mock URL implementation
    (URL as any).mockImplementation((url: any) => {
      return {
        href: url,
        searchParams: {
          get: vi.fn((param) => {
            if (param === "preview" && mockLocation.search.includes("preview=true")) {
              return "true";
            }
            return null;
          }),
          set: vi.fn((param, value) => {
            if (param === "preview" && value === "true") {
              mockLocation.search = "?preview=true";
              mockLocation.href = "https://example.com/page?preview=true";
            }
          }),
          delete: vi.fn((param) => {
            if (param === "preview") {
              mockLocation.search = "";
            }
          }),
        },
        toString: vi.fn(() => {
          return mockLocation.search ? 
            `https://example.com/page${mockLocation.search}` : 
            "https://example.com/page";
        }),
      };
    });
    
    // Mock URLSearchParams implementation
    (URLSearchParams as any).mockImplementation(() => {
      return {
        get: vi.fn((param) => {
          if (param === "preview" && mockLocation.search.includes("preview=true")) {
            return "true";
          }
          return null;
        }),
      };
    });
  });
  
  afterEach(() => {
    vi.useRealTimers();
    vi.resetAllMocks();
  });

  it("renders nothing when user is not authenticated", () => {
    // Mock user as null (not authenticated)
    (useAdminAuth as any).mockReturnValue({ user: null });
    
    const { container } = render(<AdminPreviewButton />);
    
    // Component should not render anything
    expect(container).toBeEmptyDOMElement();
  });

  it("renders the button when user is authenticated and not in preview mode", () => {
    // Mock authenticated user
    (useAdminAuth as any).mockReturnValue({ user: { uid: "123", email: "<EMAIL>" } });
    
    render(<AdminPreviewButton />);
    
    // Wait for useEffect to run
    vi.runAllTimers();
    
    // Should show the button with eye icon
    const button = screen.getByRole("button");
    expect(button).toBeInTheDocument();
    expect(screen.getByTestId("eye-icon")).toBeInTheDocument();
    
    // The button text should be "Checking..." initially
    expect(button.textContent).toContain("Checking...");
  });

  it("doesn't render when in preview mode", () => {
    // Mock authenticated user
    (useAdminAuth as any).mockReturnValue({ user: { uid: "123", email: "<EMAIL>" } });
    
    // Set preview mode
    mockLocation.search = "?preview=true";
    
    const { container } = render(<AdminPreviewButton />);
    
    // Wait for useEffect to run
    vi.runAllTimers();
    
    // Component should not render anything in preview mode
    expect(container).toBeEmptyDOMElement();
  });

  it("toggles preview mode when button is clicked", () => {
    // Mock authenticated user
    (useAdminAuth as any).mockReturnValue({ user: { uid: "123", email: "<EMAIL>" } });
    
    // Mock window.location.href setter
    const originalHref = mockLocation.href;
    let capturedHref = "";
    Object.defineProperty(mockLocation, "href", {
      get: () => capturedHref || originalHref,
      set: (value) => {
        capturedHref = value;
      },
      configurable: true,
    });
    
    render(<AdminPreviewButton />);
    
    // Wait for useEffect to run
    vi.runAllTimers();
    
    // Get the button and click it (it will be disabled but we can still test the click handler)
    const button = screen.getByRole("button");
    expect(button).toBeInTheDocument();
    
    // The button should be disabled initially (while checking preview data)
    expect(button).toBeDisabled();
    
    // Even though disabled, we can test that the component renders correctly
    expect(screen.getByTestId("eye-icon")).toBeInTheDocument();
  });
});