"use client"; // To read query parameters

import { NextPage } from 'next';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { Suspense } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle } from 'lucide-react'; // Using an icon

const ErrorContent = () => {
  const searchParams = useSearchParams();
  const errorMessage = searchParams.get('message');

  return (
    <div className="container mx-auto px-4 py-12 flex justify-center items-center min-h-[calc(100vh-200px)]">
      <Card className="w-full max-w-md text-center">
        <CardHeader className="items-center">
          <AlertTriangle className="w-12 h-12 text-red-500 mb-2" />
          <CardTitle>Confirmation Error</CardTitle>
          <CardDescription>Something went wrong.</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-6">
            We encountered an error while trying to confirm your email address.
            {errorMessage && <span className="block mt-2 text-sm text-red-600">Error: {errorMessage}</span>}
          </p>
          <p className="text-sm text-gray-500 mb-4">
            Please try again later or contact support if the issue persists.
          </p>
          <Button asChild>
            <Link href="/">Return to Homepage</Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

const ConfirmationErrorPage: NextPage = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ErrorContent />
    </Suspense>
  );
};

export default ConfirmationErrorPage;
