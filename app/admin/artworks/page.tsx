"use client";

import { useState, useEffect } from "react";
import { Artwork } from "@/types/artworks";
import { useArtworks } from "@/hooks/use-artwork";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { AddNewArtworkDialog } from "@/components/admin";
import { ArtworkGrid } from "@/components/artwork";
import { deleteArtwork } from "@/lib/artwork-repository";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Plus,
  Search,
  Filter,
  Grid,
  List,
  Edit,
  Trash2,
  ChevronDown,
  ChevronUp,
  X,
  Loader2,
} from "lucide-react";
import { useImageResolver } from "@/hooks/use-image-resolver";

const isUndefined = (a: unknown): a is undefined => typeof a === "undefined";

export default function ArtworksPage() {
  // Fetch artworks with pagination
  const {
    artworks,
    isLoading,
    error,
    pagination: { hasMore, loadMore, isLoadingMore, totalCount },
  } = useArtworks({
    pageSize: 12,
  });

  // UI state
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingArtwork, setEditingArtwork] = useState<Artwork | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<
    "title" | "year" | "createdAt" | "updatedAt"
  >("title");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [filterCategory, setFilterCategory] = useState<string>("");
  const [filterTags, setFilterTags] = useState<string[]>([]);

  // Image resolution
  const { resolvedUrls, resolveImages } = useImageResolver();

  // Resolve all artwork images when artworks change
  useEffect(() => {
    if (artworks.length > 0) {
      const imagePaths = artworks
        .map((artwork) => artwork.image)
        .filter(Boolean);
      resolveImages(imagePaths);
    }
  }, [artworks, resolveImages]);

  // Derived state
  const categories = [...new Set(artworks.map((artwork) => artwork.category))]
    .filter(Boolean)
    .sort();

  const allTags = [
    ...new Set(artworks.flatMap((artwork) => artwork.tags || [])),
  ]
    .filter(Boolean)
    .sort();

  // Filter and sort artworks
  const filteredArtworks = artworks
    .filter((artwork) => {
      // Search term filter
      if (
        searchTerm &&
        !artwork.title.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !artwork.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
        return false;

      // Category filter
      if (
        filterCategory &&
        filterCategory !== "all-categories" &&
        artwork.category !== filterCategory
      )
        return false;

      // Tags filter
      return (
        filterTags.length <= 0 ||
        filterTags.some((tag) => artwork.tags?.includes(tag))
      );
    })
    .sort((a, b) => {
      // Handle sorting
      if (sortBy === "title")
        return sortDirection === "asc"
          ? a.title.localeCompare(b.title)
          : b.title.localeCompare(a.title);

      if (sortBy === "year")
        return sortDirection === "asc"
          ? a.year.localeCompare(b.year)
          : b.year.localeCompare(a.year);

      if (sortBy === "createdAt") {
        const aDate = a.createdAt ? new Date(a.createdAt).getTime() : 0;
        const bDate = b.createdAt ? new Date(b.createdAt).getTime() : 0;

        return sortDirection === "asc" ? aDate - bDate : bDate - aDate;
      }

      if (sortBy === "updatedAt") {
        const aDate = a.updatedAt ? new Date(a.updatedAt).getTime() : 0;
        const bDate = b.updatedAt ? new Date(b.updatedAt).getTime() : 0;

        return sortDirection === "asc" ? aDate - bDate : bDate - aDate;
      }

      return 0;
    });

  const handleArtworkCreated = () => {
    // Refresh the page to show the updated data
    window.location.reload();
  };

  const handleDeleteArtwork = async (id: string) => {
    if (!id) return;

    if (
      confirm(
        "Are you sure you want to delete this artwork? This action cannot be undone."
      )
    )
      try {
        await deleteArtwork(id);
        // Refresh the page to show the updated data
        window.location.reload();
      } catch {
        alert("Failed to delete artwork. Please try again.");
      }
  };

  const toggleSortDirection = () => {
    setSortDirection(sortDirection === "asc" ? "desc" : "asc");
  };

  const addFilterTag = (tag: string) => {
    if (!filterTags.includes(tag)) setFilterTags([...filterTags, tag]);
  };

  const removeFilterTag = (tag: string) => {
    setFilterTags(filterTags.filter((t) => t !== tag));
  };

  const clearFilters = () => {
    setSearchTerm("");
    setFilterCategory("");
    setFilterTags([]);
  };

  const openAddDialog = () => {
    setEditingArtwork(null);
    setIsDialogOpen(true);
  };

  const openEditDialog = (artwork: Artwork) => {
    setEditingArtwork(artwork);
    setIsDialogOpen(true);
  };

  return (
    <div className="container py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Artwork Repository</h1>
        <Button onClick={openAddDialog}>
          <Plus className="h-4 w-4 mr-2" />
          Add New Artwork
        </Button>
      </div>
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search artworks..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <div className="flex gap-2">
              <Select
                value={sortBy}
                onValueChange={(
                  value: "title" | "year" | "createdAt" | "updatedAt"
                ) => setSortBy(value)}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="title">Title</SelectItem>
                  <SelectItem value="year">Year</SelectItem>
                  <SelectItem value="createdAt">Date Added</SelectItem>
                  <SelectItem value="updatedAt">Last Updated</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="icon"
                onClick={toggleSortDirection}
              >
                {sortDirection === "asc" ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() =>
                  setViewMode(viewMode === "grid" ? "list" : "grid")
                }
              >
                {viewMode === "grid" ? (
                  <List className="h-4 w-4" />
                ) : (
                  <Grid className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
          <div className="mt-4">
            <div className="flex items-center gap-2 mb-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Filters</span>
              {(filterCategory || filterTags.length > 0) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilters}
                  className="ml-auto h-7 px-2"
                >
                  <X className="h-3 w-3 mr-1" />
                  Clear
                </Button>
              )}
            </div>
            <div className="flex flex-wrap gap-2">
              <Select
                value={filterCategory || "all-categories"}
                onValueChange={(value: string) =>
                  setFilterCategory(value === "all-categories" ? "" : value)
                }
              >
                <SelectTrigger className="w-[180px] h-8">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all-categories">All Categories</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select
                value="select-tag"
                onValueChange={(value: string) => {
                  if (value && value !== "select-tag") addFilterTag(value);
                }}
              >
                <SelectTrigger className="w-[180px] h-8">
                  <SelectValue placeholder="Add Tag Filter" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="select-tag" disabled>
                    Select a tag
                  </SelectItem>
                  {allTags
                    .filter((tag) => !filterTags.includes(tag))
                    .map((tag) => (
                      <SelectItem key={tag} value={tag}>
                        {tag}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
            {filterTags.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {filterTags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {tag}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => removeFilterTag(tag)}
                    />
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
      {isLoading && !isLoadingMore ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p>Loading artworks...</p>
        </div>
      ) : error ? (
        <div className="text-center py-12 text-red-500">
          Failed to load artworks. Please try again.
        </div>
      ) : filteredArtworks.length === 0 ? (
        <div className="text-center py-12 text-muted-foreground">
          No artworks found matching your filters.
        </div>
      ) : viewMode === "grid" ? (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {filteredArtworks.map((artwork) => (
              <Card key={artwork.id} className="overflow-hidden">
                <div className="relative aspect-square">
                  <ArtworkGrid
                    artworks={[artwork]}
                    mode="minimal"
                    columns={1}
                    gap={0}
                    resolvedUrls={resolvedUrls}
                    isLoading={isLoading}
                  />
                </div>
                <CardHeader className="p-3 pb-0">
                  <CardTitle className="text-lg">{artwork.title}</CardTitle>
                </CardHeader>
                <CardContent className="p-3 pt-1">
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-muted-foreground">
                      {artwork.year}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {artwork.medium}
                    </p>
                  </div>
                </CardContent>
                <CardFooter className="p-3 pt-0 flex justify-end gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openEditDialog(artwork)}
                  >
                    <Edit className="h-3 w-3 mr-1" />
                    Edit
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-red-500 hover:text-red-700"
                    onClick={() => handleDeleteArtwork(artwork.id!)}
                  >
                    <Trash2 className="h-3 w-3 mr-1" />
                    Delete
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
          {/* Load More Button */}
          {hasMore && (
            <div className="flex justify-center mt-8">
              <Button
                onClick={loadMore}
                disabled={isLoadingMore}
                variant="outline"
                size="lg"
              >
                {isLoadingMore ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Loading...
                  </>
                ) : (
                  "Load More Artworks"
                )}
              </Button>
            </div>
          )}
        </>
      ) : (
        <>
          <div className="space-y-2">
            {filteredArtworks.map((artwork) => (
              <Card key={artwork.id} className="overflow-hidden">
                <div className="flex">
                  <div className="w-24 h-24 relative flex-shrink-0">
                    <ArtworkGrid
                      artworks={[artwork]}
                      mode="minimal"
                      columns={1}
                      gap={0}
                      resolvedUrls={resolvedUrls}
                      isLoading={isLoading}
                    />
                  </div>
                  <div className="flex-1 p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">{artwork.title}</h3>
                        <p className="text-sm text-muted-foreground">
                          {artwork.year} • {artwork.medium}
                        </p>
                        <p className="text-sm mt-1 line-clamp-2">
                          {artwork.description}
                        </p>
                      </div>
                      <div className="flex items-center">
                        <Button
                          variant="outline"
                          size="sm"
                          className="mr-2"
                          onClick={() => openEditDialog(artwork)}
                        >
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-500 hover:text-red-700"
                          onClick={() => handleDeleteArtwork(artwork.id!)}
                        >
                          <Trash2 className="h-3 w-3 mr-1" />
                          Delete
                        </Button>
                      </div>
                    </div>
                    {artwork.tags && artwork.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {artwork.tags.map((tag) => (
                          <Badge
                            key={tag}
                            variant="outline"
                            className="text-xs"
                          >
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            ))}
          </div>
          {/* Load More Button */}
          {hasMore && (
            <div className="flex justify-center mt-8">
              <Button
                onClick={loadMore}
                disabled={isLoadingMore}
                variant="outline"
                size="lg"
              >
                {isLoadingMore ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Loading...
                  </>
                ) : (
                  "Load More Artworks"
                )}
              </Button>
            </div>
          )}
        </>
      )}
      {/* Pagination Info */}
      {!isUndefined(totalCount) && (
        <div className="text-center text-sm text-muted-foreground mt-4">
          Showing {filteredArtworks.length} of {totalCount} artworks
        </div>
      )}
      {/* Artwork Dialog */}
      <AddNewArtworkDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        onArtworkCreated={handleArtworkCreated}
        artwork={editingArtwork || undefined}
        categories={categories}
      />
    </div>
  );
}
