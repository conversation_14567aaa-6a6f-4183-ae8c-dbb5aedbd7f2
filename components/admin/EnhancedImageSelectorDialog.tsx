"use client";

import React, { useState, useCallback, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { storage } from "@/lib/firebase";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  ref,
  listAll,
  getDownloadURL,
  StorageReference,
} from "firebase/storage";
import {
  ChevronRight,
  Folder,
  Search,
  X,
  ArrowLeft,
  Grid2X2,
  List,
  ImageIcon,
  CheckCircle2,
} from "lucide-react";

const BASE_PATH = process.env.NEXT_PUBLIC_STORAGE_BASE_PATH || "images";

interface StorageItem {
  ref: StorageReference;
  url?: string;
  type: "folder" | "file";
  parentPath: string;
}

interface EnhancedImageSelectorDialogProps {
  onSelect: (url: string) => void;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentImage?: string;
}

export function EnhancedImageSelectorDialog({
  onSelect,
  open,
  onOpenChange,
  currentImage,
}: EnhancedImageSelectorDialogProps) {
  const [allItems, setAllItems] = useState<StorageItem[]>([]);
  const [currentPath, setCurrentPath] = useState(BASE_PATH);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // No longer need folder creation state variables
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [dragOver, setDragOver] = useState(false);

  // Set the selected item to the current image when the dialog opens
  useEffect(() => {
    if (open && currentImage) setSelectedItem(currentImage);
    else if (open) setSelectedItem(null);
  }, [open, currentImage]);

  const loadAllContent = useCallback(
    async (path: string = BASE_PATH): Promise<StorageItem[]> => {
      try {
        const storageRef = ref(storage, path);
        const result = await listAll(storageRef);

        // Get all folders
        const folderItems: StorageItem[] = result.prefixes.map((prefixRef) => ({
          ref: prefixRef,
          type: "folder",
          parentPath: path,
        }));

        // Get all files with URLs
        const filePromises = result.items.map(async (itemRef) => {
          const url = await getDownloadURL(itemRef);

          return {
            ref: itemRef,
            url,
            type: "file" as const,
            parentPath: path,
          };
        });

        const fileItems = await Promise.all(filePromises);

        // Recursively get items from all subfolders
        const subFolderPromises = result.prefixes.map((prefix) =>
          loadAllContent(prefix.fullPath)
        );

        const subFolderResults = await Promise.all(subFolderPromises);
        const allSubItems = subFolderResults.flat();

        return [...folderItems, ...fileItems, ...allSubItems];
      } catch {
        return [];
      }
    },
    []
  );

  // Remove currentPath from dependency array (not needed)
  const loadContent = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      // Always load all items on initial load
      if (!allItems.length) {
        const items = await loadAllContent();
        setAllItems(items);
      }
    } catch {
      setError("Failed to load content");
    } finally {
      setLoading(false);
    }
  }, [loadAllContent, allItems.length]);

  // Load content on mount and when current path changes
  useEffect(() => {
    if (open) loadContent();
  }, [loadContent, open]);

  // Clear search when changing folders
  useEffect(() => {
    setSearchTerm("");
  }, [currentPath]);

  const handleFolderClick = (folderRef: StorageReference) => {
    setCurrentPath(folderRef.fullPath);
  };

  const navigateToParent = () => {
    const parentPath = currentPath.split("/").slice(0, -1).join("/");

    if (parentPath.length >= BASE_PATH.length) setCurrentPath(parentPath);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = () => {
    setDragOver(false);
  };

  // Get current folder's items or search results
  const currentItems = React.useMemo(() => {
    if (!allItems) return [];

    const searchLower = searchTerm.toLowerCase();

    // If searching, show all matching items regardless of current path
    if (searchTerm)
      return allItems.filter((item) => {
        const nameMatch = item.ref.name.toLowerCase().includes(searchLower);

        const pathMatch = item.parentPath.toLowerCase().includes(searchLower);

        return nameMatch || pathMatch;
      });

    // If not searching, only show items in current path
    return allItems.filter((item) => item.parentPath === currentPath);
  }, [allItems, currentPath, searchTerm]);

  // Path navigation breadcrumbs
  const pathSegments = currentPath.split("/").filter(Boolean);

  const baseFolderName = BASE_PATH.split("/").pop() || "root";

  // Get relative path for selected item
  const getRelativePath = (fullPath: string) => {
    return fullPath.replace(`${BASE_PATH}/`, "").replace(BASE_PATH, "");
  };

  // Handle double-click to select
  const handleImageClick = (relativePath: string) => {
    if (selectedItem === relativePath)
      // If already selected, treat as double-click and select
      onSelect(relativePath);
    // Otherwise just select
    else setSelectedItem(relativePath);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col overflow-hidden p-0">
        <DialogHeader className="p-6 pb-2">
          <DialogTitle className="text-xl">Select Image</DialogTitle>
        </DialogHeader>
        <div className="flex-1 flex flex-col overflow-hidden px-6">
          {/* Path navigation */}
          <div className="flex items-center gap-1 text-sm text-muted-foreground mb-4 bg-gray-50 p-2 rounded-md overflow-x-auto">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCurrentPath(BASE_PATH)}
              className="flex items-center shrink-0"
            >
              <Folder className="h-4 w-4 mr-1" />
              {baseFolderName}
            </Button>
            {pathSegments.map((segment, index) => {
              if (segment === baseFolderName) return null;

              const path = pathSegments.slice(0, index + 1).join("/");

              return (
                <React.Fragment key={path}>
                  <ChevronRight className="h-4 w-4 shrink-0" />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setCurrentPath(`${BASE_PATH}/${path}`)}
                    className="shrink-0"
                  >
                    {segment}
                  </Button>
                </React.Fragment>
              );
            })}
          </div>
          {/* Controls */}
          <div className="flex flex-wrap gap-2 mb-4">
            <div className="relative flex-1 min-w-[200px]">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search images..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() =>
                  setViewMode(viewMode === "grid" ? "list" : "grid")
                }
                title={viewMode === "grid" ? "List view" : "Grid view"}
              >
                {viewMode === "grid" ? (
                  <List className="h-4 w-4" />
                ) : (
                  <Grid2X2 className="h-4 w-4" />
                )}
              </Button>
              {currentPath !== BASE_PATH && (
                <Button
                  variant="outline"
                  onClick={navigateToParent}
                  className="flex items-center"
                  title="Go up one level"
                >
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  Up
                </Button>
              )}
            </div>
          </div>
          {error && (
            <div className="text-red-500 text-sm mb-4 p-3 bg-red-50 rounded-md flex items-center">
              <X className="h-4 w-4 mr-2 text-red-500" />
              {error}
              <Button
                variant="ghost"
                size="sm"
                className="ml-auto h-7 px-2 text-red-500 hover:text-red-700 hover:bg-red-100"
                onClick={() => setError(null)}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          )}
          {/* Content area with fixed height to ensure footer visibility */}
          <div
            className="flex-1 overflow-auto mb-4"
            style={{
              maxHeight: "calc(70vh - 200px)",
            }}
          >
            <div
              className="rounded-md"
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
            >
              {loading ? (
                <div className="flex items-center justify-center h-40">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-10 w-10 border-4 border-gray-200 border-t-primary mx-auto mb-3"></div>
                    <p className="text-gray-600">Loading images...</p>
                  </div>
                </div>
              ) : dragOver ? (
                <div className="flex items-center justify-center h-40 border-2 border-dashed border-primary bg-primary/5 rounded-lg">
                  <div className="text-center">
                    <p>Please use the Add New button to upload images</p>
                  </div>
                </div>
              ) : currentItems.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-40 bg-gray-50 rounded-lg">
                  <ImageIcon className="h-12 w-12 text-gray-300 mb-2" />
                  <div className="text-center text-muted-foreground">
                    {searchTerm
                      ? "No matching images found"
                      : "This folder is empty"}
                  </div>
                  <p className="text-sm text-gray-400 mt-2">
                    {searchTerm
                      ? "Try a different search term"
                      : "Use the Add New button to upload images"}
                  </p>
                </div>
              ) : viewMode === "grid" ? (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                  {currentItems.map((item) => {
                    if (item.type === "folder")
                      return (
                        <Card
                          key={item.ref.fullPath}
                          className="relative aspect-square cursor-pointer hover:ring-2 hover:ring-primary transition-all flex items-center justify-center overflow-hidden group"
                          onClick={() => handleFolderClick(item.ref)}
                        >
                          <div className="absolute inset-0 bg-gray-50 flex items-center justify-center">
                            <Folder className="h-16 w-16 text-gray-300 group-hover:text-gray-400 transition-colors" />
                          </div>
                          <div className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-2 text-center">
                            <div className="text-sm font-medium truncate">
                              {item.ref.name}
                            </div>
                            {searchTerm && (
                              <div className="text-xs text-muted-foreground truncate">
                                {item.parentPath.replace(BASE_PATH, "")}
                              </div>
                            )}
                          </div>
                        </Card>
                      );

                    const relativePath = getRelativePath(item.ref.fullPath);
                    const isSelected = selectedItem === relativePath;

                    return (
                      <Card
                        key={item.ref.fullPath}
                        className={`relative aspect-square cursor-pointer transition-all overflow-hidden group ${
                          isSelected
                            ? "ring-4 ring-primary"
                            : "hover:ring-2 hover:ring-primary/50"
                        }`}
                        onClick={() => handleImageClick(relativePath)}
                      >
                        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
                          <Image
                            src={item.url || "/placeholder.svg"}
                            alt={item.ref.name}
                            fill
                            className="object-cover"
                          />
                        </div>
                        {/* Filename overlay */}
                        <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 p-2 transform translate-y-full group-hover:translate-y-0 transition-transform">
                          <div className="text-xs text-white truncate">
                            {item.ref.name}
                          </div>
                        </div>
                        {searchTerm && (
                          <div className="absolute top-0 left-0 right-0 text-xs text-white bg-black/70 px-2 py-1 truncate">
                            {item.parentPath.replace(BASE_PATH, "")}
                          </div>
                        )}
                        {isSelected && (
                          <div className="absolute top-2 right-2">
                            <Badge
                              variant="default"
                              className="bg-primary text-white"
                            >
                              <CheckCircle2 className="h-3 w-3 mr-1" />
                              Selected
                            </Badge>
                          </div>
                        )}
                      </Card>
                    );
                  })}
                </div>
              ) : (
                <div className="space-y-1">
                  {currentItems.map((item) => {
                    if (item.type === "folder")
                      return (
                        <div
                          key={item.ref.fullPath}
                          className="flex items-center p-3 hover:bg-gray-50 rounded-md cursor-pointer border border-transparent hover:border-gray-200"
                          onClick={() => handleFolderClick(item.ref)}
                        >
                          <div className="h-8 w-8 bg-gray-100 rounded-md flex items-center justify-center mr-3">
                            <Folder className="h-5 w-5 text-gray-500" />
                          </div>
                          <div>
                            <div className="font-medium">{item.ref.name}</div>
                            {searchTerm && (
                              <div className="text-xs text-muted-foreground">
                                {item.parentPath.replace(BASE_PATH, "")}
                              </div>
                            )}
                          </div>
                        </div>
                      );

                    const relativePath = getRelativePath(item.ref.fullPath);
                    const isSelected = selectedItem === relativePath;

                    return (
                      <div
                        key={item.ref.fullPath}
                        className={`flex items-center p-3 rounded-md cursor-pointer border ${
                          isSelected
                            ? "bg-primary/5 border-primary"
                            : "hover:bg-gray-50 border-transparent hover:border-gray-200"
                        }`}
                        onClick={() => handleImageClick(relativePath)}
                      >
                        <div className="h-12 w-12 relative mr-3 flex-shrink-0 rounded-md overflow-hidden">
                          <Image
                            src={item.url || "/placeholder.svg"}
                            alt={item.ref.name}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="font-medium truncate">
                            {item.ref.name}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {item.ref.name.split(".").pop()?.toUpperCase()}
                          </div>
                          {searchTerm && (
                            <div className="text-xs text-muted-foreground truncate">
                              {item.parentPath.replace(BASE_PATH, "")}
                            </div>
                          )}
                        </div>
                        {isSelected && (
                          <div className="ml-2">
                            <CheckCircle2 className="h-5 w-5 text-primary" />
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        </div>
        {/* Fixed footer with selection controls */}
        <DialogFooter className="border-t p-4 mt-auto bg-gray-50">
          <div className="flex justify-between items-center w-full">
            <div className="text-sm">
              {selectedItem ? (
                <div className="flex items-center">
                  <Badge variant="outline" className="mr-2">
                    1 selected
                  </Badge>
                  <span className="text-muted-foreground truncate max-w-[200px]">
                    {selectedItem}
                  </span>
                </div>
              ) : (
                <span className="text-muted-foreground">No image selected</span>
              )}
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button
                onClick={() => {
                  if (selectedItem) onSelect(selectedItem);
                }}
                disabled={!selectedItem}
                variant="default"
              >
                <CheckCircle2 className="h-4 w-4 mr-2" />
                Use Selected Image
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
