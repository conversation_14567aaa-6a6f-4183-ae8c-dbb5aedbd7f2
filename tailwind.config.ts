import type {Config} from 'tailwindcss';

const config: Config = {
    content: [
        './pages/**/*.{js,ts,jsx,tsx,mdx}',
        './components/**/*.{js,ts,jsx,tsx,mdx}',
        './app/**/*.{js,ts,jsx,tsx,mdx}',
        '*.{js,ts,jsx,tsx,mdx}',
    ],
    theme: {
        extend: {
            colors: {
                modernArt: {
                    background: '#FCFCFC',
                    foreground: '#1A1A1A',
                    primaryAccent: '#0A747E',
                    secondaryAccent: '#D1E0E0',
                    border: '#E0E0E0',
                    cardBackground: '#FFFFFF',
                    cardForeground: '#1A1A1A',
                    mutedForeground: '#555555',
                },
                border: '#E0E0E0', // modernArt.border
                input: '#E0E0E0', // modernArt.border
                ring: '#0A747E', // modernArt.primaryAccent
                background: '#FCFCFC', // modernArt.background
                foreground: '#1A1A1A', // modernArt.foreground
                primary: {
                    DEFAULT: '#0A747E', // modernArt.primaryAccent
                    foreground: '#FFFFFF',
                },
                secondary: {
                    DEFAULT: '#F0F0F0',
                    foreground: '#1A1A1A', // modernArt.foreground
                },
                destructive: {
                    DEFAULT: 'hsl(var(--destructive))', // Keep existing destructive colors
                    foreground: 'hsl(var(--destructive-foreground))',
                },
                muted: {
                    DEFAULT: '#F9F9F9',
                    foreground: '#555555', // modernArt.mutedForeground
                },
                accent: {
                    DEFAULT: '#0A747E', // modernArt.primaryAccent
                    foreground: '#FFFFFF',
                },
                popover: {
                    DEFAULT: '#FFFFFF', // modernArt.cardBackground
                    foreground: '#1A1A1A', // modernArt.cardForeground
                },
                card: {
                    DEFAULT: '#FFFFFF', // modernArt.cardBackground
                    foreground: '#1A1A1A', // modernArt.cardForeground
                },
            },
            borderRadius: {
                lg: 'var(--radius)',
                md: 'calc(var(--radius) - 2px)',
                sm: 'calc(var(--radius) - 4px)',
            },
            fontFamily: {
                sans: ['Inter', 'Montserrat', 'sans-serif'],
                serif: ['Cormorant Garamond', 'serif'],
                cormorant: ['Cormorant Garamond', 'serif'],
                montserrat: ['Montserrat', 'sans-serif'],
            },
            boxShadow: {
                // elegant box shadows removed
            },
        },
    },
    plugins: [],
};

export default config;
