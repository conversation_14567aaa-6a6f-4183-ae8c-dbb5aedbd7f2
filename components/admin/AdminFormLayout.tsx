"use client";

import { ReactNode, useState, useEffect, useRef, useCallback } from "react";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";
import {
  doc,
  deleteDoc,
  collection,
  getDocs,
  writeBatch,
  getDoc,
} from "firebase/firestore";
import { db } from "@/lib/firebase";
import { FloatingActionBar } from "./FloatingActionBar";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

const noop = () => {};

interface AdminFormLayoutProps {
  title: string;
  children: ReactNode;
  isLoading?: boolean;
  isSaving?: boolean;
  isDirty?: boolean;
  onSave?: () => void;
  onReset?: () => void;
  actions?: ReactNode;
  description?: string;
  previewDocId?: string;
}

/**
 * A reusable layout component for admin forms with consistent styling and behavior
 */
export default function AdminFormLayout({
  title,
  children,
  isLoading = false,
  isSaving = false,
  isDirty = false,
  onSave,
  onReset,
  actions,
  description,
  previewDocId,
}: AdminFormLayoutProps) {
  const { toast, showToast } = useToast();
  const [isRemovingPreview, setIsRemovingPreview] = useState(false);
  const [removePreviewDialogOpen, setRemovePreviewDialogOpen] = useState(false);
  const [hasPreviewData, setHasPreviewData] = useState(false);
  const [hasCurrentPagePreview, setHasCurrentPagePreview] = useState(false);
  const [checkingPreviewData, setCheckingPreviewData] = useState(true);

  const isInitialMount = useRef(true);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Skip during Fast Refresh remounts in development
    if (process.env.NODE_ENV === 'development' && !isInitialMount.current) {
      return;
    }

    // Only proceed if this is a direct form submission
    if (e.type === 'submit' && onSave) {
      onSave();
    }
  };

  useEffect(() => {
    isInitialMount.current = false;
  }, []);

  // Function to check if preview data exists
  const checkPreviewData = useCallback(async () => {
    if (!previewDocId) {
      setCheckingPreviewData(false);
      return;
    }

    try {
      setCheckingPreviewData(true);
      
      // Check if preview collection exists and has any documents
      const previewCollection = collection(db, "preview");
      const snapshot = await getDocs(previewCollection);
      setHasPreviewData(!snapshot.empty);

      // Check if current page has preview data
      const currentPagePreviewRef = doc(db, "preview", previewDocId);
      const currentPagePreviewDoc = await getDoc(currentPagePreviewRef);
      setHasCurrentPagePreview(currentPagePreviewDoc.exists());
    } catch (error) {
      console.error("Error checking preview data:", error);
      setHasPreviewData(false);
      setHasCurrentPagePreview(false);
    } finally {
      setCheckingPreviewData(false);
    }
  }, [previewDocId]);

  // Check if preview data exists on mount
  useEffect(() => {
    checkPreviewData();
  }, [checkPreviewData]);

  // Function to refresh preview data and notify other components
  const refreshPreviewData = async () => {
    await checkPreviewData();
    // Dispatch custom event to notify AdminPreviewButton
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('refreshPreviewData'));
    }
  };


  // Function to remove all preview data
  const handleRemoveAllPreview = async () => {
    if (!previewDocId) return;

    try {
      setIsRemovingPreview(true);
      // Delete all preview documents
      const previewCollection = collection(db, "preview");
      const snapshot = await getDocs(previewCollection);

      const batch = writeBatch(db);
      snapshot.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });

      await batch.commit();

      showToast("All preview data removed successfully", {
        variant: "success",
      });

      // Reload the page to refresh the form data
      window.location.reload();
    } catch {
      showToast("Failed to remove preview data", {
        variant: "error",
      });
    } finally {
      setIsRemovingPreview(false);
      setRemovePreviewDialogOpen(false);
    }
  };

  // Function to remove only this page's preview data
  const handleRemovePagePreview = async () => {
    if (!previewDocId) return;

    try {
      setIsRemovingPreview(true);
      // Delete the preview document for this page
      const previewRef = doc(db, "preview", previewDocId);
      await deleteDoc(previewRef);

      showToast("Preview data removed successfully", {
        variant: "success",
      });

      // If the form is dirty, ask the user if they want to reload
      if (isDirty) {
        if (confirm("Form has unsaved changes. Reload to get fresh data?")) {
          window.location.reload();
        }
      } else {
        // If the form is not dirty, just reload
        window.location.reload();
      }
    } catch {
      showToast("Failed to remove preview data", {
        variant: "error",
      });
    } finally {
      setIsRemovingPreview(false);
      setRemovePreviewDialogOpen(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto bg-white rounded-lg shadow p-6 mb-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">{title}</h1>
          {description && <p className="text-gray-600 mt-1">{description}</p>}
        </div>
        <div className="flex items-center space-x-2">{actions}</div>
      </div>
      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          <span className="ml-2 text-gray-500">Loading content...</span>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          {children}
          {/* We're removing the buttons here since they'll be in the floating bar */}
        </form>
      )}
      {/* Floating action bar */}
      {!isLoading && (
        <FloatingActionBar
          isDirty={isDirty}
          isSaving={isSaving}
          isRemovingPreview={isRemovingPreview}
          onSave={onSave || noop}
          onReset={onReset || noop}
          onSaveComplete={refreshPreviewData}
          onRemovePreview={
            previewDocId && hasPreviewData && !checkingPreviewData
              ? () => setRemovePreviewDialogOpen(true)
              : undefined
          }
        />
      )}
      {toast}

      {/* Remove Preview Confirmation Dialog */}
      <AlertDialog
        open={removePreviewDialogOpen}
        onOpenChange={setRemovePreviewDialogOpen}
      >
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Preview Data</AlertDialogTitle>
            <AlertDialogDescription>
              Choose which preview data you want to remove:
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex flex-col gap-2 sm:gap-2">
            <div className="flex flex-col gap-2 w-full">
              {hasCurrentPagePreview && (
                <AlertDialogAction
                  onClick={(e) => {
                    e.preventDefault();
                    handleRemovePagePreview();
                  }}
                  disabled={isRemovingPreview}
                  className="bg-amber-600 hover:bg-amber-700 w-full"
                >
                  {isRemovingPreview ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Removing...
                    </>
                  ) : (
                    "Remove Current Page Preview"
                  )}
                </AlertDialogAction>
              )}
              {hasPreviewData && (
                <AlertDialogAction
                  onClick={(e) => {
                    e.preventDefault();
                    handleRemoveAllPreview();
                  }}
                  disabled={isRemovingPreview}
                  className="bg-red-600 hover:bg-red-700 w-full"
                >
                  {isRemovingPreview ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Removing...
                    </>
                  ) : (
                    "Remove All Preview Data"
                  )}
                </AlertDialogAction>
              )}
            </div>
            <AlertDialogCancel disabled={isRemovingPreview} className="w-full">
              Cancel
            </AlertDialogCancel>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
