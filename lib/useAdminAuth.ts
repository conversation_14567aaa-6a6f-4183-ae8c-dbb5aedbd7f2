"use client";

import {useState, useEffect} from 'react';
import tryToCatch from 'try-to-catch';
import {
    GoogleAuthProvider,
    signInWithPopup,
    signInWithRedirect,
    onAuthStateChanged,
    signOut as fbSignOut,
    User,
} from 'firebase/auth';
import {auth} from './firebase';

export function useAdminAuth() {
    const [user, setUser] = useState<User | null>(null);
    const [loading, setLoading] = useState(true);
    
    const allowedEmails = (process.env.NEXT_PUBLIC_ADMIN_EMAILS || '')
        .split(',')
        .map((e) => e
        .trim()
        .toLowerCase())
        .filter(Boolean);
    
    useEffect(() => {
        if (!auth) {
            setUser(null);
            setLoading(false);
            return;
        }
        
        const unsubscribe = onAuthStateChanged(auth, (u) => {
            if (u?.email && allowedEmails.includes(u.email.toLowerCase())) {
                setUser(u);
            } else {
                if (u && auth)
                    fbSignOut(auth);
                
                setUser(null);
            }
            
            setLoading(false);
        });
        
        return unsubscribe;
    }, [allowedEmails]);
    
    const signIn = async () => {
        if (!auth) return;
        
        const provider = new GoogleAuthProvider();
        const [err] = await tryToCatch(signInWithPopup, auth, provider);
        
        if (err)
            // Fallback to redirect if popup is blocked
            await signInWithRedirect(auth, provider);
    };
    
    const signOut = async () => {
        if (!auth) {
            setUser(null);
            return;
        }
        
        try {
            await fbSignOut(auth);
            setUser(null);
        } catch {}
    };
    
    return {
        user,
        loading,
        signIn,
        signOut,
    };
}

/**
 * Simple utility to check if admin features should be shown
 * Returns false in static builds, otherwise checks if user exists
 */
export function useIsAdmin(): boolean {
    const isStaticBuild = process.env.NEXT_PUBLIC_STATIC_BUILD === 'true';
    
    // Always call useAdminAuth to satisfy React hooks rules
    // but in static builds, the auth will be null anyway due to conditional Firebase init
    const { user } = useAdminAuth();
    
    // Return false in static builds regardless of user state
    return !isStaticBuild && !!user;
}
