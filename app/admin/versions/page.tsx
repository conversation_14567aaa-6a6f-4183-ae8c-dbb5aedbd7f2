"use client";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import VersionManager from "@/components/admin/VersionManager";

export default function VersionsPage() {
  return (
    <div className="container mx-auto py-6 space-y-6 max-w-5xl">
      <div className="flex items-center">
        <h1 className="text-3xl font-bold">Version Management</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Content Versions</CardTitle>
          <CardDescription>
            Manage your content versions. You can roll back to a previous
            version or delete old versions.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <VersionManager />
        </CardContent>
      </Card>
    </div>
  );
}
