import { NextResponse } from 'next/server';

export async function GET() {
  try {
    const emulatorHost = process.env.NEXT_PUBLIC_FIREBASE_STORAGE_EMULATOR_HOST || 'localhost:9199';
    const testUrl = `http://${emulatorHost}/v0/b/test/o/test?alt=media`;
    
    console.log(`Testing emulator access from server: ${testUrl}`);
    
    const response = await fetch(testUrl, {
      method: 'HEAD',
      signal: AbortSignal.timeout(3000)
    });
    
    return NextResponse.json({
      success: true,
      status: response.status,
      emulatorHost,
      message: 'Emulator is accessible from server-side'
    });
  } catch (error: any) {
    console.error('Server-side emulator test failed:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      code: error.code,
      message: 'Emulator not accessible from server-side'
    });
  }
}
