"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { categorizeEvents, EventWithStatus } from "@/lib/eventsHelpers";
import { EventCard } from "@/components/EventCard";
import EditButton from "@/components/EditButton";
import PageHeader from "@/components/PageHeader";
import usePreview from "@/lib/usePreview";
import { usePublicContent } from "@/hooks/use-public-content";
import { useImageResolver } from "@/hooks/use-image-resolver";
import { EventsContent } from "@/types/events";
import { useAdminAuth } from "@/lib/useAdminAuth";

const DEFAULT_EVENTS_CONTENT: EventsContent = {
  events: [],
};

const PLACEHOLDER_IMAGE_SRC = "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7";

export default function Events() {
  const isPreviewMode = usePreview();
  const { user } = useAdminAuth();
  const [fullImageOpen, setFullImageOpen] = useState(false);
  const [currentImage, setCurrentImage] = useState<string | null>(null);
  const [events, setEvents] = useState<EventWithStatus[]>([]);
  const [activeTab, setActiveTab] = useState("ongoing");

  // Use the new hook for content loading
  const { content, loading, error } = usePublicContent<EventsContent>({
    collection: "events",
    documentId: "events",
    defaultValue: DEFAULT_EVENTS_CONTENT,
    previewMode: isPreviewMode,
  });

  // Use the image resolver hook
  const { resolvedUrls: resolvedImageUrls, resolveImages } = useImageResolver();

  // Resolve images when content changes
  useEffect(() => {
    if (content?.events) {
      const imagePaths = content.events
        .flatMap((ev) => [ev.images?.main, ev.images?.details])
        .filter(Boolean);
      resolveImages(imagePaths);
    }
  }, [content, resolveImages]);

  const handleImageClick = (imageUrl: string) => {
    setCurrentImage(imageUrl);
    setFullImageOpen(true);
  };

  const closeFullImage = () => {
    setFullImageOpen(false);
  };

  // Categorize events after content is loaded
  useEffect(() => {
    if (!content) return;
    const eventsList = content.events || [];
    const categorizedEvents = categorizeEvents(eventsList);
    setEvents(categorizedEvents);
  }, [content]);

  // Filter events by status
  const ongoingEvents = events.filter((event) => event._status === "ongoing");
  const upcomingEvents = events.filter((event) => event._status === "upcoming");
  const pastEvents = events.filter((event) => event._status === "past");

  // Determine which tab to show by default
  useEffect(() => {
    if (!loading) {
      if (ongoingEvents.length > 0) {
        setActiveTab("ongoing");
      } else if (upcomingEvents.length > 0) {
        setActiveTab("upcoming");
      } else {
        setActiveTab("past");
      }
    }
  }, [loading, ongoingEvents.length, upcomingEvents.length]);

  // Render event list for a specific category
  const renderEventList = (eventList: EventWithStatus[]) => {
    if (eventList.length === 0) {
      return (
        <div className="text-center text-elegant-text/60 font-montserrat italic">
          No events in this category
        </div>
      );
    }

    return (
      <div className="space-y-20">
        {eventList.map((event, index) => (
          <div key={event.id} className="relative animate-subtle-fade group">
            <div className="bg-white elegant-card p-1 shadow-lg transition-shadow duration-300 group-hover:shadow-xl">
              <EventCard
                {...event}
                images={{
                  main: resolvedImageUrls[event.images.main] || PLACEHOLDER_IMAGE_SRC,
                  details: event.images.details
                    ? (resolvedImageUrls[event.images.details] || PLACEHOLDER_IMAGE_SRC)
                    : undefined,
                }}
                onImageClick={(imageClickedIndex) => {
                  let imageUrlToOpen = '';
                  if (imageClickedIndex === 0 && event.images.main) {
                    imageUrlToOpen = resolvedImageUrls[event.images.main] || PLACEHOLDER_IMAGE_SRC;
                  } else if (imageClickedIndex === 1 && event.images.details) {
                    imageUrlToOpen = resolvedImageUrls[event.images.details] || PLACEHOLDER_IMAGE_SRC;
                  }
                  if (imageUrlToOpen) {
                    handleImageClick(imageUrlToOpen);
                  }
                }}
                eventIndex={index}
              />
            </div>
            {/* Divider */}
            {index < eventList.length - 1 && (
              <div className="absolute -bottom-10 left-1/2 transform -translate-x-1/2 flex items-center space-x-2 opacity-50 group-hover:opacity-100 transition-opacity duration-300">
                <div className="w-2.5 h-2.5 rounded-full bg-elegant-accent/40"></div>
                <div className="w-2.5 h-2.5 rounded-full bg-elegant-accent/60"></div>
                <div className="w-2.5 h-2.5 rounded-full bg-elegant-accent/40"></div>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="max-w-5xl mx-auto px-4 py-16">
      <PageHeader
        title="Events & Exhibitions"
        subtitle="Calendar"
        description="A showcase of art exhibitions and events featuring serigraph prints and watercolor paintings"
      />
      <div className="flex items-center justify-end mb-2 -mt-12">
        <EditButton path="/admin/events" hidden={isPreviewMode || !user} />
      </div>
      {loading ? (
        <div className="text-center text-elegant-text/60 font-montserrat">
          Loading events...
        </div>
      ) : error ? (
        <div className="text-center text-elegant-text/60 font-montserrat">
          Error loading events
        </div>
      ) : (
        <div>
          {/* Tabs Navigation */}
          <div className="flex border-b border-elegant mb-12 justify-center">
            <button
              onClick={() => setActiveTab("ongoing")}
              className={`px-6 py-3 font-montserrat text-sm uppercase tracking-wider relative ${
                activeTab === "ongoing"
                  ? "text-elegant-accent border-b-2 border-elegant-accent"
                  : "text-elegant-text/70 hover:text-elegant-text"
              }`}
              disabled={ongoingEvents.length === 0}
            >
              Ongoing
              {ongoingEvents.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-elegant-accent text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {ongoingEvents.length}
                </span>
              )}
            </button>
            <button
              onClick={() => setActiveTab("upcoming")}
              className={`px-6 py-3 font-montserrat text-sm uppercase tracking-wider relative ${
                activeTab === "upcoming"
                  ? "text-elegant-accent border-b-2 border-elegant-accent"
                  : "text-elegant-text/70 hover:text-elegant-text"
              }`}
              disabled={upcomingEvents.length === 0}
            >
              Upcoming
              {upcomingEvents.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-elegant-accent text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {upcomingEvents.length}
                </span>
              )}
            </button>
            <button
              onClick={() => setActiveTab("past")}
              className={`px-6 py-3 font-montserrat text-sm uppercase tracking-wider relative ${
                activeTab === "past"
                  ? "text-elegant-accent border-b-2 border-elegant-accent"
                  : "text-elegant-text/70 hover:text-elegant-text"
              }`}
              disabled={pastEvents.length === 0}
            >
              Past
              {pastEvents.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-elegant-accent text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {pastEvents.length}
                </span>
              )}
            </button>
          </div>
          {/* Tab Content */}
          <div className="mt-10">
            {activeTab === "ongoing" && renderEventList(ongoingEvents)}
            {activeTab === "upcoming" && renderEventList(upcomingEvents)}
            {activeTab === "past" && renderEventList(pastEvents)}
          </div>
        </div>
      )}

      {/* Simple full-screen image viewer */}
      {fullImageOpen && currentImage && (
        <div
          className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center"
          onClick={closeFullImage}
        >
          <button
            className="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
            aria-label="Close image"
          >
            <svg
              className="w-8 h-8"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
          <div
            className="relative h-[90vh] w-[90vw] cursor-zoom-in"
            onClick={(e) => e.stopPropagation()}
          >
            <Image
              src={currentImage}
              alt="Event image"
              fill
              className="object-contain"
              sizes="90vw"
              priority
            />
          </div>
        </div>
      )}
    </div>
  );
}
