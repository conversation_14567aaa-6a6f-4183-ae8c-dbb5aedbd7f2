import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import * as nodemailer from "nodemailer";

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}



// Email sending interface
interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  from?: string;
  attachments?: Array<{
    filename: string;
    content: Buffer;
    cid: string;
    contentDisposition: 'inline';
  }>;
}

/**
 * Sends an email using nodemailer
 */
export async function sendEmail(options: EmailOptions): Promise<void> {
  const { to, subject, html, from, attachments } = options;

  // Get email configuration from environment variables
  const emailUser = process.env.GMAIL_EMAIL;
  const emailPass = process.env.GMAIL_APP_PASSWORD;

  if (!emailUser || !emailPass) {
    functions.logger.warn("Email configuration missing. Logging email instead of sending.");
    functions.logger.info(`Mock email send:
      To: ${to}
      Subject: ${subject}
      HTML: ${html.substring(0, 200)}...
      Attachments: ${attachments ? attachments.length : 0}
    `);
    return;
  }

  // Create transporter
  const transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: emailUser,
      pass: emailPass,
    },
  });

  // Send email
  try {
    const info = await transporter.sendMail({
      from: from || `"Bela Gallery" <${emailUser}>`,
      to,
      subject,
      html,
      attachments: attachments || [],
    });

    functions.logger.info(`Email sent: ${info.messageId}`);
    if (attachments && attachments.length > 0) {
      functions.logger.info(`📎 Email sent with ${attachments.length} image attachments`);
    }
  } catch (error) {
    functions.logger.error("Error sending email:", error);
    throw error;
  }
}

/**
 * Creates HTML for subscription confirmation email
 */
export function createConfirmationEmailHtml(name: string, confirmationUrl: string): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2>Confirm Your Subscription</h2>
      <p>Hello ${name},</p>
      <p>Thank you for subscribing to Bela Gallery updates. Please confirm your subscription by clicking the button below:</p>
      <p style="text-align: center; margin: 30px 0;">
        <a href="${confirmationUrl}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; display: inline-block;">
          Confirm Subscription
        </a>
      </p>
      <p>If you didn't request this subscription, you can safely ignore this email.</p>
      <p>Best regards,<br>Bela Gallery Team</p>
    </div>
  `;
}

/**
 * Creates HTML for welcome email after confirmation
 */
export function createWelcomeEmailHtml(name: string, unsubscribeUrl?: string): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to Our Newsletter</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
            .container { max-width: 600px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9; }
            .header { text-align: center; margin-bottom: 30px; }
            .header h1 { color: #007bff; margin: 0; }
            .content { background-color: #ffffff; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
            .highlight { background-color: #e7f3ff; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
            .footer { margin-top: 20px; font-size: 0.9em; color: #777; text-align: center; }
            .unsubscribe { font-size: 0.8em; color: #999; margin-top: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎨 Welcome to Bela Gallery!</h1>
            </div>
            <div class="content">
                <h2>Hello ${name}!</h2>
                <p>Welcome to our community! We're thrilled to have you join our newsletter.</p>

                <div class="highlight">
                    <p><strong>What to expect:</strong></p>
                    <ul>
                        <li>🖼️ Latest artwork updates and new gallery additions</li>
                        <li>🎭 Upcoming events and exhibitions</li>
                        <li>✨ Behind-the-scenes insights from the artist</li>
                        <li>🎨 Exclusive previews of new collections</li>
                    </ul>
                </div>

                <p>Thank you for your interest in our art and for being part of our creative journey. We look forward to sharing beautiful moments and artistic discoveries with you!</p>

                <p>Stay inspired,<br>
                <strong>The Bela Gallery Team</strong></p>
            </div>
            <div class="footer">
                <p>&copy; ${new Date().getFullYear()} Bela Gallery. All rights reserved.</p>
                <div class="unsubscribe">
                    <p>You're receiving this email because you subscribed to our newsletter. If you no longer wish to receive these emails, you can <a href="${unsubscribeUrl || '#'}" style="color: #007bff; text-decoration: none;">unsubscribe at any time</a>.</p>
                </div>
            </div>
        </div>
    </body>
    </html>
  `;
}

// Newsletter data interface
interface NewsletterData {
  name: string;
  subject: string;
  content: string;
  featuredArtworks: Array<{
    title: string;
    description?: string;
    imageUrl?: string;
    galleryUrl?: string;
  }>;
  upcomingEvents: Array<{
    title: string;
    date: string;
    description?: string;
    location?: string;
  }>;
  authorName: string;
  unsubscribeUrl: string;
}

// Helper function to convert markdown-style formatting to HTML
function convertMarkdownToHtml(text: string): string {
  const html = text
    // Convert **bold** to <strong>
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // Convert *italic* to <em>
    .replace(/\*(.*?)\*/g, '<em>$1</em>');

  // Handle list items more carefully
  const lines = html.split('\n');
  const processedLines: string[] = [];
  let inList = false;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    if (line.startsWith('• ')) {
      if (!inList) {
        processedLines.push('<ul style="margin: 10px 0; padding-left: 20px;">');
        inList = true;
      }
      processedLines.push(`<li>${line.substring(2)}</li>`);
    } else {
      if (inList) {
        processedLines.push('</ul>');
        inList = false;
      }
      if (line) {
        processedLines.push(line);
      }
    }
  }

  // Close any open list
  if (inList) {
    processedLines.push('</ul>');
  }

  return processedLines.join('\n');
}

// Helper function to convert relative URL to full URL
function getFullUrl(url: string): string {
  if (!url) return '';

  // If it's already a full URL, return as is
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }

  // Convert relative URL to full URL
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
  const fullUrl = `${baseUrl}${url.startsWith('/') ? '' : '/'}${url}`;
  return fullUrl;
}

// Helper function to detect image type from file extension or magic bytes
function detectImageType(buffer: Buffer, filename: string): string {
  // Check magic bytes first (more reliable)
  const firstBytes = buffer.subarray(0, 4);

  // JPEG magic bytes: FF D8 FF
  if (firstBytes[0] === 0xFF && firstBytes[1] === 0xD8 && firstBytes[2] === 0xFF) {
    return 'image/jpeg';
  }

  // PNG magic bytes: 89 50 4E 47
  if (firstBytes[0] === 0x89 && firstBytes[1] === 0x50 && firstBytes[2] === 0x4E && firstBytes[3] === 0x47) {
    return 'image/png';
  }

  // GIF magic bytes: 47 49 46 38
  if (firstBytes[0] === 0x47 && firstBytes[1] === 0x49 && firstBytes[2] === 0x46 && firstBytes[3] === 0x38) {
    return 'image/gif';
  }

  // WebP magic bytes: 52 49 46 46 (RIFF) + WEBP at offset 8
  if (firstBytes[0] === 0x52 && firstBytes[1] === 0x49 && firstBytes[2] === 0x46 && firstBytes[3] === 0x46) {
    const webpBytes = buffer.subarray(8, 12);
    if (webpBytes[0] === 0x57 && webpBytes[1] === 0x45 && webpBytes[2] === 0x42 && webpBytes[3] === 0x50) {
      return 'image/webp';
    }
  }

  // Fallback to file extension
  const ext = filename.toLowerCase().split('.').pop();
  switch (ext) {
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'png':
      return 'image/png';
    case 'gif':
      return 'image/gif';
    case 'webp':
      return 'image/webp';
    default:
      return 'image/jpeg'; // Default fallback
  }
}

// Helper function to resize image using Sharp (server-side)
async function resizeImageBuffer(buffer: Buffer, maxWidth: number = 300, quality: number = 0.7): Promise<Buffer> {
  try {
    // Import sharp for server-side image processing
    const sharp = await import('sharp');

    // Get metadata first to validate the image
    const metadata = await sharp.default(buffer).metadata();

    // Validate that we have valid image data
    if (!metadata.width || !metadata.height || !metadata.format) {
      return buffer;
    }

    // Resize and compress the image
    const resizedBuffer = await sharp.default(buffer)
      .resize(maxWidth, null, {
        withoutEnlargement: true,
        fit: 'inside'
      })
      .jpeg({
        quality: Math.round(quality * 100),
        progressive: true,
        mozjpeg: true
      })
      .toBuffer();

    // Validate the output
    if (!resizedBuffer || resizedBuffer.length === 0) {
      return buffer;
    }

    // Quick validation that the output is valid JPEG
    const jpegHeader = resizedBuffer.subarray(0, 3);
    if (jpegHeader[0] !== 0xFF || jpegHeader[1] !== 0xD8 || jpegHeader[2] !== 0xFF) {
      return buffer;
    }

    return resizedBuffer;
  } catch (error) {
    // Fallback: Return original buffer
    return buffer;
  }
}

/**
 * Creates newsletter email HTML with CID attachments for images
 */
// Helper function to fetch image and return as buffer for CID attachment
async function getImageAsBuffer(imageUrl: string): Promise<{ buffer: Buffer; contentType: string } | null> {
  if (!imageUrl) return null;

  try {
    // Use the server-side method to get the full image URL
    const fullUrl = await getServerSideImageUrl(imageUrl);

    // Validate that we have a proper URL
    if (!fullUrl || (!fullUrl.startsWith('http://') && !fullUrl.startsWith('https://'))) {
      functions.logger.warn(`❌ Invalid URL for ${imageUrl}, skipping`);
      return null;
    }

    // Add timeout to prevent hanging
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

    const response = await fetch(fullUrl, {
      signal: controller.signal,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; Newsletter-Service/1.0)',
        'Accept': 'image/*,*/*;q=0.8'
      }
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      functions.logger.warn(`❌ Failed to fetch image (${response.status} ${response.statusText}): ${fullUrl}`);
      return null;
    }

    const arrayBuffer = await response.arrayBuffer();
    const originalBuffer = Buffer.from(arrayBuffer);

    // Validate that we actually got image data
    if (originalBuffer.length === 0) {
      functions.logger.warn(`❌ Empty image data for ${imageUrl}`);
      return null;
    }

    // Validate the original image data
    const detectedType = detectImageType(originalBuffer, imageUrl);

    // Try to resize the image, but fallback to original if resizing fails
    let finalBuffer: Buffer = originalBuffer;
    let contentType = detectedType;

    try {
      const resizedBuffer = await resizeImageBuffer(originalBuffer, 300, 0.6); // 300px width, 60% quality

      // Validate the resized buffer
      if (resizedBuffer && resizedBuffer.length > 0) {
        finalBuffer = resizedBuffer;
        contentType = 'image/jpeg'; // Sharp outputs JPEG
      }
    } catch (resizeError) {
      functions.logger.warn(`⚠️  Image resizing failed for ${imageUrl}:`, resizeError);
      // Keep original buffer and content type
    }

    // Check final size after resizing - use stricter limits for email compatibility
    if (finalBuffer.length > 50000) { // 50KB limit for better email client compatibility
      try {
        // Try more aggressive compression
        const moreCompressedBuffer = await resizeImageBuffer(originalBuffer, 200, 0.3); // 200px width, 30% quality

        if (moreCompressedBuffer.length > 50000) {
          // Ultra compression as last resort
          const ultraCompressedBuffer = await resizeImageBuffer(originalBuffer, 150, 0.2); // 150px width, 20% quality

          if (ultraCompressedBuffer.length > 50000) {
            functions.logger.warn(`📏 Image too large after compression (${ultraCompressedBuffer.length} bytes), skipping ${imageUrl}`);
            return null;
          }

          // Use the ultra compressed version
          finalBuffer = ultraCompressedBuffer;
          contentType = 'image/jpeg';
        } else {
          // Use the more compressed version
          finalBuffer = moreCompressedBuffer;
          contentType = 'image/jpeg';
        }
      } catch (compressionError) {
        functions.logger.warn(`⚠️  Additional compression failed for ${imageUrl}:`, compressionError);
      }
    }

    // Validate the final buffer
    if (!finalBuffer || finalBuffer.length === 0) {
      functions.logger.warn(`⚠️  Final buffer is empty for ${imageUrl}`);
      return null;
    }

    return { buffer: finalBuffer, contentType };
  } catch (error) {
    if (error instanceof Error) {
      functions.logger.error(`❌ Error converting image to data URL: ${imageUrl}`, {
        message: error.message,
        name: error.name,
        stack: error.stack
      });
    } else {
      functions.logger.error(`❌ Unknown error converting image to data URL: ${imageUrl}`, error);
    }
    // Return null to skip image embedding instead of failing
    return null;
  }
}

// Helper function to get server-side image URL
async function getServerSideImageUrl(imageUrl: string): Promise<string> {
  if (!imageUrl) return '';

  const isStaticBuild = process.env.NEXT_PUBLIC_STATIC_BUILD === 'true';
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

  if (isStaticBuild) {
    // Static mode: convert relative paths to absolute URLs for email compatibility
    const normalizedPath = imageUrl.startsWith('/') ? imageUrl : `/${imageUrl}`;
    const absoluteUrl = `${baseUrl}${normalizedPath}`;
    functions.logger.info(`📸 Static mode - converting ${imageUrl} to ${absoluteUrl}`);
    return absoluteUrl;
  } else {
    // Firebase Storage mode - try multiple approaches
    const basePath = process.env.NEXT_PUBLIC_STORAGE_BASE_PATH || 'bela-gallery';
    const useEmulator = process.env.NEXT_PUBLIC_FIREBASE_USE_EMULATOR === 'true';
    const bucketName = process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || 'belagallery-9e01b.firebasestorage.app';

    // Remove leading slash for consistency
    const normalizedPath = imageUrl.startsWith('/') ? imageUrl.slice(1) : imageUrl;
    const fullPath = `${basePath}/${normalizedPath}`;

    if (useEmulator) {
      // For emulator, try emulator URL first, then fallback to production
      const emulatorHost = process.env.NEXT_PUBLIC_FIREBASE_STORAGE_EMULATOR_HOST || 'localhost:9199';
      const encodedPath = encodeURIComponent(fullPath);
      const emulatorUrl = `http://${emulatorHost}/v0/b/${bucketName}/o/${encodedPath}?alt=media`;

      // Test if emulator URL is accessible
      try {
        const testResponse = await fetch(emulatorUrl, { method: 'HEAD', signal: AbortSignal.timeout(3000) });
        if (testResponse.ok) {
          return emulatorUrl;
        }
      } catch (error) {
        // Emulator not accessible, fall through to production
      }

      // Fallback to production URL if emulator is not accessible
      const prodUrl = `https://firebasestorage.googleapis.com/v0/b/${bucketName}/o/${encodedPath}?alt=media`;
      return prodUrl;
    } else {
      // For production, use direct URL
      const encodedPath = encodeURIComponent(fullPath);
      const directUrl = `https://firebasestorage.googleapis.com/v0/b/${bucketName}/o/${encodedPath}?alt=media`;
      return directUrl;
    }
  }
}

export async function createNewsletterEmailHtml(data: NewsletterData): Promise<{ html: string; attachments: Array<{ filename: string; content: Buffer; cid: string; contentDisposition: 'inline' }> }> {
  const { name, subject, content, featuredArtworks, upcomingEvents, authorName, unsubscribeUrl } = data;

  // Convert markdown-style content to HTML
  const htmlContent = convertMarkdownToHtml(content);

  // Array to store email attachments
  const attachments: Array<{ filename: string; content: Buffer; cid: string; contentDisposition: 'inline' }> = [];

  // Generate featured artworks section with resized embedded images
  let artworksSection = '';
  if (featuredArtworks.length > 0) {
    const artworkItems = await Promise.all(
      featuredArtworks.map(async (artwork, index) => {
        let imageHtml = '';
        if (artwork.imageUrl) {
          const imageData = await getImageAsBuffer(artwork.imageUrl);
          if (imageData) {
            // Create a unique CID for this image
            const cid = `artwork-${index}@belagallery.com`;

            // Add to attachments array
            attachments.push({
              filename: `${artwork.title.replace(/[^a-zA-Z0-9]/g, '-')}.jpg`,
              content: imageData.buffer,
              cid: cid,
              contentDisposition: 'inline'
            });

            imageHtml = `<img src="cid:${cid}" alt="${artwork.title}" style="max-width: 100%; width: 300px; height: auto; border-radius: 5px; margin-bottom: 10px; display: block; border: 0;" border="0">`;
          }
        }

        return `
          <div class="artwork-item">
            ${imageHtml}
            <h4 style="margin: 10px 0 5px 0; color: #007bff;">${artwork.title}</h4>
            ${artwork.description ? `<p style="margin: 5px 0;">${artwork.description}</p>` : ''}
            ${artwork.galleryUrl ? `<p><a href="${getFullUrl(artwork.galleryUrl)}" style="color: #007bff; text-decoration: none; font-weight: bold;">View Full Artwork in Gallery →</a></p>` : ''}
          </div>
        `;
      })
    );

    artworksSection = `
      <div class="section">
        <h3>🖼️ Featured Artworks</h3>
        ${artworkItems.join('')}
      </div>
    `;
  }

  // Generate events section
  const eventsSection = upcomingEvents.length > 0 ? `
    <div class="section">
      <h3>🎭 Upcoming Events</h3>
      ${upcomingEvents.map(event => `
        <div class="event-item">
          <h4 style="margin: 15px 0 5px 0; color: #007bff;">${event.title}</h4>
          <p style="margin: 5px 0; font-weight: bold; color: #666;">📅 ${event.date}</p>
          ${event.location ? `<p style="margin: 5px 0; color: #666;">📍 ${event.location}</p>` : ''}
          ${event.description ? `<p style="margin: 10px 0;">${event.description}</p>` : ''}
        </div>
      `).join('')}
    </div>
  ` : '';

  const finalHtml = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${subject}</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
            .container { max-width: 600px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9; }
            .header { text-align: center; margin-bottom: 30px; }
            .header h1 { color: #007bff; margin: 0; }
            .content { background-color: #ffffff; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
            .section { margin: 30px 0; padding: 20px; background-color: #f8f9fa; border-radius: 5px; border-left: 4px solid #007bff; }
            .artwork-item, .event-item { margin: 20px 0; padding: 15px; background-color: #ffffff; border-radius: 5px; border: 1px solid #e9ecef; }
            .footer { margin-top: 20px; font-size: 0.9em; color: #777; text-align: center; }
            .unsubscribe { font-size: 0.8em; color: #999; margin-top: 20px; }
            .signature { margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef; font-style: italic; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎨 ${subject}</h1>
            </div>
            <div class="content">
                <h2>Hello ${name}!</h2>

                <div style="margin: 20px 0; line-height: 1.8;">
                    ${htmlContent.split('\n').map(line => {
                      const trimmed = line.trim();
                      if (!trimmed) return '';
                      // Don't wrap list elements or already wrapped HTML in paragraphs
                      if (trimmed.startsWith('<ul>') || trimmed.startsWith('<li>') || trimmed.startsWith('</ul>') || trimmed.includes('<strong>') || trimmed.includes('<em>')) {
                        return trimmed;
                      }
                      return `<p>${trimmed}</p>`;
                    }).filter(Boolean).join('')}
                </div>

                ${artworksSection}

                ${eventsSection}

                <div class="signature">
                    <p>With warm regards,<br>
                    <strong>${authorName}</strong></p>
                </div>
            </div>
            <div class="footer">
                <p>&copy; ${new Date().getFullYear()} Bela Gallery. All rights reserved.</p>
                <div class="unsubscribe">
                    <p>You're receiving this email because you subscribed to our newsletter. If you no longer wish to receive these emails, you can <a href="${unsubscribeUrl}" style="color: #007bff; text-decoration: none;">unsubscribe at any time</a>.</p>
                </div>
            </div>
        </div>
    </body>
    </html>
  `;

  return { html: finalHtml, attachments };
}