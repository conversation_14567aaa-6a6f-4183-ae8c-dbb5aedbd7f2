import { describe, it, expect, vi, beforeEach } from "vitest";
import { cn, formatDate, truncateText, slugify, debounce } from "./utils";

describe("utils", () => {
  describe("cn", () => {
    it("combines class names correctly", () => {
      expect(cn("class1", "class2")).toBe("class1 class2");
      expect(cn("p-4", "m-2")).toBe("p-4 m-2");
    });

    it("handles conditional classes", () => {
      const condition = true;
      expect(cn("base", condition && "active")).toBe("base active");
      expect(cn("base", !condition && "inactive")).toBe("base");
    });

    it("merges tailwind classes properly", () => {
      expect(cn("p-2 m-2", "p-4")).toBe("m-2 p-4");
      expect(cn("text-red-500", "text-blue-500")).toBe("text-blue-500");
    });

    it("handles undefined and null values", () => {
      expect(cn("base", undefined, null, "active")).toBe("base active");
    });
  });

  describe("formatDate", () => {
    beforeEach(() => {
      // Mock Intl.DateTimeFormat to ensure consistent output
      vi.spyOn(Intl, "DateTimeFormat").mockImplementation(() => ({
        format: (date: Date) => {
          return `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}`;
        },
      } as any));
    });

    it("formats a valid date string", () => {
      expect(formatDate("2023-01-15")).toBe("1/15/2023");
    });

    it("returns empty string for empty input", () => {
      expect(formatDate("")).toBe("");
    });

    it("returns original string for invalid date", () => {
      expect(formatDate("not-a-date")).toBe("not-a-date");
    });
  });

  describe("truncateText", () => {
    it("truncates text longer than maxLength", () => {
      expect(truncateText("This is a long text", 10)).toBe("This is a ...");
    });

    it("doesn't truncate text shorter than maxLength", () => {
      expect(truncateText("Short", 10)).toBe("Short");
    });

    it("returns empty string for empty input", () => {
      expect(truncateText("", 10)).toBe("");
    });

    it("handles null or undefined input", () => {
      expect(truncateText(null as any, 10)).toBe(null);
      expect(truncateText(undefined as any, 10)).toBe(undefined);
    });
  });

  describe("slugify", () => {
    it("converts text to lowercase", () => {
      expect(slugify("UPPERCASE")).toBe("uppercase");
    });

    it("replaces non-alphanumeric characters with hyphens", () => {
      expect(slugify("Hello World!")).toBe("hello-world");
    });

    it("removes leading and trailing hyphens", () => {
      expect(slugify("!Hello World!")).toBe("hello-world");
    });

    it("handles multiple spaces and special characters", () => {
      expect(slugify("This is a   test with $pecial ch@racters")).toBe(
        "this-is-a-test-with-pecial-ch-racters"
      );
    });
  });

  describe("debounce", () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it("delays function execution", () => {
      const func = vi.fn();
      const debouncedFunc = debounce(func, 1000);

      debouncedFunc();
      expect(func).not.toHaveBeenCalled();

      vi.advanceTimersByTime(500);
      expect(func).not.toHaveBeenCalled();

      vi.advanceTimersByTime(500);
      expect(func).toHaveBeenCalledTimes(1);
    });

    it("cancels previous timeout on subsequent calls", () => {
      const func = vi.fn();
      const debouncedFunc = debounce(func, 1000);

      debouncedFunc();
      vi.advanceTimersByTime(500);

      debouncedFunc();
      vi.advanceTimersByTime(500);
      expect(func).not.toHaveBeenCalled();

      vi.advanceTimersByTime(500);
      expect(func).toHaveBeenCalledTimes(1);
    });

    it("passes arguments to the debounced function", () => {
      const func = vi.fn();
      const debouncedFunc = debounce(func, 1000);

      debouncedFunc("arg1", "arg2");
      vi.advanceTimersByTime(1000);

      expect(func).toHaveBeenCalledWith("arg1", "arg2");
    });
  });
});