import { describe, it, expect, vi, beforeEach } from "vitest";
import { renderHook, act } from "@testing-library/react";
import { useImageResolver } from "./use-image-resolver";
import { getPublicImageUrl } from "@/lib/image-paths";
import { logError } from "@/lib/errorLogger";

// Mock dependencies
vi.mock("@/lib/image-paths", () => ({
  getPublicImageUrl: vi.fn(),
}));

vi.mock("@/lib/errorLogger", () => ({
  logError: vi.fn(),
}));

describe("useImageResolver", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should initialize with empty resolved URLs", () => {
    const { result } = renderHook(() => useImageResolver());

    expect(result.current.resolvedUrls).toEqual({});
    expect(result.current.isResolving).toBe(false);
  });

  it("should resolve images successfully", async () => {
    const mockUrls = {
      "image1.jpg": "https://storage.googleapis.com/image1.jpg",
      "image2.jpg": "https://storage.googleapis.com/image2.jpg",
    };

    (getPublicImageUrl as any)
      .mockResolvedValueOnce(mockUrls["image1.jpg"])
      .mockResolvedValueOnce(mockUrls["image2.jpg"]);

    const { result } = renderHook(() => useImageResolver());

    await act(async () => {
      await result.current.resolveImages(["image1.jpg", "image2.jpg"]);
    });

    expect(result.current.resolvedUrls).toEqual(mockUrls);
    expect(result.current.isResolving).toBe(false);
    expect(getPublicImageUrl).toHaveBeenCalledTimes(2);
  });

  it("should handle duplicate paths", async () => {
    const mockUrl = "https://storage.googleapis.com/image1.jpg";
    (getPublicImageUrl as any).mockResolvedValue(mockUrl);

    const { result } = renderHook(() => useImageResolver());

    await act(async () => {
      await result.current.resolveImages(["image1.jpg", "image1.jpg"]);
    });

    expect(result.current.resolvedUrls).toEqual({ "image1.jpg": mockUrl });
    expect(getPublicImageUrl).toHaveBeenCalledTimes(1);
  });

  it("should handle empty paths array", async () => {
    const { result } = renderHook(() => useImageResolver());

    await act(async () => {
      await result.current.resolveImages([]);
    });

    expect(result.current.resolvedUrls).toEqual({});
    expect(getPublicImageUrl).not.toHaveBeenCalled();
  });

  it("should handle individual image resolution errors", async () => {
    const error = new Error("Failed to resolve image");
    (getPublicImageUrl as any)
      .mockResolvedValueOnce("https://storage.googleapis.com/image1.jpg")
      .mockRejectedValueOnce(error);

    const { result } = renderHook(() => useImageResolver());

    await act(async () => {
      await result.current.resolveImages(["image1.jpg", "image2.jpg"]);
    });

    expect(result.current.resolvedUrls).toEqual({
      "image1.jpg": "https://storage.googleapis.com/image1.jpg",
      "image2.jpg": null,
    });
    expect(logError).toHaveBeenCalledWith(
      error,
      "useImageResolver.resolveImages",
      expect.anything()
    );
  });

  it("should handle getResolvedUrl helper function", async () => {
    const mockUrl = "https://storage.googleapis.com/image1.jpg";
    (getPublicImageUrl as any).mockResolvedValue(mockUrl);

    const { result } = renderHook(() => useImageResolver());

    await act(async () => {
      await result.current.resolveImages(["image1.jpg"]);
    });

    expect(result.current.getResolvedUrl("image1.jpg")).toBe(mockUrl);
    expect(result.current.getResolvedUrl("unresolved.jpg")).toBeUndefined();
  });

  it("should handle overall resolution error", async () => {
    const error = new Error("Failed to resolve images");
    // This test setup implies that Promise.all itself would reject,
    // or that the entire map operation fails, which is less likely with individual try/catches.
    // The current implementation of resolveImages catches errors within the map,
    // so an "overall" error in the sense of the Promise.all rejecting is avoided.
    // Instead, individual errors are logged and paths are set to null.
    // Let's adjust the mock to reflect individual failures for consistency with the hook's logic.
    (getPublicImageUrl as any).mockImplementation(async (path: string) => {
      if (path === "image1.jpg" || path === "image2.jpg") {
        throw error; // Simulate failure for these specific paths
      }
      return `https://storage.googleapis.com/${path}`; // For any other paths if needed
    });


    const { result } = renderHook(() => useImageResolver());

    await act(async () => {
      await result.current.resolveImages(["image1.jpg", "image2.jpg"]);
    });

    // With individual errors caught, paths should be null
    expect(result.current.resolvedUrls).toEqual({
      "image1.jpg": null,
      "image2.jpg": null,
    });
    // logError should be called for each failed resolution attempt within the map
    expect(logError).toHaveBeenCalledTimes(2);
    expect(logError).toHaveBeenCalledWith(
      error,
      "useImageResolver.resolveImages",
      expect.anything()
    );
  });
});
