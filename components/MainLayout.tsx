"use client";

import {ReactNode} from 'react';
import {Header} from './header';
import {Footer} from './footer';
import {Toaster} from './ui/toaster';
import {PreviewProvider} from './PreviewProvider';

interface MainLayoutProps {
    children: ReactNode;
}

export default function MainLayout({children}: MainLayoutProps) {
    return (
        <PreviewProvider>
            <Header/>
            <main className="min-h-screen py-8 pt-16">
                {children}
            </main>
            <Footer/>
            <Toaster/>
        </PreviewProvider>
    );
}
