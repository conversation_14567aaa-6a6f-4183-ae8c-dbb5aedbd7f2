import * as functions from "firebase-functions";
import * as nodemailer from "nodemailer";

// Email sending interface
interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  from?: string;
}

/**
 * Sends an email using nodemailer
 */
export async function sendEmail(options: EmailOptions): Promise<void> {
  const { to, subject, html, from } = options;
  
  // Get email configuration from environment variables
  const emailUser = process.env.EMAIL_USER;
  const emailPass = process.env.EMAIL_PASS;
  const emailHost = process.env.EMAIL_HOST || "smtp.gmail.com";
  const emailPort = parseInt(process.env.EMAIL_PORT || "587", 10);
  const defaultFrom = process.env.EMAIL_FROM || "<EMAIL>";
  
  if (!emailUser || !emailPass) {
    throw new Error("Email configuration missing. Set EMAIL_USER and EMAIL_PASS environment variables.");
  }
  
  // Create transporter
  const transporter = nodemailer.createTransport({
    host: emailHost,
    port: emailPort,
    secure: emailPort === 465,
    auth: {
      user: emailUser,
      pass: emailPass,
    },
  });
  
  // Send email
  try {
    const info = await transporter.sendMail({
      from: from || defaultFrom,
      to,
      subject,
      html,
    });
    
    functions.logger.info(`Email sent: ${info.messageId}`);
  } catch (error) {
    functions.logger.error("Error sending email:", error);
    throw error;
  }
}

/**
 * Creates HTML for subscription confirmation email
 */
export function createConfirmationEmailHtml(name: string, confirmationUrl: string): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2>Confirm Your Subscription</h2>
      <p>Hello ${name},</p>
      <p>Thank you for subscribing to Bela Gallery updates. Please confirm your subscription by clicking the button below:</p>
      <p style="text-align: center; margin: 30px 0;">
        <a href="${confirmationUrl}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; display: inline-block;">
          Confirm Subscription
        </a>
      </p>
      <p>If you didn't request this subscription, you can safely ignore this email.</p>
      <p>Best regards,<br>Bela Gallery Team</p>
    </div>
  `;
}

/**
 * Creates HTML for welcome email after confirmation
 */
export function createWelcomeEmailHtml(name: string, unsubscribeUrl: string): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2>Welcome to Bela Gallery!</h2>
      <p>Hello ${name},</p>
      <p>Thank you for confirming your subscription. You'll now receive updates about new exhibitions, events, and gallery news.</p>
      <p>We're excited to have you join our community!</p>
      <p>Best regards,<br>Bela Gallery Team</p>
      <p style="font-size: 12px; color: #666; margin-top: 30px;">
        If you wish to unsubscribe at any time, <a href="${unsubscribeUrl}">click here</a>.
      </p>
    </div>
  `;
}