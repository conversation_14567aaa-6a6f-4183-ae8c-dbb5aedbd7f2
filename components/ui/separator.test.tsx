import React from "react";
import { describe, it, expect, vi } from "vitest";
import { render, screen } from "@testing-library/react";
import { Separator } from "./separator";

// Mock the Radix UI Separator component
vi.mock("@radix-ui/react-separator", () => ({
  Root: ({ className, orientation, decorative, ...props }) => (
    <div
      data-testid="separator"
      data-orientation={orientation}
      data-decorative={decorative}
      className={className}
      {...props}
    />
  ),
}));

describe("Separator", () => {
  it("renders with default props", () => {
    render(<Separator />);
    const separator = screen.getByTestId("separator");
    expect(separator).toBeInTheDocument();
    expect(separator).toHaveAttribute("data-orientation", "horizontal");
    expect(separator).toHaveAttribute("data-decorative", "true");
    expect(separator).toHaveClass("shrink-0");
    expect(separator).toHaveClass("bg-border");
    expect(separator).toHaveClass("h-[1px]");
    expect(separator).toHaveClass("w-full");
  });

  it("renders with vertical orientation", () => {
    render(<Separator orientation="vertical" />);
    const separator = screen.getByTestId("separator");
    expect(separator).toHaveAttribute("data-orientation", "vertical");
    expect(separator).toHaveClass("h-full");
    expect(separator).toHaveClass("w-[1px]");
    expect(separator).not.toHaveClass("h-[1px]");
    expect(separator).not.toHaveClass("w-full");
  });

  it("renders with non-decorative attribute", () => {
    render(<Separator decorative={false} />);
    const separator = screen.getByTestId("separator");
    expect(separator).toHaveAttribute("data-decorative", "false");
  });

  it("applies custom className", () => {
    render(<Separator className="custom-class" />);
    const separator = screen.getByTestId("separator");
    expect(separator).toHaveClass("custom-class");
  });

  it("forwards ref correctly", () => {
    const ref = React.createRef<HTMLDivElement>();
    render(<Separator ref={ref} />);
    expect(ref.current).not.toBeNull();
    expect(ref.current).toBe(screen.getByTestId("separator"));
  });

  it("passes additional props to the separator element", () => {
    render(<Separator aria-label="Separator Label" data-custom="custom-value" />);
    const separator = screen.getByTestId("separator");
    expect(separator).toHaveAttribute("aria-label", "Separator Label");
    expect(separator).toHaveAttribute("data-custom", "custom-value");
  });
});