import Link from 'next/link';

export default function NotFound() {
  return (
    <div className="max-w-5xl mx-auto px-4 py-16 text-center">
      <h1 className="text-4xl md:text-5xl font-cormorant font-light text-elegant-text mb-4 tracking-wide">
        Page Not Found
      </h1>
      <div className="flex justify-center items-center mb-6">
        <div className="w-16 h-px bg-elegant-accent mx-2"></div>
        <span className="font-montserrat text-xs uppercase tracking-widest text-elegant-text/70">404 Error</span>
        <div className="w-16 h-px bg-elegant-accent mx-2"></div>
      </div>
      <p className="text-center font-montserrat text-elegant-text/80 mb-8 text-lg max-w-2xl mx-auto">
        The page you are looking for does not exist or has been moved.
      </p>
      <Link 
        href="/" 
        className="inline-block px-6 py-3 bg-elegant-accent text-white font-montserrat text-sm uppercase tracking-wider hover:bg-elegant-accent/90 transition-colors"
      >
        Return to Home
      </Link>
    </div>
  );
}