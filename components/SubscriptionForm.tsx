"use client";

import React, { useState, FormEvent } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast'; // Assuming a toast system is in place

export function SubscriptionForm() {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast(); // Using the existing toast component

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsLoading(true);

    if (!name.trim()) {
      toast({
        message: "Name is required.",
        variant: "error",
      });
      setIsLoading(false);
      return;
    }

    if (!email) {
      toast({
        message: "Email address is required.",
        variant: "error",
      });
      setIsLoading(false);
      return;
    }

    try {
      // Use Firebase function instead of Next.js API route
      const functionUrl = process.env.NODE_ENV === 'production'
        ? 'https://us-central1-belagallery-9e01b.cloudfunctions.net/subscribe'
        : 'http://127.0.0.1:5001/belagallery-9e01b/us-central1/subscribe';

      const response = await fetch(functionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: name.trim(), email }),
      });

      const data = await response.json();

      if (response.ok) {
        toast({
          message: data.message || "Please check your email to confirm your subscription.",
          variant: "success",
        });
        setName(''); // Clear name field on success
        setEmail(''); // Clear email field on success
      } else {
        toast({
          message: data.error || "An unknown error occurred.",
          variant: "error",
        });
      }
    } catch (error) {
      console.error("Subscription form error:", error);
      toast({
        message: "An error occurred while subscribing. Please try again later.",
        variant: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="flex flex-col gap-3 items-center max-w-md mx-auto">
      <div className="flex flex-col sm:flex-row gap-2 w-full">
        <Input
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Your name"
          disabled={isLoading}
          className="flex-grow"
          aria-label="Your name"
        />
        <Input
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="Your email address"
          disabled={isLoading}
          className="flex-grow"
          aria-label="Email address for newsletter"
        />
      </div>
      <Button type="submit" disabled={isLoading} className="w-full sm:w-auto">
        {isLoading ? 'Subscribing...' : 'Subscribe'}
      </Button>
      {/* {message && <p className="mt-2 text-sm text-gray-600">{message}</p>} */}
      {/* Toast notifications are preferred over the inline message variable now */}
    </form>
  );
}
