import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { usePublicContent } from './use-public-content';
import { fetchContent } from '@/lib/content-repository';
import { logError } from '@/lib/errorLogger';

// Mock dependencies
vi.mock('@/lib/firebase', () => ({
  db: {},
  auth: {},
  storage: {}
}));

vi.mock('@/lib/content-repository', () => ({
  fetchContent: vi.fn()
}));

vi.mock('@/lib/errorLogger', () => ({
  logError: vi.fn()
}));

describe('usePublicContent', () => {
  const defaultValue = { title: 'Default Title' };
  
  beforeEach(() => {
    vi.clearAllMocks();
  });
  
  it('should load content from live collection in normal mode', async () => {
    // Setup
    const mockData = { title: 'Live Content' };
    (fetchContent as any).mockResolvedValue(mockData);
    
    // Execute
    const { result } = renderHook(() => usePublicContent({
      collection: 'pages',
      documentId: 'about',
      defaultValue,
      previewMode: false
    }));
    
    // Initial state
    expect(result.current.loading).toBe(true);
    expect(result.current.content).toEqual(defaultValue);
    
    // Wait for content to load
    await waitFor(() => expect(result.current.loading).toBe(false));
    
    // Verify
    expect(fetchContent).toHaveBeenCalledWith(
      'live',
      'about',
      expect.objectContaining({
        defaultValue,
        fallbackCollection: undefined
      })
    );
    expect(result.current.content).toEqual(mockData);
    expect(result.current.error).toBeNull();
  });
  
  it('should try preview first in preview mode', async () => {
    // Setup
    const mockData = { title: 'Preview Content' };
    (fetchContent as any).mockResolvedValue(mockData);
    
    // Execute
    const { result } = renderHook(() => usePublicContent({
      collection: 'pages',
      documentId: 'about',
      defaultValue,
      previewMode: true
    }));
    
    // Wait for content to load
    await waitFor(() => expect(result.current.loading).toBe(false));
    
    // Verify
    expect(fetchContent).toHaveBeenCalledWith(
      'preview',
      'about',
      expect.objectContaining({
        defaultValue,
        fallbackCollection: 'live',
        fallbackDocId: 'about'
      })
    );
    expect(result.current.content).toEqual(mockData);
  });
  
  it('should apply transform function if provided', async () => {
    // Setup
    const mockData = { title: 'Raw Title' };
    const transformedData = { title: 'Transformed: Raw Title' };
    (fetchContent as any).mockResolvedValue(transformedData);
    
    const transform = (data: unknown) => {
      const typed = data as { title: string };
      return { title: `Transformed: ${typed.title}` };
    };
    
    // Execute
    const { result } = renderHook(() => usePublicContent({
      collection: 'pages',
      documentId: 'about',
      defaultValue,
      transformData: transform
    }));
    
    // Wait for content to load
    await waitFor(() => expect(result.current.loading).toBe(false));
    
    // Verify
    expect(fetchContent).toHaveBeenCalledWith(
      'live',
      'about',
      expect.objectContaining({
        transformData: transform
      })
    );
    expect(result.current.content).toEqual(transformedData);
  });
  
  it('should handle errors', async () => {
    // Setup
    const error = new Error('Failed to fetch');
    (fetchContent as any).mockRejectedValue(error);
    
    // Execute
    const { result } = renderHook(() => usePublicContent({
      collection: 'pages',
      documentId: 'about',
      defaultValue
    }));
    
    // Wait for error to be set
    await waitFor(() => expect(result.current.loading).toBe(false));
    
    // Verify
    expect(result.current.error).toEqual(error);
    expect(logError).toHaveBeenCalledWith(
      error,
      'usePublicContent.loadContent',
      expect.anything()
    );
    expect(result.current.content).toEqual(defaultValue);
  });
});
