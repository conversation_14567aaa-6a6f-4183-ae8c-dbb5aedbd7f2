import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { categorizeEvents, mapEventsToSlideshowImages } from '@/lib/eventsHelpers';
import { Event } from '@/lib/types';

describe('categorizeEvents', () => {
  // Mock the Date object
  let mockDate: Date;
  
  beforeEach(() => {
    // Set a fixed date for testing: May 15, 2023
    mockDate = new Date(2023, 4, 15);
    vi.useFakeTimers();
    vi.setSystemTime(mockDate);
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should categorize past events correctly', () => {
    const pastEvents: Event[] = [
      {
        id: 1,
        title: 'Past Event 1',
        date: 'March 22-16, 2014',
        location: 'Test Location',
        description: 'Test Description',
        images: { main: 'test.jpg' }
      },
      {
        id: 2,
        title: 'Past Event 2',
        date: 'January 10, 2023',
        location: 'Test Location',
        description: 'Test Description',
        images: { main: 'test.jpg' }
      }
    ];

    const categorized = categorizeEvents(pastEvents);
    
    expect(categorized[0]._status).toBe('past');
    expect(categorized[1]._status).toBe('past');
  });

  it('should categorize ongoing events correctly', () => {
    const ongoingEvents: Event[] = [
      {
        id: 3,
        title: 'Ongoing Event',
        date: 'May 10-20, 2023',
        location: 'Test Location',
        description: 'Test Description',
        images: { main: 'test.jpg' }
      }
    ];

    const categorized = categorizeEvents(ongoingEvents);
    
    expect(categorized[0]._status).toBe('ongoing');
  });

  it('should categorize upcoming events correctly', () => {
    const upcomingEvents: Event[] = [
      {
        id: 4,
        title: 'Upcoming Event 1',
        date: 'June 10-20, 2023',
        location: 'Test Location',
        description: 'Test Description',
        images: { main: 'test.jpg' }
      },
      {
        id: 5,
        title: 'Upcoming Event 2',
        date: 'May 20-30, 2023',
        location: 'Test Location',
        description: 'Test Description',
        images: { main: 'test.jpg' }
      }
    ];

    const categorized = categorizeEvents(upcomingEvents);
    
    expect(categorized[0]._status).toBe('upcoming');
    expect(categorized[1]._status).toBe('upcoming');
  });

  it('should handle invalid date formats gracefully', () => {
    const invalidEvents: Event[] = [
      {
        id: 6,
        title: 'Invalid Date Event',
        date: 'Not a date',
        location: 'Test Location',
        description: 'Test Description',
        images: { main: 'test.jpg' }
      }
    ];

    const categorized = categorizeEvents(invalidEvents);
    
    // Default to past for invalid dates
    expect(categorized[0]._status).toBe('past');
  });

  it('should categorize events with invalid day ranges as past', () => {
    const invalidDayRangeEvent: Event[] = [
      {
        id: 7,
        title: 'Invalid Day Range Event',
        date: 'May 30-10, 2023', // Invalid range: end day is before start day
        location: 'Test Location',
        description: 'Test Description',
        images: { main: 'test.jpg' }
      }
    ];

    const categorized = categorizeEvents(invalidDayRangeEvent);

    // Should be categorized as 'past'
    expect(categorized[0]._status).toBe('past');
  });

  describe('single date categorization', () => {
    it('should categorize a past single date event as past', () => {
      const events: Event[] = [{ id: 8, title: 'Past Single Date', date: 'January 10, 2023', location: 'L', description: 'D', images: { main: 'i.jpg' } }];
      const categorized = categorizeEvents(events);
      expect(categorized[0]._status).toBe('past');
    });

    it('should categorize a current single date event as ongoing', () => {
      const events: Event[] = [{ id: 9, title: 'Ongoing Single Date', date: 'May 15, 2023', location: 'L', description: 'D', images: { main: 'i.jpg' } }];
      const categorized = categorizeEvents(events);
      expect(categorized[0]._status).toBe('ongoing');
    });

    it('should categorize a future single date event as upcoming', () => {
      const events: Event[] = [{ id: 10, title: 'Upcoming Single Date', date: 'June 10, 2023', location: 'L', description: 'D', images: { main: 'i.jpg' } }];
      const categorized = categorizeEvents(events);
      expect(categorized[0]._status).toBe('upcoming');
    });

    it('should categorize a single date event in a past year as past', () => {
      const events: Event[] = [{ id: 11, title: 'Past Year Single Date', date: 'December 10, 2022', location: 'L', description: 'D', images: { main: 'i.jpg' } }];
      const categorized = categorizeEvents(events);
      expect(categorized[0]._status).toBe('past');
    });

    it('should categorize a single date event in a future year as upcoming', () => {
      const events: Event[] = [{ id: 12, title: 'Future Year Single Date', date: 'January 10, 2024', location: 'L', description: 'D', images: { main: 'i.jpg' } }];
      const categorized = categorizeEvents(events);
      expect(categorized[0]._status).toBe('upcoming');
    });
  });
});

describe('categorizeEvents month parsing robustness', () => {
  // Ensure mock date is May 15, 2023 for these tests too
  let mockDateForMonthTests: Date;
  beforeEach(() => {
    mockDateForMonthTests = new Date(2023, 4, 15);
    vi.useFakeTimers();
    vi.setSystemTime(mockDateForMonthTests);
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  // Mock date is May 15, 2023
  it('should correctly parse full month names for upcoming events', () => {
    const events: Event[] = [{ id: 1, title: 'T', date: 'June 01-10, 2023', l: 'L', d: 'D', images: { main: 'i.jpg' } }];
    expect(categorizeEvents(events)[0]._status).toBe('upcoming');
  });

  it('should correctly parse abbreviated month names for upcoming events', () => {
    const events: Event[] = [
      { id: 1, title: 'T', date: 'Sep 01-10, 2023', l: 'L', d: 'D', images: { main: 'i.jpg' } },
      { id: 2, title: 'T', date: 'NOV 01-10, 2023', l: 'L', d: 'D', images: { main: 'i.jpg' } },
    ];
    expect(categorizeEvents(events)[0]._status).toBe('upcoming');
    expect(categorizeEvents(events)[1]._status).toBe('upcoming');
  });

  it('should correctly parse mixed case month names for upcoming events', () => {
    const events: Event[] = [
      { id: 1, title: 'T', date: 'july 01-10, 2023', l: 'L', d: 'D', images: { main: 'i.jpg' } },
      { id: 2, title: 'T', date: 'AUGust 01-10, 2023', l: 'L', d: 'D', images: { main: 'i.jpg' } },
    ];
    expect(categorizeEvents(events)[0]._status).toBe('upcoming');
    expect(categorizeEvents(events)[1]._status).toBe('upcoming');
  });

  it('should correctly parse full month names for ongoing events', () => {
    const events: Event[] = [{ id: 1, title: 'T', date: 'May 01-20, 2023', l: 'L', d: 'D', images: { main: 'i.jpg' } }];
    expect(categorizeEvents(events)[0]._status).toBe('ongoing');
  });

  it('should correctly parse abbreviated month names for ongoing events (using May)', () => {
    // No common abbreviation for May in the list other than 'may' itself
    const events: Event[] = [{ id: 1, title: 'T', date: 'May 01-20, 2023', l: 'L', d: 'D', images: { main: 'i.jpg' } }];
    expect(categorizeEvents(events)[0]._status).toBe('ongoing');
  });

  it('should correctly parse mixed case month names for ongoing events', () => {
    const events: Event[] = [
      { id: 1, title: 'T', date: 'may 01-20, 2023', l: 'L', d: 'D', images: { main: 'i.jpg' } },
      { id: 2, title: 'T', date: 'MAY 01-20, 2023', l: 'L', d: 'D', images: { main: 'i.jpg' } },
    ];
    expect(categorizeEvents(events)[0]._status).toBe('ongoing');
    expect(categorizeEvents(events)[1]._status).toBe('ongoing');
  });

  it('should correctly parse full month names for past events', () => {
    const events: Event[] = [{ id: 1, title: 'T', date: 'April 01-10, 2023', l: 'L', d: 'D', images: { main: 'i.jpg' } }];
    expect(categorizeEvents(events)[0]._status).toBe('past');
  });

  it('should correctly parse abbreviated month names for past events', () => {
    const events: Event[] = [
      { id: 1, title: 'T', date: 'Jan 01-10, 2023', l: 'L', d: 'D', images: { main: 'i.jpg' } },
      { id: 2, title: 'T', date: 'MAR 01-10, 2023', l: 'L', d: 'D', images: { main: 'i.jpg' } },
    ];
    expect(categorizeEvents(events)[0]._status).toBe('past');
    expect(categorizeEvents(events)[1]._status).toBe('past');
  });

  it('should correctly parse mixed case month names for past events', () => {
    const events: Event[] = [
      { id: 1, title: 'T', date: 'february 01-10, 2023', l: 'L', d: 'D', images: { main: 'i.jpg' } },
      { id: 2, title: 'T', date: 'APr 01-10, 2023', l: 'L', d: 'D', images: { main: 'i.jpg' } },
    ];
    expect(categorizeEvents(events)[0]._status).toBe('past');
    expect(categorizeEvents(events)[1]._status).toBe('past');
  });
});

describe('mapEventsToSlideshowImages', () => {
  it('should correctly extract year from "Month Day-Day, Year" format and include details image', () => {
    const events: Event[] = [{
      id: 1, title: 'Event Range Date', date: 'March 16-22, 2014', location: 'Location 1', description: 'Desc 1',
      images: { main: 'main1.jpg', details: 'details1.jpg' }
    }];
    const expectedYear = '2014';
    const slideshowImages = mapEventsToSlideshowImages(events);
    expect(slideshowImages).toHaveLength(2);
    expect(slideshowImages[0]).toEqual({ title: 'Event Range Date', image: 'main1.jpg', description: 'March 16-22, 2014 at Location 1', year: expectedYear, size: '' });
    expect(slideshowImages[1]).toEqual({ title: 'Event Range Date - Details', image: 'details1.jpg', description: 'March 16-22, 2014 at Location 1', year: expectedYear, size: '' });
  });

  it('should correctly extract year from "Month Day, Year" format and handle no details image', () => {
    const events: Event[] = [{
      id: 2, title: 'Event Single Date', date: 'January 10, 2023', location: 'Location 2', description: 'Desc 2',
      images: { main: 'main2.jpg' }
    }];
    const expectedYear = '2023';
    const slideshowImages = mapEventsToSlideshowImages(events);
    expect(slideshowImages).toHaveLength(1);
    expect(slideshowImages[0]).toEqual({ title: 'Event Single Date', image: 'main2.jpg', description: 'January 10, 2023 at Location 2', year: expectedYear, size: '' });
  });

  it('should correctly extract year for "May 10, 2023"', () => {
    const events: Event[] = [{
      id: 3, title: 'Event May 10', date: 'May 10, 2023', location: 'Location 3', description: 'Desc 3',
      images: { main: 'main3.jpg' }
    }];
    const expectedYear = '2023';
    const slideshowImages = mapEventsToSlideshowImages(events);
    expect(slideshowImages[0].year).toBe(expectedYear);
  });

  it('should correctly extract year for "May 1-2, 2024"', () => {
    const events: Event[] = [{
      id: 4, title: 'Event May 1-2', date: 'May 1-2, 2024', location: 'Location 4', description: 'Desc 4',
      images: { main: 'main4.jpg', details: 'details4.jpg' }
    }];
    const expectedYear = '2024';
    const slideshowImages = mapEventsToSlideshowImages(events);
    expect(slideshowImages[0].year).toBe(expectedYear);
    expect(slideshowImages[1].year).toBe(expectedYear);
  });
});