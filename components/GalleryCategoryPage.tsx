"use client";

import { useState, useEffect, useMemo } from "react";
import ImageSlideshow from "@/components/ImageSlideshow";
import EditButton from "@/components/EditButton";
import { ArtworkDisplay } from "@/components/artwork/ArtworkDisplay"; // + Import ArtworkDisplay
import usePreview from "@/lib/usePreview";
import { usePublicContent } from "@/hooks/use-public-content";
import { useImageResolver } from "@/hooks/use-image-resolver";
import type { GalleryContent } from "@/types/gallery";
import { useAdminAuth } from "@/lib/useAdminAuth";

const DEFAULT_GALLERY_CONTENT: GalleryContent = {
  categories: [],
  slugs: [],
};

interface GalleryCategoryPageProps {
  slug: string;
}

export default function GalleryCategoryPage({
  slug,
}: GalleryCategoryPageProps) {
  const isPreviewMode = usePreview();
  const { user } = useAdminAuth();
  const [selectedIndex, setSelectedIndex] = useState<number>(-1);
  const [isSlideShowOpen, setIsSlideShowOpen] = useState<boolean>(false);

  // Use the new hook for content loading
  const { content, loading, error } = usePublicContent<GalleryContent>({
    collection: "live",
    documentId: "gallery",
    defaultValue: DEFAULT_GALLERY_CONTENT,
    previewMode: isPreviewMode,
  });

  // Find the category and its artworks
  const found = useMemo(() => {
    const slugsArr = Array.isArray(content?.slugs) ? content.slugs : [];
    return slugsArr.find((s) => s.slug === slug);
  }, [content?.slugs, slug]);
  const artworks = useMemo(() => found?.artworks || [], [found]);

  // Use the image resolver hook for artwork images
  const { resolvedUrls: imageUrls, resolveImages } = useImageResolver();

  // Resolve images when content changes
  useEffect(() => {
    if (artworks.length > 0) {
      const imagePaths = artworks
        .map((artwork) => artwork.image)
        .filter(Boolean);
      resolveImages(imagePaths);
    }
  }, [artworks, resolveImages]);

  const handleImageClick = (index: number) => {
    if (index >= 0 && index < artworks.length) {
      setSelectedIndex(index);
      setIsSlideShowOpen(true);
    }
  };

  const handleClose = () => {
    setIsSlideShowOpen(false);
    setSelectedIndex(-1);
  };

  const title =
    slug
      .split("-")
      .map((w) => w[0].toUpperCase() + w.slice(1))
      .join(" ") + " Gallery";

  // Artworks for the slideshow should use the resolved URLs
  const artworksForSlideshow = artworks
    .filter((artwork) => artwork.image && imageUrls[artwork.image]) // Only include resolved images
    .map((artwork) => ({
      ...artwork,
      image: imageUrls[artwork.image], // Use resolved URL
    }));

  if (loading)
    return (
      <div className="container mx-auto px-4 py-8 text-center text-elegant-text/60 font-montserrat">
        Loading gallery content...
      </div>
    );

  if (error)
    return (
      <div className="container mx-auto px-4 py-8 text-center text-elegant-text/60 font-montserrat">
        Error loading gallery content
      </div>
    );

  if (!found || artworks.length === 0)
    return (
      <div className="container mx-auto px-4 py-8 text-center text-elegant-text/60 font-montserrat">
        Gallery category not found
      </div>
    );

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-2">
        <h1 className="text-3xl font-bold mb-8">{title}</h1>
        <div className="flex items-center justify-end">
          <EditButton path={`/admin/gallery/${slug}`} hidden={isPreviewMode || !user} />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {artworks.map((artwork, index) => (
          <div
            key={artwork.id || index} // Assuming artwork might have an id
            className="cursor-pointer" // Keep cursor pointer for clickability
            onClick={() => handleImageClick(index)}
          >
            <ArtworkDisplay
              artwork={artwork}
              mode="standard"
              resolvedUrls={imageUrls}
              className="bg-white rounded-lg shadow-lg overflow-hidden transition-transform hover:scale-[1.02]"
            />
          </div>
        ))}
      </div>
      <ImageSlideshow
        artworks={artworksForSlideshow}
        currentIndex={selectedIndex}
        isOpen={isSlideShowOpen}
        onClose={handleClose}
        showThumbnails={true}
        enableZoom={true}
      />
    </div>
  );
}
