import { describe, it, expect, vi } from "vitest";
import { renderHook, act } from "@testing-library/react";
import { useFormValidation } from "./use-form-validation";

interface TestForm {
  name: string;
  email: string;
  age: number;
}

describe("useFormValidation", () => {
  const validationRules = {
    name: [
      {
        validate: (value: string) => value.length >= 2,
        message: "Name must be at least 2 characters",
      },
    ],
    email: [
      {
        validate: (value: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
        message: "Invalid email format",
      },
    ],
    age: [
      {
        validate: (value: number) => value >= 18,
        message: "Must be at least 18 years old",
      },
    ],
  };

  it("should initialize with no errors", () => {
    const { result } = renderHook(() =>
      useFormValidation<TestForm>(validationRules)
    );

    expect(result.current.errors).toEqual({});
  });

  it("should validate a single field successfully", () => {
    const { result } = renderHook(() =>
      useFormValidation<TestForm>(validationRules)
    );

    act(() => {
      const isValid = result.current.validateField("name", "John");
      expect(isValid).toBe(true);
    });

    expect(result.current.errors).toEqual({});
  });

  it("should show error for invalid field", () => {
    const { result } = renderHook(() =>
      useFormValidation<TestForm>(validationRules)
    );

    act(() => {
      const isValid = result.current.validateField("name", "J");
      expect(isValid).toBe(false);
    });

    expect(result.current.errors).toEqual({
      name: "Name must be at least 2 characters",
    });
  });

  it("should validate entire form successfully", () => {
    const { result } = renderHook(() =>
      useFormValidation<TestForm>(validationRules)
    );

    act(() => {
      const isValid = result.current.validateForm({
        name: "John",
        email: "<EMAIL>",
        age: 25,
      });
      expect(isValid).toBe(true);
    });

    expect(result.current.errors).toEqual({});
  });

  it("should show multiple errors for invalid form", () => {
    const { result } = renderHook(() =>
      useFormValidation<TestForm>(validationRules)
    );

    act(() => {
      const isValid = result.current.validateForm({
        name: "J",
        email: "invalid-email",
        age: 16,
      });
      expect(isValid).toBe(false);
    });

    expect(result.current.errors).toEqual({
      name: "Name must be at least 2 characters",
      email: "Invalid email format",
      age: "Must be at least 18 years old",
    });
  });

  it("should clear all errors", () => {
    const { result } = renderHook(() =>
      useFormValidation<TestForm>(validationRules)
    );

    // First create some errors
    act(() => {
      result.current.validateForm({
        name: "J",
        email: "invalid-email",
        age: 16,
      });
    });

    // Then clear them
    act(() => {
      result.current.clearErrors();
    });

    expect(result.current.errors).toEqual({});
  });

  it("should clear specific field error", () => {
    const { result } = renderHook(() =>
      useFormValidation<TestForm>(validationRules)
    );

    // First create some errors
    act(() => {
      result.current.validateForm({
        name: "J",
        email: "invalid-email",
        age: 16,
      });
    });

    // Then clear just the name error
    act(() => {
      result.current.clearFieldError("name");
    });

    expect(result.current.errors).toEqual({
      email: "Invalid email format",
      age: "Must be at least 18 years old",
    });
  });

  it("should handle fields without validation rules", () => {
    const { result } = renderHook(() =>
      useFormValidation<TestForm>(validationRules)
    );

    act(() => {
      const isValid = result.current.validateField("name" as any, "John");
      expect(isValid).toBe(true);
    });

    expect(result.current.errors).toEqual({});
  });

  it("should handle multiple validation rules per field", () => {
    const complexRules = {
      password: [
        {
          validate: (value: string) => value.length >= 8,
          message: "Password must be at least 8 characters",
        },
        {
          validate: (value: string) => /[A-Z]/.test(value),
          message: "Password must contain an uppercase letter",
        },
      ],
    };

    const { result } = renderHook(() =>
      useFormValidation<{ password: string }>(complexRules)
    );

    act(() => {
      const isValid = result.current.validateField("password", "short");
      expect(isValid).toBe(false);
    });

    expect(result.current.errors).toEqual({
      password: "Password must be at least 8 characters",
    });
  });
});
