{"name": "belagallery", "version": "0.1.0", "type": "commonjs", "private": true, "scriptsComment": {"Development": {"dev": "Start Next.js development server", "start": "Start Next.js production server"}, "Build & Deploy": {"prebuild": "Generate gallery slugs before build", "build": "Build Next.js application", "serve": "Serve dynamic build locally", "build:static": "Build static version with data exp", "serve:static": "Serve static build locally", "build:static:app": "Build Next.js app for static exp", "cleanup:admin": "Remove admin files from static build", "exp:content": "Exp Firestore data and images for static build", "exp:data": "Exp Firestore collections to JSON", "exp:images": "Exp Firebase Storage images", "deploy:firebase": "Deploy to Firebase hosting"}, "Content Management": {"migrate": "Migrate content to seed firebase"}, "Firebase Emulators": {"em:stop": "Stop all Firebase emulators (both prod and test ports)", "em:stop:app": "Stop Firebase emulators for production config", "em:stop:test": "Stop Firebase emulators for test config"}, "Code Quality": {"lint": "Run ESLint", "knip": "Find unused files and dependencies", "putout": "Run code transformations"}, "Testing": {"test": "<PERSON><PERSON> to run unit tests", "test:all": "Run all tests with cov (parallel unit, shared emulator int)", "test:unit": "Run unit tests with cov", "test:unit:w": "Run unit tests in w mode", "test:e2e": "Run end-to-end tests", "test:e2e:ui": "Run e2e tests with UI", "test:int": "Run int tests with Firebase emulators", "test:int:no-em": "Run int tests without starting emulators", "test:int:dbg": "Run int tests in dbg mode", "test:int:headed": "Run int tests in headed mode", "test:int:ui": "Run int tests with UI", "test:int:w": "Run int tests in w mode", "test:functions:unit": "Run Firebase Functions unit tests", "test:functions:int": "Run Firebase Functions int tests", "test:functions:int:no-em": "Run Firebase Functions int tests without starting emulators"}, "Coverage": {"cov:clean": "Clean cov reports", "cov:merge": "Merge cov reports from different test suites", "cov:report": "Generate combined cov report"}}, "scripts": {"dev": "next dev", "start": "next start", "prebuild": "tsx scripts/generate-gallery-slugs.ts", "build": "next build", "serve": "http-server out-dynamic -p 3000", "build:static": "cross-env NODE_ENV=production npm run build:static:app && npm run cleanup:admin && npm run exp:content", "serve:static": "http-server out -p 3000", "migrate": "tsx scripts/migrate-content.ts", "deploy:firebase": "firebase deploy --only hosting", "em:stop": "tsx scripts/stop-emulator.ts", "em:stop:app": "tsx scripts/stop-emulator.ts firebase.json", "em:stop:test": "tsx scripts/stop-emulator.ts firebase.test.json", "build:static:app": "NEXT_PUBLIC_STATIC_BUILD=true env-cmd -f .env.static next build", "cleanup:admin": "rm -rf out/admin out/admin.html", "exp:content": "firebase emulators:exec --only firestore,storage --project belagallery-9e01b --import=./functions/firebase-data --ui \"npm run exp:data -- --output out && npm run exp:images -- --output out\"", "exp:data": "tsx scripts/export-firestore-to-json.ts", "exp:images": "tsx scripts/export-firebase-images.ts", "lint": "next lint", "knip": "knip", "putout": "putout .", "test": "npm run test:unit", "test:all": "npm run cov:clean && npm run em:stop && npm-run-all --parallel test:unit test:functions:unit && firebase emulators:exec --only firestore,storage --project belagallery-test --config firebase.test.json \"npm-run-all --parallel test:int:no-em test:functions:int:no-em && npm run cov:report\"", "test:unit": "vitest run --config test/vitest.config.ts --coverage ${TEST_ARGS:-}", "test:unit:w": "npm run test:unit -- --watch", "test:e2e": "playwright test --config test/playwright.config.ts --project=e2e ${TEST_ARGS:-}", "test:e2e:ui": "npm run test:e2e -- --ui", "test:int": "firebase emulators:exec --only firestore,storage --project belagallery-test --config firebase.test.json \"playwright test --config test/playwright.config.ts --project=integration ${TEST_ARGS:-}\"", "test:int:no-em": "playwright test --config test/playwright.config.ts --project=integration ${TEST_ARGS:-}", "test:int:dbg": "npm run test:int -- --debug", "test:int:headed": "npm run test:int -- --headed", "test:int:ui": "npm run test:int -- --ui", "test:int:w": "npm run test:int -- --watch", "test:functions:unit": "cd functions && npm run test:unit", "test:functions:int": "cd functions && npm run test:int", "test:functions:int:no-em": "cd functions && npm run test:int:no-em", "cov:clean": "rm -rf test/output/coverage", "cov:merge": "mkdir -p test/output/coverage/merged && (nyc merge test/output/coverage/unit test/output/coverage/merged/unit.json 2>/dev/null || echo '{}' > test/output/coverage/merged/unit.json) && (nyc merge test/output/coverage/playwright test/output/coverage/merged/playwright.json 2>/dev/null || echo '{}' > test/output/coverage/merged/playwright.json) && (nyc merge test/output/coverage/functions-unit test/output/coverage/merged/functions-unit.json 2>/dev/null || echo '{}' > test/output/coverage/merged/functions-unit.json) && (nyc merge test/output/coverage/functions-integration test/output/coverage/merged/functions-integration.json 2>/dev/null || echo '{}' > test/output/coverage/merged/functions-integration.json) && nyc merge test/output/coverage/merged test/output/coverage/merged/final.json", "cov:report": "npm run cov:merge && nyc report --reporter=html --reporter=text --temp-dir=test/output/coverage/merged --report-dir=test/output/coverage/report"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "firebase": "^11.7.1", "firebase-admin": "^13.4.0", "glob": "^11.0.2", "lucide-react": "^0.454.0", "next": "15.1.0", "react": "^19", "react-dom": "^19", "tailwind-merge": "^2.5.5", "try-to-catch": "^3.0.1"}, "nyc": {"exclude": ["**/*.test.{ts,tsx}", "**/*.spec.{ts,tsx}", "test/**/*", "functions/**/*"], "include": ["app/**/*.{ts,tsx}", "components/**/*.{ts,tsx}", "lib/**/*.{ts,tsx}", "hooks/**/*.{ts,tsx}"], "extension": [".ts", ".tsx"], "reporter": ["text", "html"], "all": true}, "devDependencies": {"@eslint/eslintrc": "^3.1.0", "@eslint/js": "^9.23.0", "@playwright/test": "^1.42.1", "@putout/plugin-remove-unused-variables": "^12.1.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^15.0.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@types/try-to-catch": "^3.0.3", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "@vitest/coverage-v8": "^1.2.0", "cross-env": "^7.0.3", "dotenv-cli": "^7.4.2", "env-cmd": "^10.1.0", "eslint": "9.23.0", "eslint-config-next": "15.2.4", "eslint-plugin-unused-imports": "^4.1.4", "firebase-tools": "^14.3.1", "http-server": "^14.1.1", "jsdom": "^24.0.0", "knip": "^5.55.1", "npm-run-all": "^4.1.5", "nyc": "^17.1.0", "postcss": "^8", "putout": "^40.0.26", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "^5.8.3", "typescript-eslint": "^8.32.0", "v8-to-istanbul": "^9.3.0", "vitest": "^1.2.0"}}