"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { usePublicContent } from "@/hooks/use-public-content";
import EditButton from "@/components/EditButton";
import PageHeader from "@/components/PageHeader";
import { ContactContent } from "@/types/contact";
import usePreview from "@/lib/usePreview";
import { useAdminAuth } from "@/lib/useAdminAuth";

const DEFAULT_CONTACT_CONTENT: ContactContent = {
  header: "",
  sectionTitle: "",
  description: "",
};

export default function Contact() {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  });

  const isPreviewMode = usePreview();
  const { user } = useAdminAuth();

  // Use the new hook for content loading
  const { content, loading } = usePublicContent<ContactContent>({
    collection: "contact",
    documentId: "contact",
    defaultValue: DEFAULT_CONTACT_CONTENT,
    previewMode: isPreviewMode,
  });

  const { header, sectionTitle, description } = content;

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 py-16 text-center">
        Loading...
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 py-16">
      <PageHeader
        title={header || "Contact"}
        subtitle="Get in Touch"
        description="Send me a message and I'll get back to you soon"
      />
      <div className="flex items-center justify-end mb-2 -mt-12">
        <EditButton path="/admin/contact" hidden={isPreviewMode || !user} />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-12 md:gap-20">
        <div className="space-y-6 bg-white elegant-card p-8">
          <h3 className="text-2xl font-cormorant font-light text-elegant-text">
            {sectionTitle}
          </h3>
          <p className="font-montserrat text-elegant-text/80">{description}</p>
        </div>
        <div className="bg-white elegant-card p-8">
          <form
            className="space-y-6"
            onSubmit={async (e) => {
              e.preventDefault();
              setIsSubmitting(true);

              try {
                if (!process.env.NEXT_PUBLIC_FORMSPREE_URL)
                  throw Error("Formspree URL is not configured");

                const response = await fetch(
                  process.env.NEXT_PUBLIC_FORMSPREE_URL,
                  {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json",
                    },
                    body: JSON.stringify(formData),
                  }
                );

                if (!response.ok) throw Error("Failed to send message");

                toast({
                  title: "Message Sent",
                  description:
                    "Thank you for your message. I will get back to you soon.",
                });

                // Reset form
                setFormData({
                  name: "",
                  email: "",
                  subject: "",
                  message: "",
                });
              } catch {
                toast({
                  title: "Error",
                  description:
                    "Failed to send message. Please try again or contact me directly via email.",
                  variant: "destructive",
                });
              } finally {
                setIsSubmitting(false);
              }
            }}
          >
            <div className="space-y-2">
              <label
                htmlFor="name"
                className="block text-sm font-montserrat text-elegant-text/90"
              >
                Name *
              </label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    name: e.target.value,
                  }))
                }
                required
                className="border-elegant focus:ring-1 focus:ring-elegant-accent focus:border-elegant-accent transition-colors font-montserrat"
              />
            </div>
            <div className="space-y-2">
              <label
                htmlFor="email"
                className="block text-sm font-montserrat text-elegant-text/90"
              >
                Email *
              </label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    email: e.target.value,
                  }))
                }
                required
                className="border-elegant focus:ring-1 focus:ring-elegant-accent focus:border-elegant-accent transition-colors font-montserrat"
              />
            </div>
            <div className="space-y-2">
              <label
                htmlFor="subject"
                className="block text-sm font-montserrat text-elegant-text/90"
              >
                Subject *
              </label>
              <Input
                id="subject"
                name="subject"
                value={formData.subject}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    subject: e.target.value,
                  }))
                }
                required
                className="border-elegant focus:ring-1 focus:ring-elegant-accent focus:border-elegant-accent transition-colors font-montserrat"
              />
            </div>
            <div className="space-y-2">
              <label
                htmlFor="message"
                className="block text-sm font-montserrat text-elegant-text/90"
              >
                Message *
              </label>
              <Textarea
                id="message"
                name="message"
                rows={5}
                value={formData.message}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    message: e.target.value,
                  }))
                }
                required
                className="border-elegant focus:ring-1 focus:ring-elegant-accent focus:border-elegant-accent transition-colors font-montserrat"
              />
            </div>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-elegant-accent hover:bg-elegant-accent/90 text-white font-montserrat mt-4"
            >
              {isSubmitting ? (
                <span className="flex items-center">
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Sending...
                </span>
              ) : (
                "Send Message"
              )}
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
}
