export interface ContactContent {
    header: string;
    sectionTitle: string;
    description: string;
}

export interface HomeContent {
    heroTitle: string;
    heroText: string;
    ctaText: string;
    ctaLink: string;
}

export interface Exhibition {
    year: string;
    details: string;
}

// About page
export interface EducationItem {
    heading: string;
    description: string;
}

export interface AboutContent {
    statement: string[];
    education: EducationItem[];
    exhibitions: Record<string, Exhibition[]>;
    awards: Exhibition[];
    collections: string[];
}

export type Event = {
    id: number;
    title: string;
    date: string;
    location: string;
    description: string;
    images: {
        main: string;
        details?: string;
    };
};

export interface EventsContent {
    events: Event[];
}

export interface GalleryCategory {
    title: string;
    slug: string;
    image: string;
}

export interface Artwork {
    title: string;
    image: string;
    description: string;
    year: string;
    size: string;
}

export interface GalleryContent {
    categories: GalleryCategory[];
}

// Gallery admin
export interface ArtworksMap {
    [slug: string]: Artwork[];
}

export interface AdminArtworksEditorProps {
    slug: string;
}

export interface MenuItem {
    label: string;
    href: string;
}

// Menu content for admin
export interface MenuContent {
    items: MenuItem[];
}