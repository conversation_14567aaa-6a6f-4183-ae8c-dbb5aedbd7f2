import React from "react";
import { describe, it, expect, vi, afterEach, beforeEach } from "vitest";
import { renderHook, act } from "@testing-library/react";
import { render, screen, cleanup } from "@testing-library/react";
import { useToast } from "./use-toast";

// Mock the Toast component
vi.mock("@/components/ui/toast", () => ({
  Toast: vi.fn((props) => {
    // Set default values for variant and duration
    const { message, variant = "default", duration = 3000, onClose } = props;
    return (
      <div data-testid="toast" data-variant={variant} data-duration={duration}>
        {message}
        <button onClick={onClose}>Close</button>
      </div>
    );
  }),
}));

describe("useToast", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    cleanup();
  });

  it("should initialize with no toast", () => {
    const { result } = renderHook(() => useToast());
    expect(result.current.toast).toBeNull();
  });

  it("should show toast with default options", async () => {
    const { result } = renderHook(() => useToast());

    await act(async () => {
      result.current.showToast("Test message");
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    expect(result.current.toast).not.toBeNull();
    render(result.current.toast!);
    const toast = screen.getByTestId("toast");
    expect(toast).toHaveTextContent("Test message");
    expect(toast.getAttribute("data-variant")).toBe("default");
    expect(toast.getAttribute("data-duration")).toBe("3000");
  });

  it("should show toast with custom options", async () => {
    const { result } = renderHook(() => useToast());

    await act(async () => {
      result.current.showToast("Test message", {
        variant: "destructive",
        duration: 5000,
      });
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    render(result.current.toast!);
    const toast = screen.getByTestId("toast");
    expect(toast).toHaveTextContent("Test message");
    expect(toast.getAttribute("data-variant")).toBe("destructive");
    expect(toast.getAttribute("data-duration")).toBe("5000");
  });

  it("should hide toast", async () => {
    const { result } = renderHook(() => useToast());

    // First show a toast
    await act(async () => {
      result.current.showToast("Test message");
      await new Promise((resolve) => setTimeout(resolve, 0));
    });
    expect(result.current.toast).not.toBeNull();

    // Then hide it
    await act(async () => {
      result.current.hideToast();
      await new Promise((resolve) => setTimeout(resolve, 0));
    });
    expect(result.current.toast).toBeNull();
  });

  it("should handle multiple show/hide cycles", async () => {
    const { result, rerender } = renderHook(() => useToast());

    // First toast
    await act(async () => {
      result.current.showToast("First message");
      await new Promise((resolve) => setTimeout(resolve, 0));
    });
    if (result.current.toast) {
      const { getByTestId } = render(result.current.toast);
      expect(getByTestId("toast")).toHaveTextContent("First message");
    }

    // Hide
    await act(async () => {
      result.current.hideToast();
      await new Promise((resolve) => setTimeout(resolve, 0));
      rerender();
    });
    expect(result.current.toast).toBeNull();

    // Second toast
    await act(async () => {
      result.current.showToast("Second message");
      await new Promise((resolve) => setTimeout(resolve, 0));
    });
    if (result.current.toast) {
      cleanup(); // Clean up previous renders
      const { getByTestId } = render(result.current.toast);
      expect(getByTestId("toast")).toHaveTextContent("Second message");
    }
  });

  it("should handle toast onClose callback", async () => {
    const { result } = renderHook(() => useToast());

    await act(async () => {
      result.current.showToast("Test message");
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    render(result.current.toast!);
    const closeBtn = screen.getByText("Close");
    await act(async () => {
      closeBtn.click();
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    expect(result.current.toast).toBeNull();
  });

  it("should preserve toast options when showing multiple toasts", async () => {
    const { result, rerender } = renderHook(() => useToast());

    // Show first toast with custom options
    await act(async () => {
      result.current.showToast("First message", {
        variant: "destructive",
        duration: 5000,
      });
      await new Promise((resolve) => setTimeout(resolve, 0));
    });
    if (result.current.toast) {
      const { getByTestId } = render(result.current.toast);
      let toast = getByTestId("toast");
      expect(toast).toHaveTextContent("First message");
      expect(toast.getAttribute("data-variant")).toBe("destructive");
      expect(toast.getAttribute("data-duration")).toBe("5000");
    }

    // Hide it
    await act(async () => {
      result.current.hideToast();
      await new Promise((resolve) => setTimeout(resolve, 0));
      rerender();
    });
    expect(result.current.toast).toBeNull();

    // Show second toast with different options
    await act(async () => {
      result.current.showToast("Second message", {
        variant: "default",
        duration: 3000,
      });
      await new Promise((resolve) => setTimeout(resolve, 0));
    });
    if (result.current.toast) {
      cleanup(); // Clean up previous renders
      const { getByTestId } = render(result.current.toast);
      let toast = getByTestId("toast");
      expect(toast).toHaveTextContent("Second message");
      expect(toast.getAttribute("data-variant")).toBe("default");
      expect(toast.getAttribute("data-duration")).toBe("3000");
    }
  });
});
