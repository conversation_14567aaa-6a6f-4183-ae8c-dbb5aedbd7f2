const isError = (a: unknown): a is Error => a instanceof Error;

/**
 * Standardized error logging utility for consistent error handling across the application
 */
// Log levels for different types of errors
export enum LogLevel {
  INFO = "info",
  WARNING = "warning",
  ERROR = "error",
  CRITICAL = "critical",
}

export interface ErrorLogOptions {
  context?: Record<string, unknown>;
  level?: LogLevel;
  silent?: boolean;
}

/**
 * Logs an error with standardized formatting and optional context
 *
 * @param error The error object or message to log
 * @param source The source of the error (component, function, etc.)
 * @param options Additional logging options
 */
export function logError(
  error: unknown,
  source: string,
  options: ErrorLogOptions = {}
): void {
  const { level = LogLevel.ERROR, silent = false } = options;

  // Format the error message
  // Log to console with appropriate level
  if (!silent)
    switch (level) {
      case LogLevel.INFO:
        break;

      case LogLevel.WARNING:
        break;

      case LogLevel.CRITICAL:
        break;

      case LogLevel.ERROR:
      default:
    }
  // Here you could add additional error reporting like sending to a service
  // For example, sending to Firebase Analytics, Sentry, etc.
}

/**
 * Wraps an async function with standardized error handling
 *
 * @param fn The async function to wrap
 * @param source The source identifier for error logging
 * @param options Error logging options
 * @returns A wrapped function with error handling
 */
export const withErrorHandling =
  <T extends (...args: unknown[]) => unknown>(
    fn: T,
    source: string,
    options: ErrorLogOptions = {}
  ) =>
  async (...args: Parameters<T>): Promise<ReturnType<T>> => {
    try {
      return await fn(...args);
    } catch (error) {
      logError(isError(error) ? error : Error(String(error)), source, options);
      throw error;
    }
  };
