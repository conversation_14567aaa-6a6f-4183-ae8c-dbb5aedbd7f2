{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "target": "ES6", "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"]}}, "include": ["**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "out-dynamic/types/**/*.ts"], "exclude": ["functions", "node_modules", ".next", "out", "out-dynamic"]}