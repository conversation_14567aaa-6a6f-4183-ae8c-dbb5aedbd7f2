"use client";

import {useAdminContent} from '@/hooks/use-admin-content';
import {useToast} from '@/hooks/use-toast';
import {MenuItem} from '@/lib/types';
import AdminFormLayout from '@/components/admin/AdminFormLayout';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import tryToCatch from 'try-to-catch';
import {
    Trash2,
    Plus,
    MoveUp,
    MoveDown,
} from 'lucide-react';

interface MenuContent {
    items: MenuItem[];
}

// Default menu
const defaultMenuContent: MenuContent = {
    items: [{
        label: 'Home',
        href: '/',
    }, {
        label: 'About',
        href: '/about',
    }, {
        label: 'Events',
        href: '/events',
    }, {
        label: 'Gallery',
        href: '/gallery',
    }, {
        label: 'Contact',
        href: '/contact',
    }],
};

export default function AdminMenuPage() {
    const {
        content,
        setContent,
        loading,
        saving,
        isDirty,
        saveContent,
        resetContent,
    } = useAdminContent<MenuContent>({
        previewCollection: 'preview',
        previewDocId: 'menu',
        liveCollection: 'menu',
        liveDocId: 'content',
        defaultValue: defaultMenuContent,
    });
    
    const {toast} = useToast();
    
    // Update a menu item field
    const updateField = (index: number, field: keyof MenuItem, value: string) => {
        const updatedItems = content.items.slice();
        
        updatedItems[index] = {
            ...updatedItems[index],
            [field]: value,
        };
        setContent({
            ...content,
            items: updatedItems,
        });
    };
    
    // Add a new menu item
    const addItem = () => {
        setContent({
            ...content,
            items: [
                ...content.items, {
                    label: '',
                    href: '',
                },
            ],
        });
    };
    
    // Remove a menu item
    const removeItem = (index: number) => {
        setContent({
            ...content,
            items: content.items.filter((_, i) => i !== index),
        });
    };
    
    // Move a menu item up
    const moveItemUp = (index: number) => {
        if (!index)
            return;
        
        const updatedItems = content.items.slice();
        const temp = updatedItems[index];
        
        updatedItems[index] = updatedItems[index - 1];
        updatedItems[index - 1] = temp;
        
        setContent({
            ...content,
            items: updatedItems,
        });
    };
    
    // Move a menu item down
    const moveItemDown = (index: number) => {
        if (index === content.items.length - 1)
            return;
        
        const updatedItems = content.items.slice();
        const temp = updatedItems[index];
        
        updatedItems[index] = updatedItems[index + 1];
        updatedItems[index + 1] = temp;
        
        setContent({
            ...content,
            items: updatedItems,
        });
    };
    
    const handleSave = async () => {
        await tryToCatch(saveContent);
    };
    
    
    return (
        <AdminFormLayout
            title="Edit Navigation Menu"
            isLoading={loading}
            isSaving={saving}
            isDirty={isDirty}
            onSave={handleSave}
            onReset={resetContent}
        >
            <div className="space-y-6">
                <p className="text-gray-600">
          Manage the navigation menu items that appear in the site header.
          Drag to reorder items or use the up/down buttons.
        </p>
                <div className="space-y-4">
                    {content.items.map((item, index) => (
                        <div key={index} className="flex items-center gap-2">
                            <div className="flex flex-col">
                                <Button
                                    type="button"
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => moveItemUp(index)}
                                    disabled={index === 0}
                                    className="h-6 w-6"
                                >
                                <MoveUp className="h-4 w-4"/>
                            </Button>
                                <Button
                                    type="button"
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => moveItemDown(index)}
                                    disabled={index === content.items.length - 1}
                                    className="h-6 w-6"
                                >
                                <MoveDown className="h-4 w-4"/>
                            </Button>
                            </div>
                            <Input
                                placeholder="Label"
                                value={item.label}
                                onChange={(e) => updateField(index, 'label', e.target.value)}
                                className="flex-1"
                            />
                            <Input
                                placeholder="URL Path"
                                value={item.href}
                                onChange={(e) => updateField(index, 'href', e.target.value)}
                                className="flex-1"
                            />
                            <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                onClick={() => removeItem(index)}
                            >
                            <Trash2 className="h-4 w-4"/>
                        </Button>
                        </div>
                    ))}
                    <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={addItem}
                        className="mt-2"
                    >
                    <Plus className="h-4 w-4 mr-1"/> Add Menu Item
          </Button>
                </div>
                <div className="border rounded-md p-4 bg-gray-50">
                    <h3 className="font-medium mb-2">Menu Preview</h3>
                    <div className="flex gap-4">
                        {content.items.map((item, index) => (
                            <div key={index} className="px-3 py-2 rounded hover:bg-gray-200">
                                {item.label || `Item ${index + 1}`}
                            </div>
                        ))}
                    </div>
                </div>
            </div>
            {toast}
        </AdminFormLayout>
    );
}
