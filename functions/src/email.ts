import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import * as nodemailer from "nodemailer";

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}



// Email sending interface
interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  from?: string;
  attachments?: Array<{
    filename: string;
    content: Buffer;
    cid: string;
    contentDisposition: 'inline';
  }>;
}

/**
 * Sends an email using nodemailer
 */
export async function sendEmail(options: EmailOptions): Promise<void> {
  const { to, subject, html, from, attachments } = options;

  // Get email configuration from environment variables
  const emailUser = process.env.GMAIL_EMAIL;
  const emailPass = process.env.GMAIL_APP_PASSWORD;

  if (!emailUser || !emailPass) {
    functions.logger.warn("Email configuration missing. Logging email instead of sending.");
    functions.logger.info(`Mock email send:
      To: ${to}
      Subject: ${subject}
      HTML: ${html.substring(0, 200)}...
      Attachments: ${attachments ? attachments.length : 0}
    `);
    return;
  }

  // Create transporter
  const transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: emailUser,
      pass: emailPass,
    },
  });

  // Send email
  try {
    const info = await transporter.sendMail({
      from: from || `"Bela Gallery" <${emailUser}>`,
      to,
      subject,
      html,
      attachments: attachments || [],
    });

    functions.logger.info(`Email sent: ${info.messageId}`);
    if (attachments && attachments.length > 0) {
      functions.logger.info(`📎 Email sent with ${attachments.length} image attachments`);
    }
  } catch (error) {
    functions.logger.error("Error sending email:", error);
    throw error;
  }
}

/**
 * Creates HTML for subscription confirmation email
 */
export function createConfirmationEmailHtml(name: string, confirmationUrl: string): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2>Confirm Your Subscription</h2>
      <p>Hello ${name},</p>
      <p>Thank you for subscribing to Bela Gallery updates. Please confirm your subscription by clicking the button below:</p>
      <p style="text-align: center; margin: 30px 0;">
        <a href="${confirmationUrl}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; display: inline-block;">
          Confirm Subscription
        </a>
      </p>
      <p>If you didn't request this subscription, you can safely ignore this email.</p>
      <p>Best regards,<br>Bela Gallery Team</p>
    </div>
  `;
}

/**
 * Creates HTML for welcome email after confirmation
 */
export function createWelcomeEmailHtml(name: string, unsubscribeUrl?: string): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to Our Newsletter</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
            .container { max-width: 600px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9; }
            .header { text-align: center; margin-bottom: 30px; }
            .header h1 { color: #007bff; margin: 0; }
            .content { background-color: #ffffff; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
            .highlight { background-color: #e7f3ff; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
            .footer { margin-top: 20px; font-size: 0.9em; color: #777; text-align: center; }
            .unsubscribe { font-size: 0.8em; color: #999; margin-top: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎨 Welcome to Bela Gallery!</h1>
            </div>
            <div class="content">
                <h2>Hello ${name}!</h2>
                <p>Welcome to our community! We're thrilled to have you join our newsletter.</p>

                <div class="highlight">
                    <p><strong>What to expect:</strong></p>
                    <ul>
                        <li>🖼️ Latest artwork updates and new gallery additions</li>
                        <li>🎭 Upcoming events and exhibitions</li>
                        <li>✨ Behind-the-scenes insights from the artist</li>
                        <li>🎨 Exclusive previews of new collections</li>
                    </ul>
                </div>

                <p>Thank you for your interest in our art and for being part of our creative journey. We look forward to sharing beautiful moments and artistic discoveries with you!</p>

                <p>Stay inspired,<br>
                <strong>The Bela Gallery Team</strong></p>
            </div>
            <div class="footer">
                <p>&copy; ${new Date().getFullYear()} Bela Gallery. All rights reserved.</p>
                <div class="unsubscribe">
                    <p>You're receiving this email because you subscribed to our newsletter. If you no longer wish to receive these emails, you can <a href="${unsubscribeUrl || '#'}" style="color: #007bff; text-decoration: none;">unsubscribe at any time</a>.</p>
                </div>
            </div>
        </div>
    </body>
    </html>
  `;
}

// Newsletter data interface
interface NewsletterData {
  name: string;
  subject: string;
  content: string;
  featuredArtworks: Array<{
    title: string;
    description?: string;
    imageUrl?: string;
    galleryUrl?: string;
  }>;
  upcomingEvents: Array<{
    title: string;
    date: string;
    description?: string;
    location?: string;
  }>;
  authorName: string;
  unsubscribeUrl: string;
}

// Helper function to convert markdown-style formatting to HTML
function convertMarkdownToHtml(text: string): string {
  const html = text
    // Convert **bold** to <strong>
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // Convert *italic* to <em>
    .replace(/\*(.*?)\*/g, '<em>$1</em>');

  // Handle list items more carefully
  const lines = html.split('\n');
  const processedLines: string[] = [];
  let inList = false;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    if (line.startsWith('• ')) {
      if (!inList) {
        processedLines.push('<ul style="margin: 10px 0; padding-left: 20px;">');
        inList = true;
      }
      processedLines.push(`<li>${line.substring(2)}</li>`);
    } else {
      if (inList) {
        processedLines.push('</ul>');
        inList = false;
      }
      if (line) {
        processedLines.push(line);
      }
    }
  }

  // Close any open list
  if (inList) {
    processedLines.push('</ul>');
  }

  return processedLines.join('\n');
}

// Helper function to convert relative URL to full URL
function getFullUrl(url: string): string {
  if (!url) return '';

  // If it's already a full URL, return as is
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }

  // Convert relative URL to full URL
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
  const fullUrl = `${baseUrl}${url.startsWith('/') ? '' : '/'}${url}`;
  return fullUrl;
}

/**
 * Creates newsletter email HTML (simplified version for Firebase functions)
 */
export function createNewsletterEmailHtml(data: NewsletterData): string {
  const { name, subject, content, featuredArtworks, upcomingEvents, authorName, unsubscribeUrl } = data;

  // Convert markdown-style content to HTML
  const htmlContent = convertMarkdownToHtml(content);

  // Generate featured artworks section
  const artworksSection = featuredArtworks.length > 0 ? `
    <div class="section">
      <h3>🖼️ Featured Artworks</h3>
      ${featuredArtworks.map(artwork => `
        <div class="artwork-item">
          ${artwork.imageUrl ? `<img src="${getFullUrl(artwork.imageUrl)}" alt="${artwork.title}" style="max-width: 100%; width: 300px; height: auto; border-radius: 5px; margin-bottom: 10px; display: block; border: 0;" border="0">` : ''}
          <h4 style="margin: 10px 0 5px 0; color: #007bff;">${artwork.title}</h4>
          ${artwork.description ? `<p style="margin: 5px 0;">${artwork.description}</p>` : ''}
          ${artwork.galleryUrl ? `<p><a href="${getFullUrl(artwork.galleryUrl)}" style="color: #007bff; text-decoration: none; font-weight: bold;">View Full Artwork in Gallery →</a></p>` : ''}
        </div>
      `).join('')}
    </div>
  ` : '';

  // Generate events section
  const eventsSection = upcomingEvents.length > 0 ? `
    <div class="section">
      <h3>🎭 Upcoming Events</h3>
      ${upcomingEvents.map(event => `
        <div class="event-item">
          <h4 style="margin: 15px 0 5px 0; color: #007bff;">${event.title}</h4>
          <p style="margin: 5px 0; font-weight: bold; color: #666;">📅 ${event.date}</p>
          ${event.location ? `<p style="margin: 5px 0; color: #666;">📍 ${event.location}</p>` : ''}
          ${event.description ? `<p style="margin: 10px 0;">${event.description}</p>` : ''}
        </div>
      `).join('')}
    </div>
  ` : '';

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${subject}</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
            .container { max-width: 600px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9; }
            .header { text-align: center; margin-bottom: 30px; }
            .header h1 { color: #007bff; margin: 0; }
            .content { background-color: #ffffff; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
            .section { margin: 30px 0; padding: 20px; background-color: #f8f9fa; border-radius: 5px; border-left: 4px solid #007bff; }
            .artwork-item, .event-item { margin: 20px 0; padding: 15px; background-color: #ffffff; border-radius: 5px; border: 1px solid #e9ecef; }
            .footer { margin-top: 20px; font-size: 0.9em; color: #777; text-align: center; }
            .unsubscribe { font-size: 0.8em; color: #999; margin-top: 20px; }
            .signature { margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef; font-style: italic; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎨 ${subject}</h1>
            </div>
            <div class="content">
                <h2>Hello ${name}!</h2>

                <div style="margin: 20px 0; line-height: 1.8;">
                    ${htmlContent.split('\n').map(line => {
                      const trimmed = line.trim();
                      if (!trimmed) return '';
                      // Don't wrap list elements or already wrapped HTML in paragraphs
                      if (trimmed.startsWith('<ul>') || trimmed.startsWith('<li>') || trimmed.startsWith('</ul>') || trimmed.includes('<strong>') || trimmed.includes('<em>')) {
                        return trimmed;
                      }
                      return `<p>${trimmed}</p>`;
                    }).filter(Boolean).join('')}
                </div>

                ${artworksSection}

                ${eventsSection}

                <div class="signature">
                    <p>With warm regards,<br>
                    <strong>${authorName}</strong></p>
                </div>
            </div>
            <div class="footer">
                <p>&copy; ${new Date().getFullYear()} Bela Gallery. All rights reserved.</p>
                <div class="unsubscribe">
                    <p>You're receiving this email because you subscribed to our newsletter. If you no longer wish to receive these emails, you can <a href="${unsubscribeUrl}" style="color: #007bff; text-decoration: none;">unsubscribe at any time</a>.</p>
                </div>
            </div>
        </div>
    </body>
    </html>
  `;
}